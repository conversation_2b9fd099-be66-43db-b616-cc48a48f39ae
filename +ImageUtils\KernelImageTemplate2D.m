 function kernelImg= KernelImageTemplate2D(varargin)
    options = OptionsMap(varargin{:});  
    kernelPixdim  = options.getOption('kernelPixdim', 0.2);
    kernelLength  = options.getOption('kernelLength', 15);
    N = round(kernelLength/kernelPixdim);
    kernelImg=VolHeaderImage;
    kernelImg.x_pixdim = kernelPixdim;
    kernelImg.y_pixdim = kernelPixdim;
    kernelImg.reverseY = false; 
    kernelData = zeros(2*N+1, 2*N+1); 
    kernelImg.setData(kernelData);
    kernelImg.alignXYCenter; 
 end