function result = convnfft_faster( A, B )
% 2012-11-07 tebuck: renamed to convnfft_faster to differentiate with my computer's version of convnfft_fast.
% CONVNFFT  FFT-BASED N-dimensional convolution.
%   C = CONVNFFT(A, B) performs the N-dimensional convolution of
%   matrices A and B. Assumes that B is the kernel and returns a
%   matrix the same size as A.
%
% Class support for inputs A,B:
% float: double, single
%
% METHOD: CONVNFFT uses Fourier transform (FT) convolution theorem, i.e.
%         FT of the convolution is equal to the product of the FTs of the
%         input functions.
%         In 1-D, the complexity is O((na+nb)*log(na+nb)), where na/nb are
%         respectively the lengths of A and B.
%
% Usage recommendation:
%         In 1D, this function is faster than CONV for nA, nB > 1000.
%         In 2D, this function is faster than CONV2 for nA, nB > 20.
%         In 3D, this function is faster than CONVN for nA, nB > 5.
% 
% See also conv, conv2, convn.
% 
%   Author: <PERSON> <br<PERSON><PERSON><PERSON>@yahoo.com>
%   History:
%       Original: 21-Jun-2009
%       23-Jun-2009: correct bug when ndims(A)<ndims(B)
%       02-Sep-2009: GPU/JACKET option
%       04-Sep-2009: options structure
%       16-Sep-2009: inplace product
%       11-Oct-2012: T. Buck removed GPU and Power2Flag options and the dims argument for now so the method can use fftn/ifftn.
%       5-Nov-2012: T. Buck removed all options except signal and kernel
%       10-Nov-2012: T. Buck assumes B is smaller.
 
 
nd = max(ndims(A),ndims(B));
dims = 1:nd;
 
% % This is non-periodic:
% A_freq = fftn(padarray(A, size(A) / 2));
% B_freq = fftn(circshift(padarray(B, size(A) - size(B) / 2), size(A) - 1));
 
A_freq = fftn(padarray(A, size(B) / 2));
B_freq = fftn(circshift(padarray(B, size(A) / 2), (size(A) + size(B)) / 2 - 1));
 
% whos A_freq B_freq
 
result_freq = A_freq.*B_freq;
result = ifftn(result_freq);
% size_limits = [size(B) / 2; size(B) * 3 / 2 - 1];
% size_limits = [size(A) / 2; size(A) * 3 / 2 - 1];
size_limits = [size(B) / 2; size(A) + size(B) / 2 - 1];
result = result(size_limits(1, 1):size_limits(2, 1), size_limits(1, 2):size_limits(2, 2), size_limits(1, 3):size_limits(2, 3));