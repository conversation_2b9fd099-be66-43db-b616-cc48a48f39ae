function outroimask = MergeROIMaskImages(roimasks, varargin)
    options    = OptionsMap(varargin{:});
    outmasktype= options.getoptioni('outputmask.imagetype', 'roimask');
    outputvh   = options.getoptioni('output.VolHeader'); 
    for m=1:numel(roimasks)
        roimask = roimasks{m};
        if ischar(roimask)
            roimask = ROIMaskImage(roimask);
        end
        
        if isa(outputvh, 'VolHeader') 
            roimask.ReformCS(outputvh);
        end
        
        if m==1
            switch lower(outmasktype)
                case {'roimask8'}
                    outroimask = ROIMask8Image();
                otherwise
                    outroimask = ROIMaskImage(VolHeaderImage(roimask));
                    outroimask.imageType=outmasktype;
                    outroimask.data(:)=0;
                    %outroimask.setIntType(63);
                    outroimask.setDataType('uint64');
                    outroimask.data = cast(outroimask.data, outroimask.data_type); 
            end
        end
        outroimask.AddRoiMaskImage(roimask, options);
    end
    
    %numrois = outroimask.NumberOfRois;
    outroimask.setIntType();

    distinguishableColors = options.getoptioni_numeric('SetDistinguishableColors', 1);
    if distinguishableColors
        outroimask.SetDistinguishableColors();
    end
end