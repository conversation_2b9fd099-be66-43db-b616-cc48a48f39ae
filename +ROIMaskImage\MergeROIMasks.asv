function targetoarmask = MergeROIMasks(targetmask, oarmask, outmaskfile, varargin)
    targetoarmask = [];
    options = OptionsMap(varargin{:});
    outmasktype = options.getoptioni('outputmask.imagetype', 'roimask');
    if ischar(targetmask) 
        if exist(targetmask, 'file')
            targetmask = ROIMaskImage(targetmask); 
            targetmask.imageType= outmasktype;
        else 
            targetmask = ROIMaskImage(VolHeaderImage(oarmask)); 
            targetmask.data(:)=0; 
            targetmask.imageType= outmasktype;
        end
    end
    
    if ischar(oarmask)
        if exist(oarmask, 'file')
            oarmask = ROIMaskImage(oarmask); 
        else
            oarmask = ROIMaskImage(VolHeaderImage(targetmask)); 
            oarmask.imageType= outmasktype;
            oarmask.data(:)=0;
        end
    end
    
    try
        indexs0 = cellfun(@(x)(x.ROIIndex), targetmask.rois);
        maxtargetindex = max(indexs0);
    catch
        maxtargetindex=0; 
    end
    
    targetoarmask = ROIMaskImage(targetmask);
    %roinames = targetoarmask.ROINames;
    oarnames= oarmask.ROINames;
    rois = oarmask.rois; 
    
    for k=1:numel(oarnames)
        roi1 = rois{k}; 
        roi1.ROIIndex = maxtargetindex+k;
        oarname = oarnames{k};
        mask    = oarmask.getROIMask(oarname); 

        targetoarmask.AddRoiMask(mask, oarname, {'roi', roi1});
    end

    targetoarmask.SetDistinguishableColors();
    
    if exist('outmaskfile', 'var')
        targetoarmask.writeNiftiImage(outmaskfile); 
    end
end