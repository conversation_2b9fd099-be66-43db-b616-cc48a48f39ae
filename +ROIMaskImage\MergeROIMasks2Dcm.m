function outroimask= MergeROIMasks2Dcm(dcmvh, roimasks, dcmrsfile, varargin)
    options=OptionsMap(varargin{:});
    outroimask=ROIMaskImage.MergeROIMaskImages(roimasks, varargin{:});
    dcmvh = DcmVolHeader(dcmvh);
    maskname   = options.getoptioni('structsetname', 'AISeg');

    rsinfo = dicom.utils.dicom_rtstruct_template;
    rsinfo.StructureSetLabel = maskname; 
    rsinfo.StructureSetName  = maskname;  
    dicom.utils.ROIMaskImage2DicomRS(outroimask,  dcmvh, [], {'outputfile.rtstruct', dcmrsfile}, {'rs.dicominfo', rsinfo});
end