function T = ROIStatTable_header(rois, propnames, roinames, varargin)
    T=[];
    if ischar(rois) && exist(rois, 'file')
%         header = utils.json.readJson(rois);
%         rois = header.rois; 
        roimask = ROIMaskImage; 
        roimask.readJsonHeader(rois);
        rois = roimask.rois; 
        pixdim = [roimask.x_pixdim roimask.y_pixdim roimask.z_pixdim];
    end
    
    if isa(rois, 'ROIMaskImage')
        roimask = rois; 
        rois = roimask.rois; 
        pixdim = [roimask.x_pixdim roimask.y_pixdim roimask.z_pixdim];
    end
    if isempty(rois)
        return;
    end
    if isstruct(rois)
        rois = arrayfun(@(x)(x), rois, 'uniformoutput', false);
    end
    roinames0= cellfun(@(x)(x.ROIName), rois, 'uniformoutput', false);
    if ~exist('roinames', 'var') || isempty(roinames)
        roinames= roinames0;
    end
    if ischar(propnames)
        propnames = strsplit(propnames, '|'); 
    end
    if ischar(roinames)
        roinames = strsplit(roinames, '|'); 
    end
    
%     propnames1=propnames;
%     if ismember('BBSize', propnames1)||ismember('BBCenter', propnames1)
%         propnames1 = union(propnames1, {'BoundingBox'});
%     end
    %propnames1 = setdiff(propnames1, {'BBCenter', 'BBSize', 'ROICenter', 'ROIVolume'}, 'stable'); 

    %T  = table; 
    M = numel(roinames); N = numel(propnames);
    val= cell(M, N);
    for n=1:N
        tempval =  DefaultValue(propnames{n});
        for m=1:M
           val{m, n} = tempval; 
        end
    end

    for m=1:M
        roiname = roinames{m};
        [~,loc] = ismember(roiname, roinames0);
        if loc==0
            continue;
        end

        roi=rois{loc};
        for n=1:N
            try
            statname = propnames{n};
            temp = StructBase.getfieldx_default(roi, statname, DefaultValue(statname)); temp=temp(:)';  
            if strcmpi(statname, 'BBSize')
               if isempty(temp) || any(isnan(temp))
                   bb = StructBase.getfieldx_default(roi, 'BoundingBox'); 
                   bb = bb(:)'; 
                   if numel(bb)==6
                     temp = bb(4:6).*pixdim;
                   else
                     temp = NaN(1, 3); 
                   end
               end
            end
            
            if strcmpi(statname, 'BBCenter')
               if isempty(temp) || any(isnan(temp))
                   bb = StructBase.getfieldx_default(roi, 'BoundingBox'); 
                   bb = bb(:)'; 
                   if numel(bb)==6
                      C = bb(1:3) + bb(4:6)/2;
                      dicomCoor = roimask.Image2DicomCoor(C');
                      temp = dicomCoor';
                   else
                      temp = NaN(1, 3); 
                   end
               end
            end
            
            if strcmpi(statname, 'REL_ELEC_DENSITY')||strcmpi(statname, 'ELEC_DENSITY')||strcmpi(statname, 'ROIDensity')||strcmpi(statname, 'RelElecDensity')
                if  isempty(temp) || any(isnan(temp))
                     try
                        if strcmpi(roi.('ROIPhysicalProperty'), 'REL_ELEC_DENSITY')
                            temp = roi.('ROIPhysicalPropertyValue');
                        else
                            temp=NaN;
                        end
                    catch
                        temp=NaN;
                    end
                end
            end
            
            val{m, n} = temp;
            catch
                disp(['error: ' roiname ' ' statname]);
            end
        end
        
    end
    T = cell2table(val); 
    T.Properties.VariableNames=propnames(:)';
     
%     if ismember('BBCenter', propnames)
%         C = (T.BoundingBox(:, 1:3) + T.BoundingBox(:, 4:6)/2);
%         dicomCoor = roimask.Image2DicomCoor(C');
%         T.BBCenter = dicomCoor'; 
%     end
% 
%     if ismember('BBSize', propnames)
%         M = size(T, 1); 
%         pixdim = [roimask.x_pixdim roimask.y_pixdim roimask.z_pixdim];
%         bbsize = T.BoundingBox(:, 4:6).*repmat(pixdim, [M, 1]);
%         T.BBSize = bbsize; 
%     end
            
   
    %T.ROINames = roinames(:);
    T.ROIName = roinames(:);
    %T = T(:, [N+1, 1:N]); 
    T = movevars(T, 'ROIName', 'before', 1); 
end

function val = DefaultValue(statname)
    switch lower(statname)
        case {'roivolume'}
            val = NaN;
        case {'roicenter', 'bbsize', 'bbcenter'}
            val = [NaN NaN NaN];
        case {'boundingbox'}
            val = [NaN NaN NaN NaN NaN NaN];
        otherwise
            val =[];
    end
end