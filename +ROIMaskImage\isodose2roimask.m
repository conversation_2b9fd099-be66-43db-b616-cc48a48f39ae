function roimask = isodose2roimask(dose, varargin)
    roimask = ROIMaskImage(dose);
    roimask.data(:)=0;
    options = OptionsMap(varargin{:});
    isounit = options.getoptioni('isodose.unit', '%');
    isovalues= options.getoptioni_numeric('isodose.value', [100:-10:10]);
    if strcmpi(isounit, '%')
        prescdose = options.getoptioni_numeric('prescdose');
        isodoses = isovalues*prescdose/100;
    else
        isodoses = isovalues;
    end
    for k=1:numel(isodoses)
        isodose = isodoses(k);
        mask = dose.data>isodose; 
        name = num2str([num2str(isovalues(k)) ' ' isounit]);
        roimask.AddNewROI(mask, name);
    end
    roimask.SetDistinguishableColors();
end

