function roimask = optmask2roimask(optmask)
    roimask = ROIMaskImage(optmask); 
    if ~strcmpi(optmask.imageType, 'optmask')
        return
    end
    
    numrois =  NumberOfRois(optmask); 
    %numoars =  NumberOfOARs(self);
    roimask.data(:)=0; 
    for k=1:numrois
        index = roimask.rois{k}.ROIIndex; 
        mask  = optmask.getROIMask(index);
        if index>=256
            index = floor(index/256)+32;
        end
        roimask.data = roimask.data + mask*2^(index-1);
        roimask.rois{k}.ROIIndex = index; 
    end
    roimask.imageType = 'roimask'; 
    roimask.setIntType();
end

