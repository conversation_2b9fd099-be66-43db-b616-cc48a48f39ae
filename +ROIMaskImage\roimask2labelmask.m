function optmask = roimask2labelmask(roimask, optmask, varargin)
    if ~exist('optmask', 'var')
        optmask =[];
    end
    
    options = OptionsMap({'keeproiindex', 1}, varargin{:});
    convertcontours = options.getoptioni_numeric('LabelMaskWithImageContours', 0);
    if convertcontours
        roimask = ROIMaskImage(roimask);
        ContourSmoothWindow = options.getoptioni_numeric( 'ContourSmoothWindow', 0);
        %ContourDirs = options.getoptioni(  'ContourDirs', 'zxy');
        ContourDirs = options.getoptioni(  'ContourDirs', 'z');
        roimask.Convert2ImageContours(ContourDirs, ContourSmoothWindow, varargin{:});
    end
    if ~isempty(roimask.rois)
    roivolumes = cellfun(@(x)(x.ROIVolume), roimask.rois);
    roinames   = cellfun(@(x)(x.ROIName), roimask.rois, 'UniformOutput',false);
    [roivolumes,I]=sort(roivolumes,'descend');
    roinames   = roinames(I);
    
    if isempty(optmask)
        optmask    = ROIMaskImage(); 
        optmask.imageType='labelmask';
    end
    
    optmask.AddRoiMaskImage(roimask, {'selectedroinames', roinames}, options);  
    end
    nomenstr = options.getoptioni('NomenConvert'); nomen=[];
    if ~isempty(nomenstr)
        nomen = StructBase.getfieldx(nomenstr, 'Nomenclature'); 
        if ~isempty(nomen)
            if ischar(nomen)
                nomen = ai.rtstruct.Nomenclature({'NomenclatureTable.file',nomen});
            end
        end
    end
    PlaceHoldROIs=[];
    if ~isempty(nomenstr)&&~isempty(nomen)
        PlaceHoldStructure = StructBase.getfieldx_default(nomenstr, 'PlaceHoldStandardID', 0); 
        if PlaceHoldStructure
            PlaceHoldROIs = xls.TableBase.Content2Str(nomen.ListStandardID());
        end
    end
    
    PlaceHoldROIs1 = options.getoptioni('PlaceHoldROIs');
    if isempty(PlaceHoldROIs)
        PlaceHoldROIs=PlaceHoldROIs1;
    elseif ~isempty(PlaceHoldROIs1)
        PlaceHoldROIs=[PlaceHoldROIs '|' xls.TableBase.Content2Str(PlaceHoldROIs1)];
    end
    
    if ~isempty(PlaceHoldROIs)
        if ischar(PlaceHoldROIs)
            PlaceHoldROIs = strsplit(PlaceHoldROIs, '|'); 
        end
        PlaceHoldROIs=setdiff(PlaceHoldROIs, optmask.ROINames); 
        for k=1:numel(PlaceHoldROIs)
            optmask.AddEmptyROI(PlaceHoldROIs{k});
        end
    end
    
    if ~isempty(nomenstr)&&~isempty(nomen)
        NomenProperty = StructBase.getfieldx(nomenstr, 'NomenProperty');
        ROIProperty = StructBase.getfieldx(nomenstr,  'ROIProperty');
        nomen.ConvertROIMaskImage(optmask, {'NomenProperty', NomenProperty}, {'ROIProperty', ROIProperty});
    end
    try
    ROIIndex  = cellfun(@(x)(x.ROIIndex), optmask.rois);
    [~, J]    = sort(ROIIndex);
    optmask.rois = optmask.rois(J);
    catch
        disp('warning: roimask2labelmask: result is empty');
    end
end