function optmask = roimask2perimmask(roimask, varargin)
    options = OptionsMap(varargin{:});
%     roivolumes = cellfun(@(x)(x.ROIVolume), roimask.rois);
%     roinames   = cellfun(@(x)(x.ROIName), roimask.rois, 'UniformOutput',false);
%     [roivolumes,I]=sort(roivolumes,'descend');
%     roinames   = roinames(I);
%     optmask    = ROIMaskImage(); optmask.imageType='optmask';
%     optmask.AddRoiMaskImage(roimask, {'selectedroinames', roinames}, options);     

    optmask    = ROIPerimImage(); 
    optmask.AddRoiMaskImage(roimask, options);     
end