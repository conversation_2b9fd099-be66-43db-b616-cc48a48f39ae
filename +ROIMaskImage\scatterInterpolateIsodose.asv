function dose= scatterInterpolateIsodose(isodose, method)
    %isodose = ROIMaskImage([folder 'isodose5.nii.gz']); 
    %isodose.roiview
    %dose0 = VolHeaderImage([folder 'dose_NormRx.nii.gz']);
    dose1= VolHeaderImage(VolHeader(isodose)); 
    dose1.setFloat();
    dose1.ZeroData(); 
    switch lower(method)
        case 'edge_interpolate'
            vals=unique(isodose.data(:));
            vals(vals==0)=[];
            %vals = double(unique(isodose.data(:)))+0.5;
            vals = vals-0.5;
            K = numel(vals);
            for k=1:K
                val = vals(k);
                %e   = edgeDetection3D(isodose.data>=val);
                e = edge3D(isodose.data>=val);
                dose1.data(e>0)=val; 
            end
            [X, Y, Z]=meshgrid(dose1.xData, dose1.yData, dose1.zData);
            I = dose1.data>0;
            %scattervals = dose1.data(I);
            F = scatteredInterpolant(X(I), Y(I), Z(I), double(dose1.data(I)), 'natural', 'none');
            %F = scatteredInterpolant(X(I), Y(I), Z(I), double(dose1.data(I)), 'linear', 'none');
            dosedata = F(X, Y, Z);
            dosedata(isnan(dosedata))=0; 
        case 'contour_interpolate'
            [zContours, vals] = labelmask2contours(isodose);
            [X, Y, Z]=meshgrid(1:isodose.x_dim, 1:isodose.y_dim, 1:isodose.z_dim);
            I = dose1.data>0;
            %scattervals = dose1.data(I);
            F = scatteredInterpolant(X(I), Y(I), Z(I), double(dose1.data(I)), 'natural', 'none');
            %F = scatteredInterpolant(X(I), Y(I), Z(I), double(dose1.data(I)), 'linear', 'none');
            dosedata = F(X, Y, Z);
            dosedata(isnan(dosedata))=0;  
        case 'gaussian_smooth'
    end

    dose = VolHeaderImage(VolHeader(isodose)); 
    dose.setData(dosedata);
end

function [points, dose1] = labelmask2contours(isodose)
    vals=double(unique(isodose.data(:)));
    vals(vals==0)=[];
    vals = vals-0.5;
    K = numel(vals);
    % roimask = ROIMaskImage(VolHeader(isodose));
    % roimask.imageType='roimask';
    % roimask.ZeroData(); 
    %dose1 = VolHeaderImage(VolHeader(isodose)); dose1.ZeroData();
    points = [];
    for k=1:K
        val = vals(k);
        mask = isodose.data>val; 
        %roimask.AddRoiMask(mask, ['isovalue' num2str(val)]);
        c = isodose.Mask2zContours(double(mask), 'image');
        c1 = vertcat(c{:});
        N = size(c1, 1); 
        points = cat(1, points, c1);
        dose1 = cat(1, dose1, val*ones(N, 1)); 
    end
    %roimask.toRtStruct;
end

function edges = edge3D(binaryVolume)
    % edge3D - Extract the edges of a binary 3D array.
    %
    % Input:
    %   binaryVolume - A 3D binary array (logical or numeric).
    %
    % Output:
    %   edges - A 3D binary array of the same size as binaryVolume, where
    %           the edges are marked as 1 (true) and the rest as 0 (false).

    % Ensure the input is logical (binary)
    if ~islogical(binaryVolume)
        binaryVolume = logical(binaryVolume);
    end

    % Define a 3x3x3 structuring element for erosion
    % se = strel('cube', 3); % A 3x3x3 cube structuring element
    % se.Neighborhood(1, 1, 1) = 0; % Remove the center voxel
    % se.Neighborhood(:, :, [1, 3]) = 0; % Remove all corner-connected voxels
    % se.Neighborhood(:, [1, 3], :) = 0; % Remove all edge-connected voxels
    % se.Neighborhood([1, 3], :, :) = 0; % Remove all edge-connected voxels

    se = zeros(3, 3, 3, 'logical'); % Initialize a 3x3x3 logical array
    se(:, :, 2) = [0 1 0; 1 1 1; 0 1 0]; % Middle layer (face-connected)
    se(2, 2, :) = [1; 1; 1]; % Center column (face-connected)

    % Erode the binary volume
    erodedVolume = imerode(binaryVolume, se);

    % Subtract the eroded volume from the original to get the edges
    edges = binaryVolume & ~erodedVolume;
end

