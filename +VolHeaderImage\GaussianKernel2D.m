function [out] = GaussianKernel2D(vh, sigma, normalize)
    if numel(sigma)==1
        sigma(2) = sigma;
    end
    
    vh.alignXYCenter;
    
    xdata = vh.xData;
    ydata = vh.yData;
            
    [X, Y] = meshgrid(xdata, ydata); 
    if sigma(1)>0
       KX = exp(-0.5*(X/sigma(1)).^2);
    else
       KX = zeros(size(X)); 
       KX(abs(X)<vh.x_pixdim)=1;
    end
    
    if sigma(2)>0
       KY = exp(-0.5*(Y/sigma(2)).^2);
    else
       KY = zeros(size(Y)); 
       KY(abs(Y)<vh.y_pixdim)=1;
    end
    
    kernel = KX.*KY;
    
    if normalize
        kernel = kernel/sum(kernel(:))*normalize; 
    end
    
    out = VolHeaderImage(vh);
    out.setData(kernel);
end

