function [vhct, dcmvh] = LoadDcmCT(fnames)
    if ischar(fnames)
        res = DosUtil.rdir(fnames); 
        fnames = {res(:).name};
    end
    ct   = RtCt2(fnames); 
    vhct = ct.toVhImage; 
    %vhct.imview
    vhct.setShort; 
    vhct.setLinearMeasureUnit('mm');
    if nargout>=2
        dcmvh = DcmVolHeader(vhct);
        info = dicominfo(fnames{1});
        info = RemovePrivateField(info);
        dcmvh.SetDicomInfo(info);
        dcmvh.SetSOPInstanceUIDs(ct.GetSOPInstanceUIDs);
    end
end

function info = RemovePrivateField(info)
    fn = fieldnames(info);
    I = cellfun(@(x)(~isempty(regexpi(x, 'Private_'))), fn);
    fn1 = fn(I);
    info = rmfield(info, fn1);
end