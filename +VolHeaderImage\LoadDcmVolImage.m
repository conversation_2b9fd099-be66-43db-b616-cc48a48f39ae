function [vhct, dcmvh] = LoadDcmVolImage(fnames, varargin)
    [vhct, dcmvh] = VolHeaderImage.LoadDcmVol(fnames);
    dcminfo = dcmvh.GetDicomInfo; 
    modality = dcminfo.Modality; 
    options = OptionsMap(varargin{:});
    switch upper(modality)
        case 'PT'
            tosuv = options.getoptioni('PT.tosuv', 1);
            if tosuv
                vhct.setFloat();
                dcmvh.setFloat();
                ptinfo = dcmvh.GetDicomInfo;
                weight   = StructBase.getfieldx_default(ptinfo, 'PatientWeight', 65);  %in units of kg
                activity = ptinfo.RadiopharmaceuticalInformationSequence.Item_1.RadionuclideTotalDose; %
                cf       = weight/activity*1000; 
                vhct.data = vhct.data*cf; 
            end
    end
end
