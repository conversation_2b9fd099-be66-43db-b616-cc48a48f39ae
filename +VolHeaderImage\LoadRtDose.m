function [vhrd] = LoadRtDose(fname, varargin)
    rd = RtDose2(fname); 
    vhrd = rd.toVhImage();
    vhrd.imageType ='dose';
    options = OptionsMap(varargin{:});   
    doseunits= options.getOption('dose.units', 'Gy'); 
    rdunits  = upper(rd.DoseUnits);
    if ~strcmpi(rdunits, doseunits)
        if strcmpi(rdunits,     'Gy')  && strcmpi(doseunits, 'cGy')
            vhrd.data = vhrd.data*100; 
        elseif strcmpi(rdunits, 'cGy') && strcmpi(doseunits, 'Gy')
            vhrd.data = vhrd.data/100; 
        end
    end
end