function [ res, data ] = ReadVidarData(headerfile, datafile)
    if ~exist('datafile', 'var')
        [path, name, ext] = fileparts(headerfile); 
        datafile = [path filesep name '.iv4']; 
    end
    
    a = parseTextFile(headerfile);
     
    fid = fopen(datafile, 'rb', 'ieee-be');
    data = fread(fid, [a.COLS, a.ROWS], 'ushort'); 
    data = data'; 
    fclose(fid); 
    data = double(data); 
    siz = size(data); 
    
    %     od = data(1:ceil(siz(1)/2), 1:ceil(siz(2)/2)); 

    hsiz = floor(siz/2);
    I1 = 1:hsiz(1); I2 = 1:hsiz(2); 
    
%     od = (data(I1, I2) + data(I1, I2+hsiz(2)) + data(hsiz(1)+I1, I2) + data(I1+hsiz(1), I2+hsiz(2)))/4;
%     od = (data(I1, I2) + data(I1, I2+hsiz(2)))/2; 
%     od = (data(I1, I2+hsiz(2)))/2; 
%     od = (data(hsiz(1)+I1, I2) + data(I1+hsiz(1), I2+hsiz(2)))/2;
    od1 = data(I1, I2);
    od2 = data(I1, I2+hsiz(2)); 
    od  = (od1+od2)/2;
    od_curve = od2dosecurve;
    dose = od_curve.getValue(log(od)); 
    dose(od<0) = 0; 
%     dose = data; 
    res = VolHeaderImage; 
    res.setData(dose); 
    res.x_pixdim = 2*a.PIXEL_SIZE(1);
    res.y_pixdim = 2*a.PIXEL_SIZE(2);
    res.alignXYCenter; 
end

function res = od2dosecurve
    a = [1400	1.129635e+03	0
            1300	1.256839e+03	0
            1200	1.435550e+03	0
            1100	1.603359e+03	0
            1000	1.864447e+03	0
            900	2.184300e+03	0
            800	2.603022e+03	0
            700	3.056806e+03	0
            600	3.843188e+03	0
            500	4.801469e+03	0
            400	6.303778e+03	0
            350	7.271349e+03	0
            300	8.698537e+03	0
            250	1.030342e+04	0
            200	1.259108e+04	0
            150	1.570986e+04	0
            100	2.069240e+04	0
            80	2.318621e+04	0
            60	2.617282e+04	0
            40	2.990867e+04	0
            20	3.498411e+04	0
            10	3.775397e+04	0
            5	3.953393e+04	0
            0	4.120627e+04	0];

    res = ProfileData(log(a(:, 2))', a(:, 1)');  
end