function dstimage = TransformImage(testimage, m_transform, m_fixedimage)
    fixed  = vhimage2imref(m_fixedimage);

    moving = vhimage2imref(testimage);

    if isa(m_transform, 'double')
        if numel(m_transform)==9
            m_transform = affine2d(m_transform);
        elseif numel(m_transform)==16
            m_transform = affine3d(m_transform);
        end
    end
%     
%      T - Forward transformation matrix
%  
%      T is a 4x4 floating point matrix that defines the forward
%      transformation. The matrix T uses the convention:
%  
%      [x y z 1] = [u v w 1] * T
%  
%      Where T has the form:
%  
%      [a b c 0;...
%       d e f 0;...
%       g h i 0;...
%       j k l 1];

    resdata= imwarp(testimage.data, moving, m_transform, 'OutputView',fixed);

    dstimage = VolHeaderImage(m_fixedimage); 

    dstimage.setData(resdata); 
end

function res = vhimage2imref(vhimage)
    siz = [vhimage.y_dim vhimage.x_dim vhimage.z_dim];
    pixdim = abs([vhimage.x_pixdim vhimage.y_pixdim  vhimage.z_pixdim]);
    if siz(3)>1
        res = imref3d(siz,pixdim(1),pixdim(2),pixdim(3));
        res.XWorldLimits = res.XWorldLimits + vhimage.x_start -vhimage.x_pixdim; 
        res.YWorldLimits = res.YWorldLimits + vhimage.y_start -vhimage.y_pixdim;  
        res.ZWorldLimits = res.ZWorldLimits + vhimage.z_start -vhimage.z_pixdim; 
    else
        res = imref2d(siz,pixdim(1),pixdim(2));
        res.XWorldLimits = res.XWorldLimits + vhimage.x_start -vhimage.x_pixdim; 
        res.YWorldLimits = res.YWorldLimits + vhimage.y_start -vhimage.y_pixdim; 
    end
end