function dstimage = TransformImageCoor(movingimage, fixedimage)
    A = fixedimage.GetCoordinateMatrix();
    
    B = movingimage.GetCoordinateMatrix();

    C = B*inv(A); 
    
    C(:, 4) = [0 0 0 1]'; %enforce 
    
%     dstimage = VolHeaderImage.TransformImage(movingimage, C, fixedimage); 
    [Xa, Ya, Za] = meshgrid(1:fixedimage.x_dim, 1:fixedimage.y_dim, 1:fixedimage.z_dim); 
    
    res = [Xa(:)'; Ya(:)'; Za(:)']*C; 
    
    dstimage = VolHeaderImage(fixedimage); 
    
    dstimage.data = interp3(movingimage.data, res(1, :), res(2, :), res(3, :), 'linear', 0); 
end

