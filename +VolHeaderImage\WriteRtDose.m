function WriteRtDose(A,outputfname, varargin)
    options = OptionsMap(varargin{:});
%     templatedcm = options.getoptioni('templatertdose', ...
%         'T:\Physics\Machines\Gamma Pod\01-Acceptance_Commissioning_UTSW\2019-01.14-23\MapCHECK\Matlab-Code2CheckDoseProfile\example3d.dcm');
%     templatedcm = options.getoptioni('templatertdose', 'RDT.dcm');
    templatedcm = options.getoptioni('templatertdose');
    if ischar(templatedcm) && exist(templatedcm, 'file')
        info3d = dicominfo(templatedcm);
    elseif isstruct(templatedcm)
        info3d = templatedcm;
    else
        info3d = dicom_rtdose_template;
    end
    
    overrides = options.getoptioni('metadata.override');
    if isstruct(overrides)
        names = fieldnames(overrides);
        for k=1:numel(names)
            name = names{k};
            info3d.(name) = overrides.(name);
        end
    end
    
    A.setLinearMeasureUnit('mm');
    info3d.ImagePositionPatient=[A.x_start A.y_start A.z_start]'; 
    orientation = A.ImageOrientationPatient;
    
    if isempty(orientation) 
        orientation = [1 0 0 0 1 0 0 0 1];
    end
    
    info3d.ImageOrientationPatient=orientation(1:6);
    info3d.PixelSpacing = abs([A.x_pixdim, A.y_pixdim]);
%     info3d.FrameOfReferenceUID= A.FrameOfReferenceUID; 
%     info3d.GridFrameOffsetVector=A.zData-info3d.ImagePositionPatient(3);
%     info3d.GridFrameOffsetVector=A.zData;

%     R(:, 1)= orientation(1:3)';
%     R(:, 2)= orientation(4:6)';
%     R(:, 3)= cross(R(:, 1), R(:, 2)); 
%     info3d.GridFrameOffsetVector = A.z_start + R(3, 3)*[0:(A.z_dim-1)]*abs(A.z_pixdim); 
    info3d.GridFrameOffsetVector =A.z_start+[0:(A.z_dim-1)]*abs(A.z_pixdim); 
    info3d.FrameIncrementPointer = [12292,12]; % points to GridFrameOffsetVector
%     info3d.NumberOfFrames = A.z_dim;
    
    data = A.data;
     
    scale = max(data(:)) / 65535;
    info3d.DoseGridScaling = scale;
   
 
    dim = size(data);
    M = reshape(data, [dim(1), dim(2), 1 dim(3)]);
    dicomwrite(uint16(M/scale),outputfname,info3d,'CreateMode','copy');
end

