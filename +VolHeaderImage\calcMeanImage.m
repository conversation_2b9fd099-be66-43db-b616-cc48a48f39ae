function dst = calcMeanImage(src0, ImagHandle)
    if ~exist('ImagHandle', 'var')
        ImagHandle=@VolHeaderImage; 
    end
    numImages = 0; 
    for k=1:numel(src0)
        if ischar(src0{k})
            if VolHeaderImage.existImage(src0{k})
                numImages      = numImages+1; 
                src{numImages} = ImagHandle(src0{k}); 

            end
        else
              numImages      = numImages+1; 
              src{numImages} = src0{k}; 
        end
    end

    if  numImages <=0
        dst =[]; 
        return;
    end

    dst = VolHeaderImage.calcSumImage(src, ImagHandle); 
    dst.data = dst.data/numImages; 
end