function dst = calcSumImage(src0, ImagHandle)      
    if ~exist('ImagHandle','var')
        ImagHandle = @VolHeaderImage; 
    end
    if ~iscell(src0)
        for k=1:numel(src0)
            src{k} = src0(k);
        end
        src0 = src;
    end
    
    a=cellfun(@isempty, src0); 
    src0=src0(~a);
    if isempty(src0)
        dst=[];
        return;
    end
    
    dst = ImagHandle(src0{1}); 
    for k=2:numel(src0)
        if isempty(src0{k}) 
            continue; 
        end

        try
            dst.data = dst.data+src0{k}.data; 
        catch
            img = ImagHandle(src0{k});
            if ~img.isEqualDim(dst)
                img.reform(dst); 
            end
            dst.data = dst.data+ img.data; 
        end
    end
end

