function [img, header] = load_mgh(fname, headeronly)
%https://surfer.nmr.mgh.harvard.edu/fswiki/FsTutorial/MghFormat

if(nargin < 1 || nargin > 2)
  msg = 'USAGE: [vol M] = load_mgh(fname,<headeronly>)';
  fprintf('%s',msg);
  return;
end

% unzip if it is compressed 

% if (strcmpi(fname((strlen(fname)-3):strlen(fname)), '.MGZ') | ...
% 		strcmpi(fname((strlen(fname)-3):strlen(fname)), '.GZ'))
[~, ~, ext] = fileparts(fname);
if strcmpi(ext, '.mgz') || strcmpi(ext, '.gz')
% 	gzipped =  round(rand(1)*10000000);
% 	ind = findstr(fname, '.');
% 	new_fname = sprintf('/tmp/tmp%d.mgh', gzipped);
% 	unix(sprintf('zcat %s > %s', fname, new_fname)) ;
% 	fname = new_fname ;
    gzipped = 1; 
    fname = UnZip(fname); 
else
	gzipped = -1 ;
end


if ~exist('slices', 'var') slices = []; end
if(isempty(slices)) slices = 0; end
if(slices(1) <= 0) slices = 0; end

if ~exist('frames', 'var') frames = []; end
if(isempty(frames)) frames = 0; end
if(frames(1) <= 0) frames = 0; end

if ~exist('headeronly', 'var') headeronly = 0; end
if(slices(1) > 0)
  ind = find(slices > ndim3);
  if(~isempty(ind))
    fprintf('ERROR: load_mgh: some slices exceed nslices\n');
    return;
  end
end

if(frames(1) > 0)
  ind = find(frames > nframes);
  if(~isempty(ind))
    fprintf('ERROR: load_mgh: some frames exceed nframes\n');
    return;
  end
end

fid    = fopen(fname, 'rb', 'b') ;
if(fid == -1)
  fprintf('ERROR: could not open %s for reading\n',fname);
  return;
end

% v       = fread(fid, 1, 'int') ; 
% ndim1   = fread(fid, 1, 'int') ; 
% ndim2   = fread(fid, 1, 'int') ; 
% ndim3   = fread(fid, 1, 'int') ; 
% nframes = fread(fid, 1, 'int') ;
% type    = fread(fid, 1, 'int') ; 
% dof     = fread(fid, 1, 'int') ; 

header.version = fread(fid, 1, 'int') ; 
dims    = fread(fid, 4, 'int') ; 
header.dims=dims(:)'; 
header.datatype    = fread(fid, 1, 'int') ; 
header.dof     = fread(fid, 1, 'int') ; 
header.goodRASFlag = fread(fid, 1, 'short') ;

UNUSED_SPACE_SIZE= 256;
unused_space_size = UNUSED_SPACE_SIZE-2 ;
 
if (header.goodRASFlag)
  spacing  = fread(fid, 3, 'float32') ; 
  xform    = fread(fid, 12, 'float32') ; 
  xform    = reshape(xform, 3, 4); 
  xform    = [xform; 0 0 0 1];
  header.ras_xform =xform ;
  
%   Mdc    = fread(fid, 9, 'float32') ; 
%   Mdc    = reshape(Mdc,[3 3]);
%   Pxyz_c = fread(fid, 3, 'float32') ; 

  D = diag(spacing);

  %Pcrs_c = [ndim1/2 ndim2/2 ndim3/2]'; % Should this be kept?
  Pcrs_c = header.dims(1:3)/2; Pcrs_c=Pcrs_c(:);
  Pxyz_c = xform(1:3, 4); 
  Mdc    = xform(1:3, 1:3)*D;  
  Pxyz_0 = Pxyz_c - Mdc*Pcrs_c;
  M = [Mdc Pxyz_0;  ...
	0 0 0 1];
%   ras_xform = [Mdc Pxyz_c; ...
% 	0 0 0 1];
%   orientation =xform(1:3, 1:3);   offset = xform(1:3, 4);
%   M = AffineMatrix4x4.ComposeAffineMatrix(orientation, spacing, offset);   
  USED_SPACE_SIZE = (3*4+4*3*4);  % space for ras transform
  unused_space_size = unused_space_size - USED_SPACE_SIZE ;
end

img = VolHeaderImage; img.LinearMeasureUnit='mm';
%RAS2LPS = eye(4); RAS2LPS(1, 1)=-1; RAS2LPS(2, 2)=-1;
RAS2LPS = diag([-1 -1 1 1]);
A = M*RAS2LPS;
%A = M;
img.ResetDicomAffineMatrix(A);

fseek(fid, unused_space_size, 'cof') ;
% nv = ndim1 * ndim2 * ndim3 * nframes;  
% volsz = [ndim1 ndim2 ndim3 nframes];
%volsz = header.dims;
nv = prod(header.dims); 

MRI_UCHAR =  0 ;
MRI_INT =    1 ;
MRI_LONG =   2 ;
MRI_FLOAT =  3 ;
MRI_SHORT =  4 ;
MRI_BITMAP = 5 ;

% Determine number of bytes per voxel
switch header.datatype
 case MRI_FLOAT
  nbytespervox = 4;
 case MRI_UCHAR
  nbytespervox = 1;
 case MRI_SHORT
  nbytespervox = 2;
 case MRI_INT
  nbytespervox = 4;
end

if(headeronly)
  fseek(fid,nv*nbytespervox,'cof');
  if(~feof(fid))
    [header.mr_parms count] = fread(fid,4,'float32');
    if(count ~= 4) 
      fprintf('WARNING: error reading MR params\n');
    end
  end
  fclose(fid);
  CleanUP(fname, gzipped);
  return;
end



%------------------ Read in the entire volume ----------------%
%if(slices(1) <= 0 && frames(1) <= 0)
  switch header.datatype
   case MRI_FLOAT
    vol = fread(fid, nv, 'float32') ; 
    img.setFloat();
   case MRI_UCHAR
    vol = fread(fid, nv, 'uchar') ; 
    img.setChar();
   case MRI_SHORT
    vol = fread(fid, nv, 'short') ; 
    img.setShort();
   case MRI_INT
    vol = fread(fid, nv, 'int') ; 
    img.setInt();
  end

  if(~feof(fid))
    [header.mr_parms count] = fread(fid,4,'float32');
    if(count ~= 4) 
      fprintf('WARNING: error reading MR params\n');
    end
  end
  fclose(fid) ;
  CleanUP(fname, gzipped);
  
  nread = numel(vol);
  if(nread ~= nv)
    fprintf('ERROR: tried to read %d, actually read %d\n',nv,nread);
    return;
  end
  vol = reshape(vol,header.dims); vol=permute(vol, [2 1 3 4]); 
  img.setData(vol); 
end

function [fname] = UnZip(fname)
   if isunix
        gzipped =  round(rand(1)*10000000);
        ind = findstr(fname, '.');
        new_fname = sprintf('/tmp/tmp%d.mgh', gzipped);
        unix(sprintf('zcat %s > %s', fname, new_fname)) ;
        fname = new_fname ;
   elseif ispc
       res   = gunzip(fname, tempdir);
       fname = res{1}; 
   end
end

function CleanUP(fname, gzipped)
    if(gzipped >=0) 
      if isunix
          unix(sprintf('rm %s', fname));  
      elseif ispc
          delete(fname);
      end
    end
end
