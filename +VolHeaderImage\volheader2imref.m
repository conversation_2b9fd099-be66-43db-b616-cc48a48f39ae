function res = volheader2imref(vhimage)
    siz = [vhimage.y_dim vhimage.x_dim vhimage.z_dim];
    pixdim = abs([vhimage.x_pixdim vhimage.y_pixdim  vhimage.z_pixdim]);
    if siz(3)>1
        res = imref3d(siz,pixdim(1),pixdim(2),pixdim(3));
        res.XWorldLimits = res.XWorldLimits + vhimage.x_start -vhimage.x_pixdim; 
        res.YWorldLimits = res.YWorldLimits + vhimage.y_start -vhimage.y_pixdim;  
        res.ZWorldLimits = res.ZWorldLimits + vhimage.z_start -vhimage.z_pixdim; 
    else
        res = imref2d(siz,pixdim(1),pixdim(2));
        res.XWorldLimits = res.XWorldLimits + vhimage.x_start -vhimage.x_pixdim; 
        res.YWorldLimits = res.YWorldLimits + vhimage.y_start -vhimage.y_pixdim; 
    end
end