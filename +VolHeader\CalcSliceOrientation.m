function [orientation, planeMapping] = CalcSliceOrientation(imageOrientationPatient)
    % Determine slice orientation from DICOM ImageOrientationPatient vector
    %
    % Usage:
    %   orientation = VolHeader.CalcSliceOrientation(imageOrientationPatient)
    %   [orientation, planeMapping] = VolHeader.CalcSliceOrientation(imageOrientationPatient)
    %
    % Input:
    %   imageOrientationPatient - 1x6 vector from DICOM tag (0020,0037)
    %                            Format: [rowX rowY rowZ colX colY colZ]
    %                            where row and col are direction cosines of the first row and first column
    %
    % Output:
    %   orientation - String indicating slice orientation:
    %                'AX'  - Axial (transverse)
    %                'SAG' - Sagittal
    %                'COR' - Coronal
    %                'OBL' - Oblique (if not clearly aligned with standard planes)
    %   planeMapping - Struct indicating which anatomical plane each coordinate plane represents:
    %                 .XY - Orientation of XY plane ('AX', 'SAG', 'COR', or 'OBL')
    %                 .YZ - Orientation of YZ plane ('AX', 'SAG', 'COR', or 'OBL')
    %                 .ZX - Orientation of ZX plane ('AX', 'SAG', 'COR', or 'OBL')
    %
    % Examples:
    %   % Axial orientation
    %   iop = [1 0 0 0 1 0];
    %   [orientation, planes] =VolHeader.CalcSliceOrientation(iop);
    %   % Returns: orientation = 'AX', planes.XY = 'AX', planes.YZ = 'COR', planes.ZX = 'SAG'
    %
    %   % Sagittal orientation
    %   iop = [0 1 0 0 0 -1];
    %   [orientation, planes] =VolHeader.CalcSliceOrientation(iop);
    %   % Returns: orientation = 'SAG', planes.XY = 'SAG', planes.YZ = 'COR', planes.ZX = 'AX'
    %
    %   % Coronal orientation
    %   iop = [1 0 0 0 0 -1];
    %   [orientation, planes] =VolHeader.CalcSliceOrientation(iop);
    %   % Returns: orientation = 'COR', planes.XY = 'COR', planes.YZ = 'AX', planes.ZX = 'SAG'

    % Initialize outputs
    orientation = 'UNKNOWN';
    planeMapping = struct('XY', 'UNKNOWN', 'YZ', 'UNKNOWN', 'ZX', 'UNKNOWN');

    % Validate input
    if isempty(imageOrientationPatient)
        return;
    end

    % Handle different input formats
    if iscell(imageOrientationPatient)
        if numel(imageOrientationPatient) == 1
            imageOrientationPatient = imageOrientationPatient{1};
        else
            imageOrientationPatient = cell2mat(imageOrientationPatient);
        end
    end

    if ischar(imageOrientationPatient) || isstring(imageOrientationPatient)
        % Handle string format like "1\0\0\0\1\0" or "1 0 0 0 1 0"
        iop_str = char(imageOrientationPatient);
        iop_str = strrep(iop_str, '\', ' ');
        iop_str = strrep(iop_str, ',', ' ');
        imageOrientationPatient = str2num(iop_str);
    end

    if numel(imageOrientationPatient) ~= 6
        return;
    end

    % Extract row and column direction cosines
    rowCosines = imageOrientationPatient(1:3);  % [rowX rowY rowZ]
    colCosines = imageOrientationPatient(4:6);  % [colX colY colZ]

    % Calculate slice normal vector (cross product of row and column vectors)
    sliceNormal = cross(rowCosines, colCosines);

    % Normalize the slice normal vector
    sliceNormal = sliceNormal / norm(sliceNormal);

    % Get absolute values for comparison
    absNormal = abs(sliceNormal);

    % Find the dominant direction (largest component)
    [maxComponent, dominantAxis] = max(absNormal);

    % Threshold for considering a direction as "aligned" (cos(30°) ≈ 0.866)
    % This allows for slight obliquity while still classifying as standard orientation
    alignmentThreshold = 0.866;

    % Determine orientation based on dominant axis
    if maxComponent >= alignmentThreshold
        switch dominantAxis
            case 1  % X-axis dominant → Sagittal slice orientation
                orientation = 'SAG';
            case 2  % Y-axis dominant → Coronal slice orientation
                orientation = 'COR';
            case 3  % Z-axis dominant → Axial slice orientation
                orientation = 'AX';
            otherwise
                orientation = 'OBL';
        end
    else
        % No dominant direction - oblique
        orientation = 'OBL';
    end

    % Calculate plane mapping for each coordinate plane
    % In medical imaging coordinate system:
    % - XY plane is the slice plane itself (X=row, Y=column, Z=slice normal)
    % - YZ plane is orthogonal to X-axis (sagittal reformation)
    % - ZX plane is orthogonal to Y-axis (coronal reformation)

    % XY plane: This is always the slice plane itself
    planeMapping.XY = orientation;

    % YZ plane (X=constant): What view when looking along X-axis?
    % For YZ plane, we need to determine what anatomical orientation
    % the YZ cross-section represents
    if abs(sliceNormal(1)) >= alignmentThreshold
        % Slice normal in X direction → YZ plane shows orthogonal views
        % YZ plane (looking along X) shows coronal view
        planeMapping.YZ = 'COR';
    elseif abs(sliceNormal(2)) >= alignmentThreshold
        % Slice normal in Y direction → YZ plane shows axial view
        planeMapping.YZ = 'AX';
    elseif abs(sliceNormal(3)) >= alignmentThreshold
        % Slice normal in Z direction → YZ plane shows sagittal view
        planeMapping.YZ = 'SAG';
    else
        planeMapping.YZ = 'OBL';
    end

    % ZX plane (Y=constant): What view when looking along Y-axis?
    % For ZX plane, we need to determine what anatomical orientation
    % the ZX cross-section represents
    if abs(sliceNormal(2)) >= alignmentThreshold
        % Slice normal in Y direction → ZX plane shows sagittal view
        % ZX plane (looking along Y) shows sagittal view
        planeMapping.ZX = 'SAG';
    elseif abs(sliceNormal(1)) >= alignmentThreshold
        % Slice normal in X direction → ZX plane shows axial view
        planeMapping.ZX = 'AX';
    elseif abs(sliceNormal(3)) >= alignmentThreshold
        % Slice normal in Z direction → ZX plane shows coronal view
        planeMapping.ZX = 'COR';
    else
        planeMapping.ZX = 'OBL';
    end

    % Handle oblique cases
    if strcmp(orientation, 'OBL')
        % For oblique orientations, determine best approximation for each plane
        [~, xyDominant] = max([abs(sliceNormal(1)), abs(sliceNormal(2))]);
        [~, yzDominant] = max([abs(sliceNormal(2)), abs(sliceNormal(3))]);
        [~, zxDominant] = max([abs(sliceNormal(1)), abs(sliceNormal(3))]);

        if xyAlignment_Z < 0.5  % Not clearly axial
            if xyDominant == 1
                planeMapping.XY = 'SAG';
            else
                planeMapping.XY = 'COR';
            end
        end
        if yzAlignment_X < 0.5  % Not clearly sagittal
            if yzDominant == 2
                planeMapping.YZ = 'AX';
            else
                planeMapping.YZ = 'COR';
            end
        end
        if zxAlignment_Y < 0.5  % Not clearly coronal
            if zxDominant == 2
                planeMapping.ZX = 'AX';
            else
                planeMapping.ZX = 'SAG';
            end
        end
    end
end