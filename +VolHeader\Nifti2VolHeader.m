function refcs = Nifti2VolHeader(hdr)
    refcs = VolHeader; 
    refcs.data_type=hdr.Datatype;
    imgsize = hdr.ImageSize; 
    pixdim =  hdr.PixelDimensions;
    if numel(imgsize)<3 
        imgsize(3)=1; 
    end
    if numel(pixdim)<3 
        pixdim(3)=1; 
    end
    refcs.x_dim = imgsize(1); 
    refcs.y_dim = imgsize(2); 
    refcs.z_dim = imgsize(3); 
    refcs.x_pixdim = pixdim(1); 
    refcs.y_pixdim = pixdim(2); 
    refcs.z_pixdim = pixdim(3); 
    
    qfac = hdr.Qfactor; 
    
    T = hdr.Transform.T; 

    T(:, 1:2) = -T(:, 1:2);

    
    if 0
    refcs.TransformMatrix = T(:)'; 
 
    A = refcs.DicomAffineMatrix; 
    start = A*[0 0 0 1]';
    
    else
        
        Spacing= abs([refcs.x_pixdim; refcs.y_pixdim; refcs.z_pixdim]); 
%         if qfac==-1
%             Spacing(2) = -Spacing(2);
% %             I = eye(4); I(2, 2)=-1; T = T*I;
% %             T(:, 2)= -T(:, 2); T(1:2, 1:2) = T(1:2, 1:2)';
%         end
        
        A = T'; 
        
        %%%%%NIFTI seems to have a bug in trasform matrix if X and Y pixdim
        %%%%%is different
%         R = A(1:3, 1:3).*(1./Spacing); 
%         A(1:3, 1:3) = R;
%         start = A*[0 0 0 1]';
%         refcs.ImageOrientationPatient =R(:)';
        %here is a temporary fix
        [orientation, s, start] = AffineMatrix4x4.DecomposeAffineMatrix(A);
%         s1 = pixdim; 
%         A = AffineMatrix4x4.ComposeAffineMatrix(orientation, s1, t);
%         start = A*[0 0 0 1]';
        refcs.ImageOrientationPatient =orientation(:)';
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%        
    end
    
    refcs.x_start = start(1); 
    refcs.y_start = start(2); 
    refcs.z_start = start(3); 
    refcs.LinearMeasureUnit = 'mm'; 
end
