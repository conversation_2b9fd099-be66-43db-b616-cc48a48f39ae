classdef AffineMatrix4x4
    properties
        
    end
    
    methods (Static)
        function A = Eye()
            A = eye(4);
        end
        
        function A = Rx(theta)
           A = eye(4);
           A(2, 2) = cos(theta); 
           A(2, 3) = -sin(theta);
           A(3, 2) = sin(theta);
           A(3, 3) = cos(theta);
        end
        
        function A = Ry(theta)
           A = eye(4);
           A(1, 1) = cos(theta); 
           A(1, 3) = sin(theta);
           A(3, 1) =-sin(theta);
           A(3, 3) = cos(theta);
        end
        
        function A = Rz(theta)
           A = eye(4);
           A(1, 1) = cos(theta); 
           A(1, 2) =-sin(theta);
           A(2, 1) = sin(theta);
           A(2, 2) = cos(theta);
        end
        
        function A = ScaleMatrix(s)
           A = eye(4); 
           A(1, 1) =s(1); 
           A(2, 2) =s(2); 
           A(3, 3) =s(3); 
        end
        
         function A = OffsetMatrix(t)
           A = eye(4, 4); 
           A(1, 4) =t(1); 
           A(2, 4) =t(2); 
           A(3, 4) =t(3); 
         end
        
         function [A, R] = ImageOrientationMatrix(orientation)
            if  numel(orientation)==0
                R = eye(3);
            elseif numel(orientation)==6
                R = reshape(orientation, [3 2]); 
                R(:, 3)= cross(R(:, 1), R(:, 2)); 
            elseif numel(orientation)==9
                R = reshape(orientation, [3 3]); 
            end
             A = eye(4); 
             A(1:3, 1:3) = R;
         end
         
         
         function R = EulerRotationMatrix(angle_x, angle_y, angle_z, rotorder)
             if ~exist('rotorder', 'var')
                 rotorder = 'ZXY';
             end
             RotationX = AffineMatrix4x4.Rx(angle_x); 
             RotationY = AffineMatrix4x4.Ry(angle_y); 
             RotationZ = AffineMatrix4x4.Rz(angle_z); 
             switch upper(rotorder)
                 case 'ZXY' %// Like VTK transformation order
                     R = RotationZ*RotationX*RotationY;
                 case 'ZYX' 
                     R = RotationZ*RotationY*RotationX;
             end
         end
         
         %A=T*R*S
         function A = ComposeAffineMatrix(orientation, s, t)
             %orientation(1:6) is a image orientation vector that specify x, y, axis orientation
             %s(1:3) pixel size along x, y, z dimension
             %t(1:3) coordinate of the first pixel
             %A is image2patinet transform matrix
             R = AffineMatrix4x4.ImageOrientationMatrix(orientation);
             S = AffineMatrix4x4.ScaleMatrix(s);
             T = AffineMatrix4x4.OffsetMatrix(t);
             %A = R*S+T;
             A = T*R*S;
             %A = R*S; A(1:3, 4) = t(:);
         end
         
          function [orientation, s, t] = DecomposeAffineMatrix(A)
             %orientation(1:9) is a image orientation vector that specify x, y, axis orientation
             %s(1:3) pixel size along x, y, z dimension
             %t(1:3) coordinate of the first pixel
             %A is image2patinet transform matrix
             t = A(1:3, 4); 
             A(1:3, 4) = 0; 
             B = A(1:3, 1:3);
             S2= B'*B; 
             s = sqrt([S2(1, 1), S2(2, 2), S2(3, 3)]');
             S = AffineMatrix4x4.ScaleMatrix(s);
             R = A/S; 
             %R = A*inv(S);
             orientation = R(1:3, 1:3);
             orientation = orientation(:);
          end
         
          function demo1
              R = AffineMatrix4x4.Rz(30)*AffineMatrix4x4.Rx(45);
              orientation = R(1:3, 1:3); orientation=orientation(:);
              s = [1 2 3];
              t = [-10 10 100];
              A = AffineMatrix4x4.ComposeAffineMatrix(orientation, s, t);
              [orientation1, s1, t1] = AffineMatrix4x4.DecomposeAffineMatrix(A);
              [orientation(:)'; orientation1(:)']
              [s(:)'; s1(:)']
              [t(:)'; t1(:)']
          end
    end
end

