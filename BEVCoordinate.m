
classdef BEVCoordinate <handle
    %UNTITLED4 Summary of this class goes here
    %   Detailed explanation goes here
    
    properties
%         relSrcPos;	   %relative to isoCenter	
        plane;         %the isocenter plane in IVCS
        xDir;               %Point<float, 3> //in IVCS
        yDir=[0 0 1];               %Point<float, 3> //in IVCS
%         caxDir;             %Point<float, 3> //in IVCS
        rhs=1;              %bool 
        SAD=100;
    end
    
    methods
        function obj = BEVCoordinate(varargin)
            obj.setBeamAngle(0); 
        end
        
        function setCoorDirs(obj,  xDir,  yDir, caxDir) 
            obj.xDir	= xDir; 
            obj.yDir	= yDir; 
            obj.plane   = SimplePlane(caxDir, 0); 
        end
        
        function dir = caxDir(obj)
            dir = obj.plane.direction; 
        end
        
        function p = relSrcPos(obj) 
            p = obj.caxDir*(-obj.SAD); 
        end
        
        function setBeamAngle(obj, projAngle)
            % once beamAngle is determined, xdir, ydir, and zdir can be set
            % to form an RHS coordinate system so that ydir is in the postive 
            % IVCS-z, and zdir is the direction pointing from the source to
            % the isocenter
            
            %             roll = obj.rhs ? (pi/2.0 - projAngle) : (projAngle - pi/2.0); 
           if 0
            if obj.rhs
                roll = pi/2.0 - projAngle;
            else
                roll = projAngle - pi/2.0;
            end
            
            cosAngle = cos(roll); 
            sinAngle = sin(roll);
            xDir = [sinAngle  -cosAngle 0];
            % caxDir is the direction of the source pointing to the
            % isocenter
            caxDir = [-cosAngle  -sinAngle  0];
            if ~obj.rhs 
                xDir=-1.0*xDir;  
            end
           else
               roll = projAngle;
               cosAngle = cos(roll); 
               sinAngle = sin(roll);
               xDir = [cosAngle, sinAngle, 0] ;
               caxDir = [-sinAngle, cosAngle, 0] ;
           end
               
            yDir = [ 0  0  1];
             %relSrcPos0 = - caxDir*obj.SAD;
            obj.setCoorDirs(xDir, yDir, caxDir); 
        end
        
        %p is the 2D coordinate on the plane
        function dir = getRadDir(obj,  p) 
            sp = obj.xDir*p(1)+obj.yDir*p(2) + obj.plane.getOrigin();
            dir= (sp-obj.relSrcPos)/norm(sp-obj.relSrcPos);
        end
        
        function result = ivcsCoor2BEVCoor(obj, p)
            line = SimpleLine(p-obj.relSrcPos, p);
            r    = norm(p-obj.relSrcPos); 
%             r = dot(p-obj.relSrcPos, obj.caxDir);
            [t, p1] = obj.plane.lineIntersection(line); 
%             result[0] = r; 
%             result[1] = p1.dotProduct(xDir); 
%             result[2] = p1.dotProduct(yDir);
            result = [dot(p1, obj.xDir), dot(p1, obj.yDir), r]; 
        end
        
        function A = getRotationMatrix(obj)
            A = [obj.xDir' obj.yDir' obj.caxDir']; 
        end
    end
end

