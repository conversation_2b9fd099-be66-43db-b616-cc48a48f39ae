classdef CTImage < VolHeaderImage
    properties  (Constant)
       % maxCTNumber = 4096
        
    end
    
    properties
        IVDT; 
    end
    
    methods
        function obj = CTImage(varargin)
            obj = obj@VolHeaderImage(varargin{:}); 
            obj.setIVDT; 
        end
        
%         function setIVDT(obj, varargin)
%             %obj.IVDT = CTImage.constructIVDT(varargin{:});
%             if numel(varargin)>=1
%                 obj.IVDT = varargin{1}; 
%             elseif isempty(obj.IVDT)
%                 %from Eclipse TPS DEF_CTSCANNER 28 ct number to electron density
%                 obj.IVDT(1, :) = [      -1000         100       1000        6000]+1000;
%                 obj.IVDT(2, :) = [       0           1.1000     1.5320       3.9200]; 
%             end
%         end
        
        function setIVDT(obj, ivdt)
            if ~exist('ivdt', 'var')
                ivdt = 'DefaultIVDT_kVCT'; 
            end
            if ischar(ivdt)
                obj.IVDT = obj.(ivdt); 
            elseif isnumeric(ivdt)
                obj.IVDT = ivdt; 
            end
        end
        
        function table = constructIVDTTable(obj)
            ct      = obj.IVDT(1, :); 
            density = obj.IVDT(2, :);
            [ct, I] = sort(ct); 
            density = density(I); 
            if ct(1)~=0
                ct= [0 ct];
            end
            cti = 0:1:CTImage.maxCTNumber;
            table =  interp1(ct,density,cti,'linear','extrap');
        end
        
        
        function density = toDensity(obj, varargin)
%             if exist('IVDT', 'var')
%                 IVDT = obj.IVDT; 
%             end
           obj.setIVDT(varargin{:});  
           table   = constructIVDTTable(obj);
           density = CTImage(obj); 
           density.data = table(max(1, int32(obj.data+1)));
           density = density.setFloat; 
           density.imageType='density'; 
        end
        
        function density = toDensity_resample(ct, sampleFactor, varargin)
            density = ct.toDensity(varargin{:}); 
            if sampleFactor~=1
                dstHeader = VolHeader(density.header); 
                dstHeader.x_dim = dstHeader.x_dim/sampleFactor; 
                dstHeader.y_dim = dstHeader.y_dim/sampleFactor; 
                dstHeader.x_pixdim = dstHeader.x_pixdim*sampleFactor; 
                dstHeader.y_pixdim = dstHeader.y_pixdim*sampleFactor; 
                density.reform(dstHeader); 
            end
        end
        
        function [info] = readDicomCTSlice(obj, fname)
            info = obj.readDicomInfo(fname);
            obj.setData(double(dicomread(fname))); 
            obj.setShort;
        end
        

        function readDicomCT2(obj, filepaths)
            readDicomCT(obj, filepaths);
        end
        
        function readDicomCT(obj, filepaths)
%             if ischar(filepaths) && exist(filepaths, 'dir')
%                 names = DosUtils.listFile([filepaths 'CT.*']);
%                 filepaths = cellfun(@(x)(fullfile(filepaths, x)), names,  'UniformOutput', false);
%             end
            ct = RtCt2(filepaths);
            convertFromBasicImage(obj, ct);
            obj.data = obj.data+1000; 
            obj.ImageOrientationPatient = dcm4che2.getTag(ct.jInfo, 'ImageOrientationPatient');
%             obj.ImagePositionPatient = [obj.x_start, obj.y_start, obj.z_start]'; 
           
        end
        
        function convertFromBasicImage(obj, src)
            prefix = 'xyz'; 
            for k=1:3
                pre = [prefix(k) '_' ];
                obj.([pre 'pixdim'])=src.spacing(k); 
                obj.([pre 'start']) =src.start(k); 
                obj.([pre 'dim'])   =src.extent(k); 
            end
            obj.setData(permute(src.data, [2 1 3])); 
        end
        
        function removeCouch(obj, couchPos)
            if ~obj.reverseY
                yi = obj.yData > couchPos;
            else
                yi = obj.yData < couchPos;
            end
            
%             orientation = obj.imageOrientation; 
%             if orientation.y>0
%                  yi = obj.yData > couchPos;
%             else
%                  yi = obj.yData < couchPos;
%             end
            obj.data(yi, :, :)= 0; 
        end
        
%         function replaceCouch(obj, couchPos, couchTopRadDepth, couchTopThickness)
%             if ~exist('couchTopRadDepth', 'var')||isempty(couchTopRadDepth)
%                 couchTopRadDepth=0;
%             end
%             
%             if ~exist('couchTopThickness', 'var')
%                 couchTopThickness=5; %5cm
%             end
%             
%             removeCouch(obj, couchPos);
%             
%             if couchTopRadDepth>0
%                 if ~obj.reverseY
%                     yi = obj.yData > couchPos & obj.yData <= couchPos+couchTopThickness;
%                 else
%                     yi = obj.yData < couchPos & obj.yData >= couchPos+couchTopThickness;
%                 end
% 
%                 density = couchTopRadDepth/couchTopThickness;
%                 obj.data(yi, :, :)= density;
%             end
%             
%         end
         function replaceCouch(obj, couchPos, couchImage)
             if exist('couchImage', 'var') && isa(couchImage, 'RTSupportStructure')
                 couchImage.replaceCTCouch(obj, couchPos); 
             else
                 obj.removeCouch(couchPos.y); 
             end
         end
    end
    
    methods (Static)
%         function IVDT = constructIVDT(ct, density)
%             if nargin<1
% %                 ct      =[0,         1024,                    2048,                    32760];
% %                 density =[0.000,     1.025000,                2.000,                   22.6];
% 
% %from Eclipse TPS DEF_CTSCANNER 28 ct number to electron density
%                 ct      = [      -1000         100        1000        6000]+1000;
%                 density = [       0           1.1000     1.5320       3.9200]; 
%             end
%             
%             [ct, I] = sort(ct); 
%             density = density(I); 
%             if ct(1)~=0
%                 ct= [0 ct];
%             end
%             cti = 0:1:CTImage.maxCTNumber;
%             IVDT =  interp1(ct,density,cti,'linear','extrap');
%         end

        function res = maxCTNumber 
            res = 65536; 
        end
        
        function dst = createAttenuatorImage(header, thickness, densityVal)
            if ~exist('densityVal', 'var')
                densityVal = 1.0; 
            end
            if isempty(header)
                header = VolHeader; 
                L = 51.2; pixdim = 0.2; 
                header.x_pixdim = pixdim; 
                header.y_pixdim = pixdim; 
                header.z_pixdim = pixdim; 
                header.x_dim = L/header.x_pixdim; 
                header.y_dim = L/header.y_pixdim; 
                header.z_dim = L/header.z_pixdim;
                header.alignXYZCenter; 
            end
            dst = VolHeaderImage(header); 
            dst.data=zeros(dst.y_dim, dst.x_dim, dst.z_dim);  
            ydata = dst.yData; 
            I = (ydata>=-thickness/2 & ydata<thickness/2); 
            dst.data(I, :, :)=densityVal;
        end
        
        %from Eclipse TPS DEF_CTSCANNER 28 ct number to electron density
        function ivdt = DefaultIVDT_kVCT
            ivdt(1, :) = [      -1000         100       1000        6000]+1000;
            ivdt(2, :) = [       0           1.1000     1.5320       3.9200]; 
        end
        
        function ivdt = DefaultIVDT_TomoMVCT
            ivdt =[
                      0.00000000,  16.00000000, 330.00000000, 531.00000000, 1037.00000000, 1141.00000000, 1158.00000000, 1325.00000000, 1487.00000000, 1696.00000000
                      0.00000000,   0.00100000,   0.30000001,   0.49000001,   1.00000000,   1.13900006,   1.15199995,   1.33399999,   1.56200004,   1.82400000
                  ];
        end

        function ctimg = Density2CT(densityimg, ivdt)
            if ~exist('ivdt', 'var')
                ivdt = CTImage.DefaultIVDT_kVCT; 
            end
            ctimg = CTImage(VolHeaderImage(densityimg));
            ctimg.setIVDT(ivdt);
            ctimg.data=interp1(ivdt(2, :), ivdt(1, :), densityimg.data,'linear', 'extrap');
            ctimg.setShort(); 
        end
    end
end

