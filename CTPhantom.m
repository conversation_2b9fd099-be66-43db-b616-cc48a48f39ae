classdef CTPhantom
    %UNTITLED2 Summary of this class goes here
    %   Detailed explanation goes here
    
    properties
    end
    
    methods
    end
    
    methods (Static)
        function [CT, density] = CTNumber
            CT.water = 1000; 	density.water = 1.000;
            CT.muscle = 1024; 	density.muscle = 1.04; 
            CT.bone = 2658; 	density.bone = 1.85;
            CT.lung = 266; 		density.lung = 0.25; 
            CT.adipose = 920; 	density.adipose = 0.920; 
            CT.cork = 298; 	  	density.cork = 0.280; 
            CT.air= 1;          density.air = 0.001; 
        end
        
        function s = matSymbol
            s.water = 'W'; 
            s.muscle = 'M'; 
            s.bone = 'B'; 
            s.lung = 'L'; 
            s.adipose = 'A'; 
            s.cork = 'C';
            s.air ='a'; 
        end
        
        function ct = createWaterPhantom(header)
            ct = VolHeaderImage(header); 
           
            ctnum = CTPhantom.CTNumber; 
            ct.data= ctnum.water* ones(header.y_dim, header.x_dim, header.z_dim); 
            ct.setShort; 
        end
        
        function ct = createSimpleMask(header)
            ct = VolHeaderImage(header); 
            ct.data = zeros(header.y_dim, header.x_dim, header.z_dim); 
            xc = header.x_dim/2; xci=round(xc); 
            yc = header.y_dim/2; yci=round(yc); 
            zc = header.z_dim/2; zci=round(zc); 
            I = -31:32; 
            ct.data(yci+I, xci+I, zci+I) = 1; 
            I = -15:16; 
            ct.data(yci+I, xci+I, zci+I) = 257; 
            ct.setShort; 
        end
        
        function ct = createSlabCT(header, mat, thick)
            if ~exist('mat', 'var') || ~exist('thick', 'var')
                [ mat, thick] = CTPhantom.standardSlab; 
                thick = thick*header.y_dim*header.y_pixdim;
            end
            ct = VolHeaderImage(header); 
            ctnum = CTPhantom.CTNumber; 
            M0=1; 
            M1=0; 
            A = zeros(ct.y_dim, ct.x_dim); 
            for k=1:length(mat)
                ctval = ctnum.(mat{k});
                M = round(thick(k)/ct.y_pixdim); 
                if M==0
                    continue; 
                end
                M1 = M1+M; 
                A(M0:M1, :)= ctval; 
                M0=M0+M; 
            end
            ct.data = repmat(A, [1, 1, ct.z_dim]); 
            ct.setShort; 
        end
        
        function ct = createMedistinum(header, w, h, s)
            if ~exist('s', 'var')
                s = 1/8; 
            end
            
            if ~exist('w', 'var')
                w = 1/4; 
            end
            
            if ~exist('h', 'var')
                h = 1/2; 
            end
            
            M = header.x_dim; N = header.y_dim; 
            x  = round(1:(w*M)); y = round(1:(h*M));
            x0 = round((.5-s-w)*M); x1 = round((.5+s)*M);
            y0 = round((1/2-h/2)*M); 
            
            CT = CTPhantom.CTNumber; 
            A = zeros(header.x_dim, header.y_dim);
           
            A(:, :) = CT.water; 
            A(y0+y, x0+x) = CT.cork; 
            A(y0+y, x1+x) = CT.cork; 
            
            ct = VolHeaderImage(header); 
            ct.data = repmat(A, [1, 1, ct.z_dim]);
            ct.setShort; 
        end
        
       
        function [mat, thick] = standardSlab
            mat = {'adipose', 'muscle', 'bone', 'muscle', 'lung', 'muscle',  'bone', 'adipose', 'bone','muscle','adipose'};
            thick = [1 1 1 1 6 1 1 1 1 1 1]/16; 
        end
        
        
        function plotSlab_1D(mat, thick, yMax)
            if ~exist('yMax', 'var')
                yMax = 100; 
            end
            yMax = 1.1*yMax; 
            symbol = CTPhantom.matSymbol;
            Y = [0 yMax];  
            x = [0 cumsum(thick)]; 
            for k=1:length(mat)
                line([x(k+1) x(k+1)], Y); text((x(k)+x(k+1))/2, Y(2), symbol.(mat{k}));
            end           
        end
        
        function plotMedistinum_2D(w, h, M,s, varargin)
            if ~exist('s', 'var')
                s=1/8;
            end
            
            symbol = CTPhantom.matSymbol;
            x0 = round((.5-s-w)*M); x1 = round((.5+s)*M);
            y0 = round((1/2-h/2)*M); 
            xrange = round([1 w*M]); 
            yrange = round([1 h*M]);
            
            X = x0+[xrange(1) xrange(2) xrange(2) xrange(1)]; 
            Y = y0+[yrange(1) yrange(1) yrange(2) yrange(2)]; 
            CTPhantom.plotPolygon(X, Y, varargin{:});
            text(mean(X), mean(Y), symbol.cork);
            
            X = x1+[xrange(1) xrange(2) xrange(2) xrange(1)]; 
            CTPhantom.plotPolygon(X, Y, varargin{:});
            text(mean(X), mean(Y), symbol.cork);
            text(M/2, M-10, symbol.water);
            CTPhantom.setTicks(gca, M); 
        end
        
        function plotSlab_2D(mat, thick, xrange, varargin)
            symbol = CTPhantom.matSymbol;
            y = [0 cumsum(thick)]; 
            X = [xrange(1) xrange(2) xrange(2) xrange(1)];
            for k=1:length(mat)
                Y = [y(k) y(k) y(k+1) y(k+1)]; 
                CTPhantom.plotPolygon(X, Y, varargin{:});
                text(xrange(1), (y(k)+y(k+1))/2, symbol.(mat{k}));
            end           
            CTPhantom.setTicks(gca, xrange(2)); 
        end
        
        function plotPolygon(X, Y, varargin)
             N = length(X); 
             X = [X X(1)]; 
             Y = [Y Y(1)]; 
             for k=1:N
                 line([X(k),  X(k+1)], [Y(k),  Y(k+1)], varargin{:});
             end
        end
        
        function setTicks(axH, M, res)
            if ~exist('res', 'var')
                res = 0.1;
            end
                
            xticks=-100:50:100;
            yticks= 0:50:250;
            set(axH, 'XTick',xticks+M/2+0.5,'XTickLabel',xticks*.1, ...
                     'YTick',yticks+0.5,'YTickLabel',  yticks*.1,'FontSize',12, ...
                 'xLim', [0, M], 'yLim', [0 M]);
            xlabel('distance (cm)'), ylabel('depth (cm)');
            
            axis ij; axis image; 
        end
    end
    
    
end

