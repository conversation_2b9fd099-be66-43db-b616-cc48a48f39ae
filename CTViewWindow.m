classdef CTViewWindow <OptionsMap   
    properties
        
    end
    
    methods
        function obj = CTViewWindow(varargin)
            obj@OptionsMap(varargin{:});   
        end
    end
    
    methods (Static)
    % https://radiopaedia.org/articles/windowing-ct       
    % head and neck
    % brain W:80 L:40
    % subdural W:130-300 L:50-100
    % stroke W:8 L:32 or W:40 L:40 3
    % temporal bones W:2800 L:600 or W:4000 L:700
    % soft tissues: W:350–400 L:20–60 4
    % chest
    % lungs W:1500 L:-600
    % mediastinum W:350 L:50
    % abdomen
    % soft tissues W:400 L:50
    % liver W:150 L:30
    % spine
    % soft tissues W:250 L:50
    % bone W:1800 L:400      
        function res = DefaultHUWindow(name)
            switch lower(name)
            %general
                case 'softtissue'
                    res = struct('W', 400, 'L', 50); 
                case 'softtissue_abdomen'
                    res= struct('W', 400, 'L', 50); 
                case 'softtissue_spine'
                    res = struct('W', 250, 'L', 50); 
                case 'bone'
                    res = struct('W', 1800, 'L', 400); 
                case 'bone_spine'
                    res = struct('W', 1800, 'L', 400); 
                case 'bone_temporal'
                    res= struct('W', 2800, 'L', 600); 
                case 'brain'
                    res = struct('W', 80, 'L', 40); 
                case 'brain_subdural'
                    res = struct('W', 220, 'L', 75); 
                case 'brain_stroke'
                    res = struct('W', 40, 'L', 40); 
                case 'softtissue_hn'
                    res= struct('W', 375, 'L', 40); 
                case 'lung'
                    res= struct('W', 1500, 'L', -600); 
                case 'mediastinum'
                    res= struct('W', 350, 'L', 50); 
                case 'liver'
                    res = struct('W', 150, 'L', 30); 
            end
        end

        function res = DefaultIntensityWindow_LH(name)
            hu = CTViewWindow.DefaultHUWindow(name);
            L = hu.L+1000; 
            res(1) = L - hu.W/2; 
            res(2) = L + hu.W/2; 
        end
    end
end

