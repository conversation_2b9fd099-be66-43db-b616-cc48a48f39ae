classdef DVFImage <VectorImage   
    properties
    end
    
    methods
        function self = DVFImage(varargin)
            self = self@VectorImage(varargin{:});
            self.imageType = 'dvf'; 
        end
        
        function readITKDeformationField(obj, mhdfile)
            header = obj.readMhdHeader(mhdfile);
            [PATHSTR,NAME,EXT] = fileparts(mhdfile);
            if isempty(PATHSTR) PATHSTR = '.'; end
            datafile = [PATHSTR filesep header.ElementDataFile]; 
            fid = fopen(datafile, 'rb', obj.machineFormat); 
            A = fread(fid, obj.data_type);
            fclose(fid);
            NDims = header.NDims;
            pixdims = [obj.x_pixdim, obj.y_pixdim, obj.z_pixdim];
            for n=1:NDims
                tempdata = reshape(A(n:NDims:end), [obj.x_dim, obj.y_dim, obj.z_dim]);
                vectordata(:, :, :, n) = permute(tempdata, [2 1 3])/pixdims(n);
            end
            
            obj.setVectorData(vectordata(:,:,:,1), vectordata(:,:,:,2), vectordata(:,:,:,3));
        end
        
        
        function dst = deformImage(self, src, varargin)
            if ~isa(src, 'VolHeaderImage')
                dst = deformImageData(self, src, varargin{:});
            else
                dstdata = deformImageData(self, src.data, varargin{:});
                dst = wtk.util.hackclone(src); 
                dst.setData(dstdata); 
            end
        end
        
        function dst = deformImageData(self, srcdata, varargin)
%             dst = wtk.util.hackclone(src);
            x0 = 1:self.x_dim; y0 = 1:self.y_dim; z0 = 1:self.z_dim; 
            [X0, Y0, Z0] = meshgrid(x0, y0, z0); 
            
%             N = self.x_dim*self.y_dim*self.z_dim; I = 1:N; 
%             siz = size(X0); 
 
%             X = X0 + permute(reshape(self.data(I), siz), [2 1 3]);
%             Y = Y0 + permute(reshape(self.data(N+I), siz), [2 1 3]); 
%             Z = Z0 + permute(reshape(self.data(2*N+I), siz), [2 1 3]); 
            X = X0 + self.dataX; 
            Y = Y0 + self.dataY; 
            Z = Z0 + self.dataZ; 
            
            if ndims(srcdata)==3
            dst = interp3(X0, Y0, Z0, srcdata, X, Y, Z, varargin{:}); 
            elseif ndims(srcdata==2)
                dst = interp2(X0, Y0,  srcdata, X, Y,  varargin{:}); 
            end
        end
    end
    
    methods (Static)
        function dvf = createShiftDVFImage(maskImage, shifts)
            dvf = DVFImage(maskImage); 
            dvf.setFloat(); 
            mask0 = logical(maskImage.data); 
            siz = 2*round(abs(shifts))+1; 
            nhood = ones(siz); 
            mask  = imdilate(mask0, nhood, 'same');
            
            nhood1 = ones(siz+2); 
            mask1  = imdilate(mask0, nhood1, 'same');
            
            zeromask = xor(mask, mask1); 
%             diffmask = xor(mask, mask0); 
            nanmask = not(or(mask0, zeromask));

            [xq, yq, zq] = meshgrid(maskImage.xData, maskImage.yData, maskImage.zData); 
           
            siz = size(maskImage.data); 
            vxi  = zeros(siz);vyi = zeros(siz); vzi = zeros(siz); 
            
            xqi = xq; yqi = yq; zqi = zq; 
            vxi(mask0) = shifts(2); vyi(mask0) = shifts(1); vzi(mask0) = shifts(3); 
            vxi(zeromask) = 0; vyi(zeromask) = 0; vzi(zeromask) = 0; 
            
            
            
            I = find(nanmask); 
            xqi(I)=[]; yqi(I) = []; zqi(I) = []; 
            vxi(I)=[]; vyi(I)=[]; vzi(I) = []; 
            
            if dvf.z_dim>1
                vx = griddata(xqi, yqi, zqi, vxi, xq, yq, zq); 
                vy = griddata(xqi, yqi, zqi, vyi, xq, yq, zq); 
                vz = griddata(xqi, yqi, zqi, vzi, xq, yq, zq); 
                vx(isnan(vx))=0; vy(isnan(vy))=0; vz(isnan(vz))=0;
                dvf.setVectorData(vx, vy, vz); 
            else %2D case
                vx = griddata(xqi, yqi, vxi, xq, yq); 
                vy = griddata(xqi, yqi, vyi, xq, yq); 
                vx(isnan(vx))=0; vy(isnan(vy))=0;
                dvf.setVectorData(vx, vy); 
            end
        end
    end
end

