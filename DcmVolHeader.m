classdef DcmVolHeader < VolHeader
    properties
        m_DicomInfo; 

        m_SOPInstanceUIDs;
    end

    methods
        function obj = DcmVolHeader(varargin)
            obj@VolHeader(varargin{:});
            if nargin==1 && ischar(varargin{1})
                fname = varargin{1};
                [~, ~, ext]=fileparts(fname);
                if strcmpi(ext, '.json')
                    obj.readJsonHeader(fname);
                elseif strcmpi(ext, '.gz')
                    hdr = niftiinfo(fname);
                    vh  = VolHeader.Nifti2VolHeader(hdr); 
                    obj.copyStruct(vh); 
                end
            end
        end

        function header = MhdHeader(self)
            header      = self.MhdHeader@VolHeader();
            
%             info = self.m_DicomInfo;
%             try
%                 info.Filename = strrep(info.Filename, '\', '/');
%             catch
%             end
%             try
%                 info.ImageType= strrep(info.ImageType, '\', '\\');
%             catch
%             end
% 
%             header.DicomInfo = info;     
            header.DicomInfo=self.m_DicomInfo;
            header.SOPInstanceUIDs= self.m_SOPInstanceUIDs; 
        end
        
        function FromMhdHeader(self, header)
            self.FromMhdHeader@VolHeader(header);
            
            self.m_DicomInfo = header.DicomInfo; 
            
            self.m_SOPInstanceUIDs = header.SOPInstanceUIDs;
        end

        function uid = SliceIndex2UID(self, zind)
            uid = self.m_SOPInstanceUIDs{zind};
        end
        
        %this function only work for LPS image, to be retired
        function uid = z2uid(self, z)
            % Useful for link RtStruct contour to image
            zind = round((z-self.z_start)/self.z_pixdim) + 1;
            uid = self.m_SOPInstanceUIDs{zind};
        end

        function res = GetDicomInfo(self)
            res = self.m_DicomInfo;
        end

        function SetDicomInfo(self, info)
            self.m_DicomInfo=info; 
        end

        function res = GetSOPInstanceUIDs(self)
            res = self.m_SOPInstanceUIDs;
        end

        function SetSOPInstanceUIDs(self, uids)
            self.m_SOPInstanceUIDs=uids; 
        end
    end

    methods (Static)
       
    end
end