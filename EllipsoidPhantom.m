classdef EllipsoidPhantom< VolHeaderImage
    %UNTITLED3 Summary of this class goes here
    %   Detailed explanation goes here
    
    properties
        
    end
    
    methods
        function density = EllipsoidPhantom(varargin)
            density = density@VolHeaderImage(varargin{:});
%             if nargin<1
%                 dim = 160;
%                 pixdim = 0.1;
%                 data = zeros(dim, dim, dim);
%                 self.setData(data);
%                 self.x_pixdim = pixdim; 
%                 self.y_pixdim = pixdim; 
%                 self.z_pixdim = pixdim; 
%                 self.alignXYZCenter();
%             end
            if nargin==1
                if isa(varargin{1}, 'VolHeader')
                    return;
                end
            end
            
            options = OptionsMap(varargin{:});
            density.x_pixdim=options.getOption('x_pixdim', .1); 
            density.y_pixdim=options.getOption('y_pixdim', .1);
            density.z_pixdim=options.getOption('z_pixdim', .1); 
            x_dim = options.getOption('x_dim', 160); 
            y_dim = options.getOption('y_dim', 160); 
            z_dim = options.getOption('z_dim', 160); 
            densityVal = options.getOption('DensityValue', 1); 
            density.setData(zeros(y_dim, x_dim, z_dim)*densityVal); 
            density.setFloat(); 
            density.alignXYZCenter; 
        end
        
        function AddEllipoid(self, paras, intensity)
            if isempty(self.data)
                self.setData(zeros(self.x_dim, self.y_dim, self.z_dim));
            end
            
            [X, Y, Z] = meshgrid(self.xData, self.yData, self.zData);
            radius = paras(1:3);
            if numel(paras)<6
                center = zeros(1, 3); 
            else
                center = paras(4:6);
            end
            
            if numel(paras)<9
                angles = zeros(1, 3); 
            else
                angles = paras(7:9);
            end
            
            %normalize
            X = (X-center(1))/radius(1);
            Y = (Y-center(2))/radius(2);
            Z = (Z-center(3))/radius(3);
            I = (X.^2+ Y.^2 + Z.^2)<=1;
                
%             A = diag((1./radius).^2);
%             [R] = self.composeRotationMatrix(angles(1), angles(2), angles(3));
%             A = R*A;
%             P = [X(:)-center(1) Y(:)-center(2) Z(:)-center(3)]; %(Nx3)
%             fun = @(p)(p*A*p'<=1);
            
%             I = P*A*P' <=1;
%             I = table2array(rowfun(fun, table(P)));
           
            if ~exist('intensity','var')
                intensity = 1;
            end
            self.data(I) = intensity; 
        end
        
        function [res, CC] = ComponentAnalysis(self)
           
        end
        
        function [res, num]=toLabelImage(self)
            res = VolHeaderImage(self);
            [res.data, num] = bwlabeln(self.data);
        end
    end
    
    methods (Static)
        function zContours = CreateZContours(vh, center, radius)
            phantom = EllipsoidPhantom(vh);
            phantom.data(:)=0;
            N=numel(radius);             radius((N+1):3) = radius(N);
            N=numel(center);             center((N+1):3) = 0;
            phantom.AddEllipoid([radius(:)' center(:)'], 1);
            xmesh = vh.xData;
            ymesh = vh.yData;
            zmesh = vh.zData;
            mask = phantom.data;  mask = permute(mask, [2 1 3]); 
            spacing = [vh.x_pixdim vh.y_pixdim vh.z_pixdim];
            % flip such that x,y,z are increasing
            if spacing(1) < 0
                xmesh = flipud(xmesh(:));
                mask  = flipdim(mask, 1);
            end
            if spacing(2) < 0
                ymesh = flipud(ymesh(:));
                mask = flipdim(mask, 2);
            end
            if spacing(3) < 0
                zmesh = flipud(zmesh(:));
                mask = flipdim(mask, 3);
            end
            zContours = RtStruct2.mask2zContours(xmesh, ymesh, zmesh, double(mask));
        end
        
        function [R, Rx, Ry, Rz] = composeRotationMatrix(alpha, beta, gamma)
            %R = composeRotationMatrix(alpha, beta, gamma)
            %R = Rx(alpha)*Ry(beta)*Rz(gamma)

            %Weiguo Lu, 08/06/01

            Rx = [	1 		0 		0;
                    0	cos(alpha)	-sin(alpha);
                    0	sin(alpha)	cos(alpha)];

            Ry = [	cos(beta) 	0 		sin(beta);
                    0			1		0;
                    -sin(beta)	0		cos(beta)];

            Rz = [	cos(gamma) 	-sin(gamma) 0;
                    sin(gamma)	cos(gamma)	0;
                    0			0			1];

            R = Rx*Ry*Rz;
        end
    end
end

