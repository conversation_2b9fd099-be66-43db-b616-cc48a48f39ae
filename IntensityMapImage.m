classdef IntensityMapImage <VolHeaderImage & RTBeamControlPoint & RTBeamDescription
    % accumulateSegments: Shapes of all segment are added up according to
    % the respective weights.
    % createSegment: Shape of a segment is created considering leaf
    % transmission.
    % fromControlPointSequence: shapes of all segments are stored as 3D
    % data. The third dimension is the number of segments.
    
    properties
        LeafPositionBoundaries;
        dosimetricLeafGap=0; 
        leafTransmissionFactor=0.02;
        leafScatterFactor = 0; 
%         refinedSegment; % This is a VolHeader.
    end
    
    methods
        function obj = IntensityMapImage(varargin)
            obj = obj@VolHeaderImage(varargin{:}); 
            obj = obj@RTBeamControlPoint(varargin{:}); 
            obj = obj@RTBeamDescription(varargin{:}); 
            if nargin <1
                obj.refresh; 
            end
        end
        
     
        function refresh(obj, varargin)
            obj.refresh@RTBeamControlPoint;
            if nargin<2
                return;
            end
            
            options = OptionsMap(varargin{:}); 
%             obj.LinearMeasureUnit = 'mm'; 
            
            header = obj.CreateIntensityMapHeader(varargin{:});
            
            obj.copyStruct(header); 
            
            obj.data = []; 
            
            %obj.dosimetricLeafGap = options.getoptioni('dosimetricLeafGap', 0.2); 
            
            obj.setLinearMeasureUnit('mm');
        end
          
        function fromDicomBeamInfo(obj, beamInfo)
            obj.copy(StructBase.getfieldx(beamInfo, 'Beam'));
            obj.copy(StructBase.getfieldx(beamInfo, 'ReferencedBeam'));
            obj.copy(StructBase.getfieldx(beamInfo, 'PatientSetup'));
             
            obj.LeafPositionBoundaries = StructBase.getfieldx_recursive(beamInfo, 'LeafPositionBoundaries'); 
            ControlPointSequence = StructBase.getfieldx_recursive(beamInfo, 'ControlPointSequence'); 
            obj.fromControlPointSequence(ControlPointSequence);
            
            try 
                NumberOfBlocks = StructBase.getfieldx(beamInfo.Beam, 'NumberOfBlocks');
                if NumberOfBlocks>0
                    BlockSequence=StructBase.getfieldx_recursive(beamInfo, 'BlockSequence'); 
                    Block        = BlockSequence.Item_1; 
                    numpoints    = Block.BlockNumberOfPoints; 
                    try
                    transmission = Block.BlockTransmission; 
                    catch
                        transmission = 1;
                    end
                    pos = reshape(Block.BlockData, 2, numpoints);  
                    pos = pos'; 
                    bw = createBWShape(obj, pos, obj);
                    for k=1:obj.z_dim
                        obj.data(:, :, k) = obj.data(:, :, k).*double(bw).*transmission;
                    end
                end
            catch err
            end
        end
        
        function fromControlPointSequence(obj, sequence)
%             obj.refineSegment;
            fn = fieldnames(sequence); 
            numControlPoints = numel(fn); 
            data = zeros(obj.y_dim, obj.x_dim, numControlPoints);
            controlPoint = RTBeamControlPoint; 
            for k=1:numControlPoints
                controlPoint.fromDicomControlPointItem(sequence.(fn{k}));
                [bw] = obj.createSegment(controlPoint);
                if ~isempty(bw)
                    data(:, :, k) = bw; 
                end
                
                names = fieldnames(RTBeamControlPoint);  
%                 for m=1:numel(names)
%                     if strcmpi(names{m}, 'MLCXPositions')||strcmpi(names{m}, 'MLCYPositions')
%                         names(m)=[]; 
%                     end
%                 end
                I = cellfun(@(x)(strcmpi(x, 'MLCXPositions')||strcmpi(x, 'MLCYPositions')), names);
                names(I)=[];
                
                if k==1
                    obj.copy(controlPoint, obj.DirectionFieldNames); 
                end
                
                obj.amendcopy(controlPoint, k, names); 
            end
            
            obj.setData(data); 
            obj.mergeControlPointProperties;
        end
        
        function bw = createBWShape(obj, pos, vh)
            if ~exist('vh', 'var')
                vh = obj.refinedSegment;
            end            
             ip = zeros(size(pos)); 
             ip(:, 1) = vh.physics2image(pos(:, 1), 'x'); 
             ip(:, 2) = vh.physics2image(pos(:, 2), 'y'); 
             bw = poly2mask(ip(:, 1), ip(:, 2), ...
                 vh.y_dim, vh.x_dim); 
        end
        
        function bw = createMLCShape(obj,  mlc_xdata, isMLCY)
            % There are 60 leaves on each of the left and right side
            % specified by 61 leaf boundry positions
            % The dosimetric leaf gap models the round shape of the leaf tip. 
             NumberOfLeafPairs = numel(obj.LeafPositionBoundaries)-1;
             L = zeros(NumberOfLeafPairs*2, 1); 
             R = zeros(NumberOfLeafPairs*2, 1); 
             for k=1:NumberOfLeafPairs
                 L([2*k-1, 2*k], 1) =  mlc_xdata(k)                  -obj.dosimetricLeafGap/2; 
                 R([2*k-1, 2*k], 1) =  mlc_xdata(k+NumberOfLeafPairs)+obj.dosimetricLeafGap/2; 
                 L(2*k-1, 2)        =  obj.LeafPositionBoundaries(k);
                 L(2*k, 2)          =  obj.LeafPositionBoundaries(k+1);
                 R(2*k-1, 2)        =  obj.LeafPositionBoundaries(k);
                 R(2*k, 2)          =  obj.LeafPositionBoundaries(k+1);   
             end
             pos = cat(1, L, R(end:-1:1, :)); 

             if ~exist('isMLCY', 'var')
                 isMLCY=0;
             end
             
             if ~isMLCY
                bw = obj.createBWShape(pos); 
             else
                vh=refinedSegment(obj); 
                temp = vh.x_dim; vh.x_dim = vh.y_dim;  vh.y_dim = temp; 
                temp = vh.x_pixdim; vh.x_pixdim = vh.y_pixdim;  vh.y_pixdim = temp; 
                temp = vh.x_start;  vh.x_start  = vh.y_start;   vh.y_start = temp; 
                bw = obj.createBWShape(pos, vh); 
                bw = bw'; %transpose for Elekta Linac
             end
        end
        
        function BW = createXYJawShape(obj, jaw_xdata, jaw_ydata)
            pos(1, :) = [jaw_xdata(1) jaw_ydata(1)];
            pos(2, :) = [jaw_xdata(2) jaw_ydata(1)];
            pos(3, :) = [jaw_xdata(2) jaw_ydata(2)];
            pos(4, :) = [jaw_xdata(1) jaw_ydata(2)];
            BW = obj.createBWShape(pos);
        end
        
        function [bw] = createSegment(obj, controlPoint)
            %[jaw_xdata, jaw_ydata, mlc_xdata] = controlPoint.getLeafJawPositions;
            bw_jaw = []; bw_mlc = []; 
            jaw_xdata = controlPoint.XJawPositions; 
            jaw_ydata = controlPoint.YJawPositions; 
            
            if isempty(jaw_xdata)
                xdata = obj.xData;
                jaw_xdata = [xdata(1) xdata(end)];
            end
            
            if isempty(jaw_ydata)
                ydata = obj.yData;
                jaw_ydata = [ydata(1) ydata(end)];
            end
            
            if ~isempty(jaw_xdata) && ~isempty(jaw_ydata)
                bw_jaw = obj.createXYJawShape(jaw_xdata, jaw_ydata);
            end
            
            if ~isempty(controlPoint.MLCXPositions)
                bw_mlc = obj.createMLCShape(controlPoint.MLCXPositions);
            elseif ~isempty(controlPoint.MLCYPositions)
                bw_mlc = obj.createMLCShape(controlPoint.MLCYPositions, 1);
            end
            
            if isempty(bw_jaw)
                bw_jaw = bw_mlc; 
            end
            
            bw = bw_jaw; 
            if ~isempty(bw_mlc)
                bw = bw_jaw & bw_mlc; 
                bw_leakage = bw_jaw & (~bw_mlc); 
                bw = bw+bw_leakage*obj.leafTransmissionFactor;
                if obj.leafScatterFactor~=0
                    bw_scatter = bw_jaw & (bw_mlc); 
                    bw = bw+bw_scatter*obj.leafScatterFactor;
                end
            end
            
            vh = obj.refinedSegment;
            if ~obj.isEqualDim(vh)
                % reform to larger pixel size.
                y=obj.yData;
                bw=interp2(vh.xData,vh.yData,double(bw), ...
                    obj.xData,y','linear',0);
            else
                bw=double(bw); 
            end
        end
        
 
        
        
        function value = getControlPointProperty(obj, propName, index)
            if ~exist('index', 'var') || isempty(index)
                value = obj.(propName);
            else
                N = size(obj.(propName), 1); 
                value = obj.(propName)(min(index, N), :); 
            end
        end
        
        function [FX, FY] = getJawField(obj, index)
            FX = obj.getControlPointProperty('XJawPositions', index);
            FY = obj.getControlPointProperty('YJawPositions', index);
        end
        
        function maxField = getJawEqFieldSize(obj, index)
            [FX, FY] = obj.getJawField(index); 
            fx = abs(FX(2)-FX(1)); fy = abs(FY(2)-FY(1)); 
            maxField = 2*fx*fy/(fx+fy); 
        end
        
        function [FX, FY] = getRectField(obj, index)
            [FX, FY] = getJawField(obj, index);
            bw = obj.data(:, :, index); 
            [I, J] = ind2sub(size(bw), find(bw)); 
            yi = [min(I(:)), max(I(:))];
            xi = [min(J(:)), max(J(:))];
            y=yi; x=xi;
            for k=1:2
                y(k) = obj.image2physics(yi(k), 'y'); 
                x(k) = obj.image2physics(xi(k), 'x');
            end
            x=sort(x); y = sort(y); 
            FX = [max(FX(1), x(1)) min(FX(2), x(2))]; 
            FY = [max(FY(1), y(1)) min(FY(2), y(2))]; 
        end
        
        function mergeControlPointProperties(obj)
            M = obj.z_dim;
            fn = fieldnames(RTBeamControlPoint);
            for k=1:numel(fn)
                name = fn{k};
                N = size(obj.(name), 1); 
                if N==M
                    if isequal(obj.(name), repmat(obj.(name)(1, :), [M, 1]))
                        obj.(name)=obj.(name)(1, :);
                    end
                end
            end
        end
        
        function weights = getSegmentWeights(obj)
            obj.CumulativeMetersetWeight=obj.CumulativeMetersetWeight(:);
            FinalCumulativeMetersetWeight=1;
            if ~isempty(obj.FinalCumulativeMetersetWeight) && obj.FinalCumulativeMetersetWeight~=0
                FinalCumulativeMetersetWeight=obj.FinalCumulativeMetersetWeight; 
            end
            K = size(obj.data, 3); 
            weights    = zeros(1, K); 
            prevweight = 0; 
            for k=1:K
                CumulativeMetersetWeight = obj.getControlPointProperty('CumulativeMetersetWeight', k);
                weights(k) = (CumulativeMetersetWeight - prevweight)/FinalCumulativeMetersetWeight; 
                prevweight = CumulativeMetersetWeight;    
            end
        end
        
        function N = numSegments(obj)
            N = obj.z_dim; 
        end
        
        function dstImage = accumulateSegments(obj, weights, srcData, arcSamples)  
            if ~exist('arcSamples', 'var')
                arcSamples = 5; 
            end
            
            if ~exist('weights', 'var') || isempty(weights)
                weights = obj.getSegmentWeights;
            end
            
            if ~exist('srcData', 'var') || isempty(srcData)
                srcData = obj.data; 
            end
            
            
            K = obj.numSegments; 
            GantryAngles0 = obj.GantryAngle; 
            if numel(GantryAngles0)==1
                M = 1; 
                arcSamples=K;
            else
                M = ceil(K/arcSamples); 
            end
            
            dstData = zeros(obj.y_dim, obj.x_dim, M);      
            rotAngle = obj.BeamLimitingDeviceAngle; 
                       
            if ~obj.reverseY
                 rotAngle = -rotAngle; 
            end
            GantryAngles=zeros(1,M);
            for m=1:M
                for n=1:arcSamples
                    k = (m-1)*arcSamples+n; 
                    if k>K
                        break; 
                    end
                    weight      = weights(k);
                    if weight==0
                        continue; 
                    end
%                     if numel(rotAngle)==1    || abs(rotAngle(k))<=0.5
%                         dstData(:, :, m) = dstData(:, :, m) + srcData(:, :, k)*weight; 
%                     else
%                         dstData(:, :, m) = dstData(:, :, m) + imrotate(srcData(:, :, k),  rotAngle(k), 'bilinear', 'crop')*weight; 
%                     end
                     dstData(:, :, m) = dstData(:, :, m) + srcData(:, :, k)*weight; 
                end
                
                mid = (m-1)*arcSamples+round(arcSamples/2);
                GantryAngles(m) = obj.getControlPointProperty('GantryAngle', mid); 
            end
            
            
            
            if ~isempty(obj.BeamMeterset) && obj.BeamMeterset~=0
                dstData = dstData*obj.BeamMeterset;
            end
            
%             if numel(rotAngle)==1  && abs(rotAngle)>0.5
%                 for m=1:M
%                     dstData(:, :, m) = imrotate(dstData(:, :, m),  rotAngle, 'bilinear', 'crop');
%                 end
%             end
            
            dstImage = PortalImage(obj);    
            dstImage.setData(dstData); 
            dstImage.GantryAngle = GantryAngles(:); 
        end
        
        
        
        function res = hasMLC(obj)
            res = ~isempty(obj.LeafPositionBoundaries); 
        end
        
        
        function doubleSourceBlur(obj, dilateFactor, weight)
            src2 = IntensityMapImage(obj); 
            src2.x_pixdim = src2.x_pixdim * dilateFactor;
            src2.y_pixdim = src2.y_pixdim * dilateFactor;
            src2.alignXYCenter; 
            src2.reform(obj); 
            obj.data = obj.data*(1-weight) + src2.data*weight;
        end
        
        function applySourceBlur(obj, mu)
%             mu = 0.1; 
            sourceKernel      = ProfileData.ExpKernel(mu, -10:0.1:10); 
            kernelHeader           = ImageUtils.KernelImageTemplate2D({'kernelPixdim', obj.x_pixdim}); 
            sourceKernelImage      = VolHeaderImage.constructSymmetryProfImage(sourceKernel, kernelHeader);
            sourceKernelImage.data = sourceKernelImage.data/sum(sourceKernelImage.data(:)); 
            obj.conv2D(sourceKernelImage.data); 
        end
        
        function applyAmbientField(obj, varargin)
            options = OptionsMap(varargin{:}); 
            ambientField = options.getOption('AmbientField'); 
            beamData = options.getOption('BeamData'); 
            if isempty(ambientField) && ~isempty(beamData)
%                 prof    = beamData.getCrossProfiles('Diag'); prof.data=prof.data/100; 
%                 prof.data(prof.dist>20) = prof.getValue(20); 
%                 prof.data(prof.dist<-20)= prof.getValue(-20); 
%                 ambientHeader= obj.header; ambientHeader.z_dim=1;  
%                 ambientField = VolHeaderImage.constructSymmetryProfImage(prof,  ambientHeader);
                ambientField = beamData.constructAmbientImage;
            end
            
            beamInfo = options.getOption('BeamInfo');
            if isa(beamInfo, 'RTBeamInfo')
                YJawPositions = obj.getControlPointProperty('YJawPositions', 1);
                [prof, numEDWs] = EnhancedDynamicWedge.getEDWProfile(beamInfo, YJawPositions, ambientField.yData);
                if numEDWs>=1
                    ambientField.data = ambientField.data .* repmat(prof.data', [1, ambientField.x_dim]); 
                end
            end
            
            if ~isempty(ambientField)
                for k=1:obj.z_dim
                    obj.data(:, :, k) = obj.data(:, :, k).*ambientField.data;
                end
            end
        end
        
        function fluence = toFluenceMap(obj, beamData, varargin)   
            options = OptionsMap(varargin{:}); 
            fluence = IntensityMapImage(obj); 
%             prof    = beamData.getCrossProfiles('Diag'); prof.data=prof.data/100; 
%             prof.data(prof.dist>20) = prof.getValue(20); 
%             prof.data(prof.dist<-20)= prof.getValue(-20); 
%             ambientField = VolHeaderImage.constructSymmetryProfImage(prof, fluence);
%             for k=1:numel(fluence.z_dim)
%                 fluence.data(:, :, k) = fluence.data(:, :, k).*ambientField.data;
%             end
            fluence.applyAmbientField({'BeamData', beamData}); 
            
            linacHead = options.getOption('LinacHead');
            if ~isempty(linacHead)
                linacHead.applyExtrafocalRadiation(fluence, varargin{:}); 
                
                
                mu = linacHead.SourceSigma; 
                if mu>0
                    fluence.applySourceBlur(mu);
                end
            else
                dilateFactor = options.getOption('SecondSourceDilateFactor', 1.6); 
                weight       = options.getOption('SecondSourceWeight', 0.04); 
                for k=1:numel(dilateFactor)
                    fluence.doubleSourceBlur(dilateFactor(k), weight(k)); 
                end
                mu           = options.getOption('SourceSigma', 0.1);             
                if mu>0
                        fluence.applySourceBlur(mu);
                end
            end
            

           
%              %apply output factor
%              [Sc] = beamData.getInairOutputFactor_dmax(10, 10); 
%              fluence.data = fluence.data*Sc; 
        end  
        
        
        function setDefaultValue(self)
            if isempty(self.NominalBeamEnergy) 
                self.NominalBeamEnergy=6; 
            end
            names ={'GantryAngle', 'BeamLimitingDeviceAngle', 'PatientSupportAngle',...
                    'TableTopEccentricAngle'}; 
            for k=1:numel(names)
                name = names{k}; 
                if isempty(self.(name))
                    self.(name) = 0; 
                end
            end
            if isempty(self.IsocenterPosition)
                self.IsocenterPosition=[0 0 0]; 
            end
            if isempty(self.SourceAxisDistance)
                self.SourceAxisDistance=100; 
            end
        end
        
        function applyCircularModulation(self, modulationFactor, period)
            x=self.xData; y = self.yData; 
            [X, Y] = meshgrid(x, y);
            R = sqrt(X.^2+Y.^2); 
            modu = ones(size(R)); 
            f = (modulationFactor-1)/(modulationFactor+1);
            modu = modu + f*sin(R/period*2*pi); 
            self.data = self.data.*modu; 
        end
    end 
   
   methods (Access = private)
      function res = refinedSegment(obj)
            res=VolHeader(obj); 
            if (obj.x_pixdim<2 && strcmpi(obj.LinearMeasureUnit, 'mm')) || (obj.x_pixdim<0.2 && strcmpi(obj.LinearMeasureUnit, 'cm'))
                return; 
            end
            
            refineNum=2; % divide the pixdim by refineNum
            res.x_pixdim=obj.x_pixdim/refineNum;
            res.y_pixdim=obj.y_pixdim/refineNum;
            res.x_dim=refineNum*obj.x_dim;
            res.y_dim=refineNum*obj.y_dim;
            res.x_start=obj.x_start-obj.x_pixdim/2+res.x_pixdim/2;
            res.y_start=obj.y_start-obj.y_pixdim/2+res.y_pixdim/2;
      end
   end
   methods (Static)
        function fn = LinearMeasureFieldNames
            fn3 = RTBeamDescription.LinearMeasureFieldNames;
            fn2 = RTBeamControlPoint.LinearMeasureFieldNames;
            fn1 = VolHeader.LinearMeasureFieldNames;
            fn =cat(2, fn1, fn2, fn3, {'dosimetricLeafGap', 'LeafPositionBoundaries'}); 
        end
        
        %the following code is buggy
        function [X, Y]=BW2RectSubfield(bw)
%             [M, N] = size(bw);
            colsum = sum(bw,2); 
            [y0, ~] = find(colsum, 1, 'first'); 
            [y1, ~] = find(colsum, 1, 'last'); 
            XLeft=0; XRight=0; Y=y0; 
            for m=y0:y1
                x1 = find(bw(m, :), 1, 'first');
                x2 = find(bw(m, :), 1, 'last');
                if isempty(x1) 
                    continue; 
                end
                
                if x1~=XLeft(end) || x2 ~=XRight(end)
                    XLeft =cat(2,  XLeft,  x1);
                    XRight=cat(2,  XRight, x2);
                    Y = cat(2, Y, m); 
                else
                    Y(end) = m; 
                end
            end
            XLeft(1)=[]; XRight(1)=[]; 
            X=[XLeft, XRight]; 
        end
        
        function obj = CreateIntensityMapHeader(varargin)
            obj = VolHeader; 
            options = OptionsMap(varargin{:}); 
            obj.setLinearMeasureUnit('cm');
            pixdim = options.getoptioni('FluenceMapPixelSize', [0.2]); %in unit of cm
            if numel(pixdim)<2
                pixdim = [pixdim pixdim]; 
            end
            
            dim = options.getoptioni('FluenceMapDim', 200); 
            if numel(dim)<2
                dim = [dim dim]; 
            end
            obj.x_dim = dim(1); 
            obj.y_dim = dim(2); 
            obj.x_pixdim = pixdim(1); 
            obj.y_pixdim = pixdim(2); 
            obj.alignXYCenter; 
            obj.reverseY = false; 
        end
    end
end

