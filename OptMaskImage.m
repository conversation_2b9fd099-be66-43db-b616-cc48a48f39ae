classdef OptMaskImage < ROIMaskImage   
    properties
        
    end
    
    methods
        function self = OptMaskImage(varargin)
            self = self@ROIMaskImage(varargin{:}); 
            self.imageType = 'optmask';
            self.setShort();
        end
        
        function flag =  isTargetROI(self,index)
            if ischar(index)
                %index = find(ismember(self.ROINames, index)); 
                index = self.getROIIndex(index);
            end
            flag =  index>=256; 
        end
        
        function [targetnames, targetindexs] = TargetNames(self)
             indexs =ROIIndexs(self);
             names = ROINames(self);
             I=indexs>=256; 
             targetnames = names(I);  
             targetindexs = indexs(I);
        end
        
        function [res, targets, names] = NumberOfTargets(self)
            res = 0;  targets = [];
            for k=1:numel(self.rois)
                index = self.rois{k}.ROIIndex;
                if index>=256
                    res = res+1; 
                    targets(res) = index;
                    names{res}   = self.rois{k}.ROIName; 
                end
            end
        end
        
        function [res, oars, names] = NumberOfOARs(self)
%             res = numel(self.rois) - NumberOfTargets(self);
            res = 0;  oars = [];
            for k=1:numel(self.rois)
                index = self.rois{k}.ROIIndex;
                if index<256
                    res = res+1; 
                    oars(res) = index;
                    names{res}   = self.rois{k}.ROIName; 
                end
            end
        end
        
%         function mask = RemoveROI(self, name)
%             if ischar(name)
%                 k = find(ismember(self.ROINames, name));
%                 index = self.rois{k}.ROIIndex;
%             elseif isnumeric(name)
%                 index = name; 
%                 
%             end
%             
%             mask = self.getROIMask(index);
%             self.rois(k)=[]; 
%             self.data = self.data-index.*mask; 
%         end
        
        
        
        function mask = getPTVImage(obj)
            mask = VolHeaderImage(obj); 
            mask.data(:) = 0; 
            maskdata = zeros(size(mask.data)); 
            for k=1:NumberOfRois(obj)
                index = obj.rois{k}.ROIIndex; 
                if isTargetROI(obj,index)
                    maskdata = maskdata | obj.getROIMask(index);
                end
            end
            mask.setData(maskdata); 
        end
        
        function mask = getOARImage(obj)
            mask = VolHeaderImage(obj); 
            mask.data(:) = 0; 
            maskdata = zeros(size(mask.data)); 
            for k=1:NumberOfRois(obj)
                index = obj.rois{k}.ROIIndex; 
                if ~isTargetROI(obj,index)
                    maskdata = maskdata | obj.getROIMask(index);
                end
            end
            mask.setData(maskdata); 
        end
        
        function res = CreateMergeMask(obj, varargin)
            options = OptionsMap(varargin{:}); 
            mergetarget = options.getoptioni('mergetarget', 1); 
            mergeoar = options.getoptioni('mergeoar', 1);
            [~, targets, targetnames] = NumberOfTargets(obj);
            [~, oars, oarnames] = NumberOfOARs(obj);
            ptvmask = getPTVImage(obj);
            oarmask = getOARImage(obj);
            if mergetarget && mergeoar
                res = obj.GetSubMaskImage({});
                res.AddNewROI(ptvmask.data, 'tumor', 1);
                res.AddNewROI(oarmask.data, 'oar', 0);
            elseif mergetarget && ~mergeoar
                res = obj.GetSubMaskImage(oarnames, zeros(size(oarnames)));
                res.AddNewROI(ptvmask.data, 'tumor', 1);
            elseif ~mergetarget && mergeoar
                res = obj.GetSubMaskImage(targetnames, zeros(size(targetnames)));
                res.AddNewROI(oarmask.data, 'oar', 0);
            end
        end
        
        
        function res = GetTargetROIMaskImage(obj)
            %res = ROIMaskImage(obj); res.imageType = 'optmaskimage';
            res = OptMaskImage(obj);
            res.data = floor(res.data/256); 
            rois0 = res.rois; m=0; 
            for k=1:numel(rois0)
                if rois0{k}.ROIIndex>=256
                    m=m+1; 
                    rois{m} = rois0{k};
                    rois{m}.ROIIndex = floor(rois{m}.ROIIndex/256); 
                end
            end
            res.rois = rois; 
        end
        
        function res = GetOARROIMaskImage(obj)
            %res = ROIMaskImage(obj); res.imageType = 'optmaskimage';
            res = OptMaskImage(obj);
            res.data = mod(res.data, 256); 
            rois0 = res.rois; m=0; 
            for k=1:numel(rois0)
                if rois0{k}.ROIIndex<256
                    m=m+1; 
                    rois{m} = rois0{k};
                end
            end
            res.rois = rois; 
        end
        
        function mask = getROIMask(obj, index)
            mask =[]; 
            if ischar(index)
                index = obj.getROIIndex(index); 
            end
            
            if isempty(index)
                return
            end
            
            if index>=256
                mask = floor(obj.data/256)== floor(index/256); 
            else
                mask = mod(obj.data, 256)==index; 
            end
        end
        

        
%         ROIIndex = 10
%         ROIName = Fiducials
%         ROIColor = [4.980392e-01     8.235294e-02     8.235294e-02]
%         RTROIInterpretedType = AVOIDANCE
%         ROIPhysicalProperty =
%         ROIPhysicalPropertyValue =

        function AddNewROI(self, mask, name, istarget, varargin)
            options = OptionsMap(varargin{:});
            targetoaroverlap = options.getoptioni('targetoaroverlap', 1); 
            numrois  = NumberOfRois(self); 
            roicolors = distinguishable_colors(64); 
            roi = options.getoptioni('roi'); 
            
            if istarget
               %index =  NumberOfTargets(self)+1; 
               if isempty(roi)
               index =  options.getoptioni_numeric('ROIIndex', NumberOfTargets(self)+1); 
               roi = struct('ROIIndex', index*256, 'ROIName', name); 
               roi.('ROIColor') = options.getoptioni('roi.color', roicolors(mod(index, 64)+1, :)); 
               else
                   index = roi.ROIIndex; 
                   roi.ROIIndex=index*256; 
               end
               
               self.rois{numrois+1} = roi;
               %self.data(mask) = mod(self.data(mask), 256) + index*256;
               if targetoaroverlap
                   self.data(mask) = mod(self.data(mask), 256) + index*256;
               else
                   self.data(mask) =  index*256;
               end
            else
               if isempty(roi)
               index =  options.getoptioni('ROIIndex', NumberOfOARs(self)+1); 
               roi = struct('ROIIndex', index, 'ROIName', name); 
               roi.('ROIColor') = options.getoptioni('roi.color', roicolors(mod(index, 64)+1, :)); 
               else
                   index = roi.ROIIndex; 
               end
               
               self.rois{numrois+1} = roi;
               %self.data(mask) = index;
               if targetoaroverlap
                    self.data(mask) = floor(self.data(mask)/256)*256+index;
               else
                   self.data(mask) =  index;
               end
            end
        end
        

        
        
        function roimask = GetSubMaskImage(self, names, targets)
            if ischar(names)
                names = strsplit(names, '|'); 
            end
            N = numel(names);  
            if ~exist('targets', 'var') || isempty(targets)
                targets = zeros(1, N); 
            end
            vh =VolHeader(self); 
            roimask = OptMaskImage(vh); 
            roimask.setData(zeros(size(self.data))); 
            for n=1:N
                name = names{n}; 
                roimask.AddNewROI(self.getROIMask(name), name, targets(n))
            end
        end
        
        function AddFromOptMask(self, srcmaskimg)
            roinames = self.ROINames;
            srcrois = srcmaskimg.rois; 
            for k=1:numel(srcrois)
                roi = srcrois{k};
                roiname = roi.ROIName;
                if ismember(roiname, roinames)
                    continue;
                else
                     index   = roi.ROIIndex;
                     istarget = index>=256;
                     mask = srcmaskimg.getROIMask(index);
                     self.AddNewROI(mask, roiname, istarget);
                end
            end
        end
        
        function roimask = toROIMask(self)
            roimask = ROIMaskImage(self); 
            numrois =  NumberOfRois(self); 
            numoars =  NumberOfOARs(self);
            roimask.data(:)=0; 
            for k=1:numrois
                index = roimask.rois{k}.ROIIndex; 
                mask = self.getROIMask(index);
                if index>=256
                    index = floor(index/256)+numoars;
                end
                roimask.data = roimask.data + mask*2^(index-1);
                roimask.rois{k}.ROIIndex = index; 
            end
            roimask.imageType = 'roimask'; 
            roimask.setIntType();
        end
        
        function [targetROINames,organROINames]= fromRTStruct(obj, rs, varargin)
            if ischar(rs) && exist(rs, 'file')
                rs =  RtStruct2(rs, obj);
            end
            vhrs = rs.GetVolHeader();
            if abs(vhrs.z_pixdim-obj.z_pixdim)<1e-5
                [targetROINames,organROINames]= obj.fromRTStruct0(rs, varargin{:});
            else
                roimask = OptMaskImage(vhrs);
                [targetROINames,organROINames]= roimask.fromRTStruct0(rs, varargin{:});
                roimask.ReformCS(obj);
                obj.setData(roimask.data);
                obj.rois = roimask.rois; 
            end
        end
        
        function [targetROINames,organROINames]= fromRTStruct0(obj, rs, varargin)
            if ischar(rs) && exist(rs, 'file')
                rs =  RtStruct2(rs, obj);
            end
            
            options = OptionsMap(varargin{:});
            targettypes     = options.getoptioni('targetroitypes', {'PTV', 'GTV', 'CTV'}); 
            if ~isempty(targettypes)
            if options.isoptioni('TargetROINames')
                targetROINames  = options.getoptioni('TargetROINames');
            else
                targetROINames = rs.getROINames_InterpretedTypes(targettypes);
            end
            
            if options.isoptioni('OrganROINames')
                organROINames   = options.getoptioni('OrganROINames');
            else
                organtypes      = options.getoptioni('organroitypes', {'ORGAN', 'EXTERNAL'}); 
                organROINames  = rs.getROINames_InterpretedTypes(organtypes);
            end
            

            targetdata      =  obj.AddROIsFromRtStruct(rs, targetROINames,1, varargin{:});
            
            oardata         = obj.AddROIsFromRtStruct(rs, organROINames, 0, varargin{:});
            
            obj.setData(targetdata+oardata); 
            else % do not differentiate target and oar
                roinames = rs.ROINames; 
                oardata  = obj.AddROIsFromRtStruct(rs, roinames, 0, varargin{:});
                obj.setData(oardata); 
            end
        end
        
%         function ReplaceRoiMask(self, name, newmask)
%             [~, loc] = ismember(name, self.ROINames);
%             if loc==0 
%                 return;
%             end
%             index = self.rois{loc}.ROIIndex;
%             mask = self.getROIMask(index);
%             diff = newmask-mask;
% %             self.data = self.data+ cast(diff*2^(index-1), self.data_type);
%             self.data = self.data + diff*index;
%         end
        
        
        
         function maskimg = GetSampleMaskImage(self, varargin)
            options   = OptionsMap(varargin{:});
            targetrate = options.getoptioni('samplerate.target', 2); 
            oarrate = options.getoptioni('samplerate.oar', 2); 
            bodyrate = options.getoptioni('samplerate.body', 4); 
            bodyindex = options.getoptioni('index.body', 1); 
            [X, Y, Z] = meshgrid(0:(self.x_dim-1), 0:(self.y_dim-1), 0:(self.z_dim-1)); 
            mask = zeros(size(self.data)); 
            [numtargets, targets] = NumberOfTargets(self);
            
            for m=1:numel(targets)
                rate = targetrate; 
                mask0= self.getROIMask(targets(m));
                I = mask0 & (mod(X, rate)==0) & (mod(Y, rate)==0) & (mod(Z, rate)==0); 
                mask(I) = 1; 
            end
            
            [numoars, oars] = NumberOfOARs(self);
            for m=1:numel(oars)
                index = oars(m);
                rate = oarrate;
                if index == bodyindex
                    rate = bodyrate;
                end
                mask0   = self.getROIMask(oars(m));
                I = mask0 & (mod(X, rate)==0) & (mod(Y, rate)==0) & (mod(Z, rate)==0); 
                mask(I) = 1; 
            end
            maskimg = VolHeaderImage(self); 
            maskimg.setData(mask); 
            maskimg.setInt();
         end
        
         function AddROIShell(obj, roiname, shell, shellname)
              roimask    = obj.getROIMask(roiname);
              shellmask  = ROIMaskImage.GetExteriorSurfaceMask(roimask, shell);
              obj.AddNewROI(shellmask, shellname, 0);
         end
         
         function AddTargetShell(obj, shell, shellname)
             bw = obj.data>=256;
             shellmask  = ROIMaskImage.GetExteriorSurfaceMask(bw, shell);
             obj.AddNewROI(shellmask, shellname, 0);
         end
    end
    
    
    methods (Access=private)
         function maskdata = ModifyMaskDataWithNewROI(self, maskdata, mask, roi, istarget, varargin)
                options  = OptionsMap(varargin{:});
                numrois  = NumberOfRois(self); 
                roi1 = struct('ROIIndex', roi.ROIIndex,...
                        'ROIName', roi.ROIName,...
                        'ROIColor', roi.ROIColor,...
                        'RTROIInterpretedType',   roi.RTROIInterpretedType,...
                        'ROIPhysicalProperty',    roi.ROIPhysicalProperty, ...
                        'ROIPhysicalPropertyValue', roi.ROIPhysicalPropertyValue); 
                
                appendindex = options.getoptioni_numeric('useappendindex', 1); 
                if appendindex
                    if istarget
                       roiindex =  (NumberOfTargets(self)+1)*256; 
                    else
                       roiindex =  NumberOfOARs(self)+1; 
                    end
                else
                     if istarget
                       roiindex =  roi.ROIIndex*256; 
                    else
                       roiindex =  roi.ROIIndex; 
                    end
                end
               roi1.ROIIndex = roiindex;
               self.rois{numrois+1} = roi1;
               maskdata(mask) = roiindex;
         end
        
          function maskdata = AddROIsFromRtStruct(self, rs, targetROINames, istarget, varargin)
            maskdata = zeros([self.y_dim self.x_dim, self.z_dim]); 
            
            if isempty(targetROINames)
                return;
            end
            options   = OptionsMap(varargin{:});
            roiindices= options.getoptioni_numeric('dstroiindex', []);
            for m=1:numel(targetROINames)
                try
                name = targetROINames{m};
                rois{m} = rs.rois(name); 
                
                if ~isempty(roiindices)
                    rois{m}.ROIIndex = roiindices(m);
                end
                
                targetmask{m} = self.RtContours2Mask(rois{m}.zContours);
                targetvolume(m)  = sum(targetmask{m}(:)); 
                catch err
                    disp(['error: ' name]);
                end
            end
             
            options = OptionsMap(varargin{:});
            tosort = options.getoptioni('tosort', 1); 
            
            if tosort
                [targetvolume, I] = sort(targetvolume, 'descend');
                targetmask = targetmask(I); 
                targetROINames = targetROINames(I);
                rois = rois(I);
            end
            
            useappendindex = options.getoptioni('useappendindex', isempty(roiindices));
            for m=1:numel(targetROINames)
                roi = rois{m};
                maskdata = self.ModifyMaskDataWithNewROI(maskdata, targetmask{m}, roi, istarget, {'useappendindex', useappendindex});
            end
%             self.data = self.data + maskdata; 
          end
        
        
    end
    
    methods (Static)
        function self = fromVolHeaderImage(vhimg, names, srcvalues, dstindex)
            self   = OptMaskImage(vhimg); 
            self.data(:)=0;
            if ~exist('dstindex', 'var') || isempty(dstindex)
                dstindex = srcvalues; 
            end
            for k=1:numel(names)
%                 rois{k} = struct('ROIIndex', values{k},...
%                     'ROIName', name,...
%                     'ROIColor', [],...
%                     'RTROIInterpretedType',   [],...
%                     'ROIPhysicalProperty',    [], ...
%                     'ROIPhysicalPropertyValue', []); 
                mask = vhimg.data==srcvalues(k); 
                self.AddNewROI(mask, names{k}, 0, {'ROIIndex', dstindex(k)});
            end
            %SetDistinguishableColors(self);
        end
        
        function self = fromROIMaskImage(roimask, targetnames, oarnames)
            self = OptMaskImage(roimask); 
            self.data(:) = 0;
            self.rois={};
            numrois = 0; 
            targetdata = zeros(size(roimask.data)); 
            for k=1:numel(targetnames)
                name = targetnames{k}; 
                index= find(ismember(roimask.ROINames, name));
                if ~isempty(index)
                    mask = roimask.getROIMask(name); 
                    numrois = numrois+1; 
                    self.rois{numrois} = roimask.rois{index};
                    self.rois{numrois}.ROIIndex = k*256; 
                    val = mask*k*256; 
                    I   = mask>0; 
                    targetdata(I) = val(I);
                end
            end
            
            
            oardata = zeros(size(roimask.data)); 
            for k=1:numel(oarnames)
                name = oarnames{k}; 
                index= find(ismember(roimask.ROINames, name));
                if ~isempty(index)
                    mask = roimask.getROIMask(name); 
                    numrois = numrois+1; 
                    self.rois{numrois} = roimask.rois{index};
                    self.rois{numrois}.ROIIndex = k; 
                    val = mask*k; 
                    I   = mask>0; 
                    oardata(I) = val(I);
                end
            end
            self.data = targetdata + oardata; 
           self.setDataType('int'); 
        end
    end
end

