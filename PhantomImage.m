classdef PhantomImage < CTImage
    
    properties
        
    end
    
    methods
        function density = PhantomImage(varargin)
            density = density@CTImage;
            options = OptionsMap(varargin{:});
            density.x_pixdim=options.getOption('x_pixdim', .2); 
%             density.y_pixdim=options.getOption('y_pixdim', .1);
            density.y_pixdim=options.getOption('y_pixdim', .2);
            density.z_pixdim=options.getOption('z_pixdim', .2); 
            x_dim = options.getOption('x_dim', 200); 
%             y_dim = options.getOption('y_dim', 320); 
            y_dim = options.getOption('y_dim', 200); 
            z_dim = options.getOption('z_dim', 200); 
            densityVal = options.getOption('DensityValue', 1); 
            density.setData(ones(y_dim, x_dim, z_dim)*densityVal); 
            density.setFloat(); 
            density.alignXYZCenter; 
            density.reverseY=0; 
        end
        
        
        function roimask_cube(self, X, Y, Z)
            xData = self.xData; yData = self.yData; zData = self.zData; 
            [x, y, z] = meshgrid(xData, yData, zData);
            I = x<X(1) | x>X(2) | y<Y(1) | y>Y(2) | z<Z(1) | z>Z(2);
            self.data(I)=0; 
        end
        
        function compareProfiles(self, profs, varargin)
%             plotSlab_1D(self, 1.1); hold on; 
            ProfileData.compareProfiles(profs, varargin{:}); 
        end
    end
    
    methods (Static)
        function tt = MatTable()
            material = {'water', 'muscle', 'bone', 'lung', 'adipose', 'cork', 'air', 'halfwater'}'; 
            ctnum    = [1000 1024 2658 266 920 298 1 500]';
            density    = [1 1.024 1.5 .266 .920 .298 .001 .500]';
            symbol   = {'W', 'M', 'B', 'L', 'A', 'C', 'a', 'H'}';
            tt = table(material, ctnum, symbol, density);
            tt.Properties.VariableNames={'material', 'ctnum', 'symbol', 'density'}; 
            tt.Properties.RowNames = material; 
        end
               
        function str = MatRegexStr
            tt = PhantomImage.MatTable; 
            material = tt.material; 
            str = ['/\b' material{1} '\b/i']; 
            for k=2:numel(material)
                str0 = ['/\b' material{k} '\b/i'];
                str = [str '|' str0]; 
            end
        end
        
        function ct = createMedistinum(header, w, h, s)
            if ~exist('s', 'var')
                s = 1/8; 
            end
            
            if ~exist('w', 'var')
                w = 1/4; 
            end
            
            if ~exist('h', 'var')
                h = 1/2; 
            end
            
            M = header.x_dim; N = header.y_dim; 
            x  = round(1:(w*M)); y = round(1:(h*M));
            x0 = round((.5-s-w)*M); x1 = round((.5+s)*M);
            y0 = round((1/2-h/2)*M); 
            
            CT = CTPhantom.CTNumber; 
            A = zeros(header.x_dim, header.y_dim);
           
            A(:, :) = CT.water; 
            A(y0+y, x0+x) = CT.cork; 
            A(y0+y, x1+x) = CT.cork; 
            
            ct = VolHeaderImage(header); 
            ct.data = repmat(A, [1, 1, ct.z_dim]);
            ct.setShort; 
        end
        
       
        
        function plotMedistinum_2D(w, h, M,s, varargin)
            if ~exist('s', 'var')
                s=1/8;
            end
            
            symbol = CTPhantom.matSymbol;
            x0 = round((.5-s-w)*M); x1 = round((.5+s)*M);
            y0 = round((1/2-h/2)*M); 
            xrange = round([1 w*M]); 
            yrange = round([1 h*M]);
            
            X = x0+[xrange(1) xrange(2) xrange(2) xrange(1)]; 
            Y = y0+[yrange(1) yrange(1) yrange(2) yrange(2)]; 
            CTPhantom.plotPolygon(X, Y, varargin{:});
            text(mean(X), mean(Y), symbol.cork);
            
            X = x1+[xrange(1) xrange(2) xrange(2) xrange(1)]; 
            CTPhantom.plotPolygon(X, Y, varargin{:});
            text(mean(X), mean(Y), symbol.cork);
            text(M/2, M-10, symbol.water);
            CTPhantom.setTicks(gca, M); 
        end
        
 
        
        function plotPolygon(X, Y, varargin)
             N = length(X); 
             X = [X X(1)]; 
             Y = [Y Y(1)]; 
             for k=1:N
                 line([X(k),  X(k+1)], [Y(k),  Y(k+1)], varargin{:});
             end
        end
        
        
    end
    
    
end

