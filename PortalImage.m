classdef PortalImage < IntensityMapImage
    %UNTITLED Summary of this class goes here
    %   Detailed explanation goes here
    % PortalImage follows IEC XRay image receptor coordinate system 
    properties
        sad=100;
        sid;  
        MetersetExposure;
%         GantryAngle;
%         %collrtn;
%         BeamLimitingDeviceAngle;
%         PatientSupportAngle;
%         
%         XJAWPositions; 
%         YJawPositions; 
        %PatientName;
        PatientID;
    end
    
    methods
        function obj = PortalImage(varargin)
            %obj = obj@VolHeaderImage(varargin{:});
            obj = obj@IntensityMapImage(varargin{:});
            %obj.reverseY = false; 
            if isempty(obj.sad)
                obj.sad = 100; %set default
            end
        end
        
        function info = readDicomInfo(obj, fname)
            obj.LinearMeasureUnit = 'cm'; 
            info = obj.readDicomInfo@VolHeader(fname);
            obj.MetersetExposure = StructBase.getfieldx(info, 'ExposureSequence.Item_1.MetersetExposure'); 
            obj.copyStructField(info, 'sad', 'RadiationMachineSAD', 0.1);
            obj.copyStructField(info, 'sid', 'RTImageSID', 0.1);
            obj.copyStructField(info, 'x_pixdim', 'ImagePlanePixelSpacing', 0.1, 1);
            obj.copyStructField(info, 'y_pixdim', 'ImagePlanePixelSpacing', 0.1, 2);      
            obj.copyStructField(info, 'translation', 'XRayImageReceptorTranslation', 0.1); 
            %obj.copyStructField(info, 'rotation', 'XRayImageReceptorAngle'); 
            rotAngle = StructBase.getfieldx(info, 'XRayImageReceptorAngle');
            if ~isempty(rotAngle)
                obj.rotation(3)=rotAngle;
            end
            
            BeamLimitingDeviceSequence=StructBase.getfieldx_recursive(info, 'BeamLimitingDeviceSequence');
            if ~isempty(BeamLimitingDeviceSequence) && isstruct(BeamLimitingDeviceSequence)
                fn = fieldnames(BeamLimitingDeviceSequence);
                for k=1:numel(fn)
                    item = BeamLimitingDeviceSequence.(fn{k}); 
                    devType = StructBase.getfieldx(item, 'RTBeamLimitingDeviceType'); 
                    devType = RTBeamInfo.convertRTBeamLimitingDeviceType(devType);
                    switch devType
                        case 'XJAW'
                           obj.XJawPositions =  StructBase.getfieldx(item, 'LeafJawPositions')/10; 
                        case 'YJAW'
                           obj.YJawPositions =  StructBase.getfieldx(item, 'LeafJawPositions')/10; 
                    end
                end
            end
        end
        
        function info = readDicomImage(obj, fname)
            info = obj.readDicomInfo(fname);
            obj.data = double(dicomread(fname));
            if isfield(info, 'RescaleSlope') && isfield(info, 'RescaleIntercept')
                obj.data = obj.data*info.RescaleSlope+info.RescaleIntercept;
            end
            
            try
            if info.RTImagePosition(2)>0
                obj.reverseY=true; 
                %obj.data=flipdim(obj.data, 1);
                %obj.reverseY=0; 
            end
            catch
            end
            
            obj.alignImageData; 
            
            %project data onto isocenter plane
            if ~isempty(obj.sad) && ~isempty(obj.sid)
                scale = obj.sad/obj.sid; 
                obj.x_pixdim=obj.x_pixdim*scale; 
                obj.y_pixdim=obj.y_pixdim*scale; 
                if numel(obj.translation)>=2
                    obj.translation(1:2)=obj.translation(1:2)*scale; 
                end
                obj.alignXYCenter; 
            end
        end
        
        function readCXF(obj, fname, pixdim, toAverage)
            [header, data] = ImageUtils.hdrload(fname);
            [header] = ImageUtils.ini2struct(header);
            obj.setData(data); 
            obj.x_pixdim = header.geometry.res1/10;
            obj.y_pixdim = header.geometry.res2/10;
            obj.sid = header.portaldose.sid; 
            obj.sad = header.field.sad; 
            obj.GantryAngle = header.field.gantryangle;
            obj.BeamLimitingDeviceAngle = header.field.collrtn; 
            obj.reverseY = false; 
            obj.alignXYCenter; 
            
            if nargin>=3
                if exist('toAverage', 'var') && toAverage>0
                    Sx = round(pixdim(1)/obj.x_pixdim); 
                    Sy = round(pixdim(2)/obj.y_pixdim); 
                    H = ones(Sy, Sx)/(Sx*Sy);
                    obj.data = filter2( H, obj.data, 'same'); 
                end
                obj.resample(pixdim); 
            end
        end
        
        function readxim(obj, fname)
            [pixels, imageObj] = ximread(fname);
            obj.setData(double(pixels)); 
            obj.setFloat(); 
            obj.x_pixdim = imageObj.PixelWidth;
            obj.y_pixdim = imageObj.PixelHeight;
            obj.sid = obj.sad - imageObj.MVDetectorVrt;
            obj.XJawPositions=[-imageObj.MVCollimatorX1 imageObj.MVCollimatorX2];
            obj.YJawPositions=[-imageObj.MVCollimatorY1 imageObj.MVCollimatorY2];
            obj.alignXYCenter; 
            obj.LinearMeasureUnit='cm'; 
        end
        

        
%         function readFile(obj, fname)
%             obj.readFile@VolHeaderImage(fname); 
%             header = parseTextFile([fname '.header']); 
%             obj.copyStruct(header); 
%         end
        
%         function writeFile(obj, fname)
%             obj.writeFile@VolHeaderImage(fname);
%             header = obj.header; 
%             header.sad=obj.sad; 
%             header.sid=obj.sid; 
%             header.MetersetExposure=obj.MetersetExposure;
%             header.GantryAngle=obj.GantryAngle;
%             header.BeamLimitingDeviceAngle=obj.BeamLimitingDeviceAngle;
%             header.PatientSupportAngle=obj.PatientSupportAngle;
%             header.PatientID = obj.PatientID; 
%             header.XJawPositions = obj.XJawPositions;
%             header.YJawPositions = obj.YJawPositions;
%             writeStruct2File([fname '.header'],  header); 
%         end
        
        %the angle is the BeamLimitingDeviceAngle in degrees
        function rotate(obj, angle)
            if ~obj.reverseY
                angle = -angle; 
            end
            obj.data = imrotate(obj.data, angle, 'bilinear', 'crop'); 
        end
        
        
        function setJawPositions(obj, FX, FY)
            if exist('FX', 'var')
                obj.XJawPositions=FX; 
            end
            if exist('FY', 'var')
                obj.YJawPositions=FY; 
            end
        end
        
        function [FX, FY]= getMaxJawPositions(obj)
            FX=[]; FY=[]; 
            if numel(obj.XJawPositions)>=2
                FX = [min(obj.XJawPositions(:, 1)) max(obj.XJawPositions(:, 2))]; 
            end
            if numel(obj.YJawPositions)>=2
                FY = [min(obj.YJawPositions(:, 1)) max(obj.YJawPositions(:, 2))]; 
            end
        end
        
        function [cx, cy] = getFieldCenter(obj)
            cx=0; cy=0;
            [FX, FY]= getMaxJawPositions(obj);
            if numel(FX)>=2 
                cx = (FX(2)+FX(1))/2.0; 
            end
            if numel(FY)>=2 
                cy = (FY(2)+FY(1))/2.0; 
            end
            c = obj.rotateBeamLimitingDeviceCoor([cx; cy]);
            cx=c(1); cy = c(2); 
        end
        
        function [fx, fy] = getMaxFieldWidth(obj)
            fx = []; fy = []; 
            if numel(obj.XJawPositions)>=2
                fx = max(obj.XJawPositions(:, 2)-obj.XJawPositions(:, 1)); 
            end
            if numel(obj.YJawPositions)>=2
                fy = max(obj.YJawPositions(:, 2)-obj.YJawPositions(:, 1)); 
            end
        end
        
        function val = getFieldCenterValue(obj, varargin)
            [cx, cy] = getFieldCenter(obj);
            val = getValueAtPos(obj, [cx, cy], varargin{:}); 
        end
        
        function meanVal = getMeanOffAxisValues(obj, radius, varargin)
            for k=1:numel(radius)
                r=radius(k); 
                meanVal(k) = getMeanOffAxisValue(obj,r, varargin{:});
            end
        end
        
        function meanVal = getMeanOffAxisValue(obj, radius, varargin)
            options = OptionsMap(varargin{:}); 
            center = options.getOption('FieldCenter', [0 0]); 
            if radius<1e-3
                meanVal = getValueAtPos(obj, center, 1); 
                return; 
            end
            numSamples = options.getOption('NumberOfSamples', 20); 
%             
%             dr = options.getOption('SampleDistance', 0.5); %1cm
%             dt = dr/radius; 
%             theta=0:dt:2*pi; 
            theta = linspace(0, 2*pi, numSamples); 
            for k=1:numel(theta)
                cx = center(1)+radius*cos(theta(k));
                cy = center(2)+radius*sin(theta(k));
                val(k) = getValueAtPos(obj, [cx, cy], 1); 
            end
            meanVal = mean(val); 
        end
        
        function [poly, bw, Xi, Yi] = calcFieldBox(obj, PenumbraExtension, RISize0)
            poly=[]; bw=[]; Xi=[]; Yi=[];
            
            if ~exist('PenumbraExtension', 'var')
                PenumbraExtension=0; 
            end
            
            [FX, FY]= obj.getMaxJawPositions;
            if numel(FX)<1 || numel(FY)<1
                return; 
            end
                          
            FX(1) = FX(1)-PenumbraExtension; FX(2) = FX(2)+PenumbraExtension; 
            FY(1) = FY(1)-PenumbraExtension; FY(2) = FY(2)+PenumbraExtension;  
            poly = [FX(1) FX(2) FX(2) FX(1); FY(1) FY(1) FY(2) FY(2)]; 
%             angle = StructBase.getfieldx(obj,'BeamLimitingDeviceAngle')*pi/180; 
%             if abs(angle)>1e-5
%                 R = [cos(angle), -sin(angle); sin(angle) cos(angle)];
%                 for k=1:4
%                     poly(:, k) = R*poly(:, k);
%                 end
%             end
            poly = obj.rotateBeamLimitingDeviceCoor(poly);
            
            if exist('RISize0', 'var')
                RISize = RISize0*obj.sad/obj.sid/2; 
                for k=1:2
                    for m=1:4
                        poly(k, m) = min(max(poly(k, m), -RISize(k)), RISize(k)); 
                    end
                end
            end
            
            Xi = obj.physics2image(poly(1, :), 'x'); 
            Yi = obj.physics2image(poly(2, :), 'y'); 
            Xi = [Xi Xi(1)]; 
            Yi = [Yi Yi(1)];
            bw = poly2mask(Xi, Yi, obj.y_dim, obj.x_dim); 
        end
        
        function [poly, X, Y, bw] = getPortalBox(obj, portalSize)
            if ~exist('portalSize', 'var')
                portalSize = [40, 30]*obj.sad/obj.sid; 
            end
            FX(1) = -portalSize(1)/2; FX(2) = -FX(1); 
            FY(1) = -portalSize(2)/2; FY(2) = -FY(1); 
            poly = [FX(1) FX(2) FX(2) FX(1); FY(1) FY(1) FY(2) FY(2)]; 
%             poly = obj.rotateBeamLimitingDeviceCoor(poly);
            X = obj.physics2image(poly(1, :), 'x'); 
            Y = obj.physics2image(poly(2, :), 'y'); 
            X=[X X(1)]; 
            Y=[Y Y(1)];
            bw = poly2mask(X, Y, obj.y_dim, obj.x_dim); 
        end
        
        function coor = rotateBeamLimitingDeviceCoor(obj, coor0)
            coor = coor0; 
            angle = StructBase.getfieldx(obj,'BeamLimitingDeviceAngle')*pi/180; 
            if abs(angle)>1e-5
                R = [cos(angle), -sin(angle); sin(angle) cos(angle)];
                coor = R*coor;
            end
        end
        
        function resetSID(obj, refSID)
            if isempty(obj.sid)
                return; 
            end
            obj.data = obj.data*(obj.sid/refSID).^2; %inverse square
            obj.sid  = refSID; 
            obj.translation(3) = obj.sad-obj.sid;
        end
        
        function showFieldBox(portalDose, axH, volHeader)
            if ~exist('volHeader', 'var')|| isempty(volHeader)
                volHeader=portalDose; 
            end
%             [~, ~, X, Y] = portalDose.calcFieldBox;
%             line(X, Y, 'Color', 'y', 'parent', axH); 
            [poly] = portalDose.calcFieldBox;
            volHeader.showPolyBox(poly, axH, 'Color', 'y'); 
        end
        
        function showPortalBox(portalDose, axH, volHeader)
            if ~exist('volHeader', 'var') || isempty(volHeader)
                volHeader=portalDose; 
            end
             showFieldBox(portalDose, axH, volHeader);
		     %[~, pvX, pvY] = portalDose.getPortalBox;
             %line(pvX, pvY, 'Color', 'c', 'parent', axH); 
             poly = portalDose.getPortalBox;
             volHeader.showPolyBox(poly, axH, 'Color', 'c'); 
        end
        
        function stat = PortalStat(portal, bw)
            if ~exist('bw', 'var') || isempty(bw)
                [poly, bw] =  portal.calcFieldBox(0, [40 30]); 
            end
            stat     = GammaAnalysis.ImageStat(portal.data(bw));  
            stat.sid = portal.sid; 
            stat.CAX = portal.getFieldCenterValue;
        end
        
        function smooth(obj, winSize)
            % This function performs a smooth operation by averaging pixels
            % in a given window size. winSize has a unit of cm, and its
            % first and second components are y and x, respectively, to
            % agree with the data matrix.
            if numel(winSize)==1
                winSize = [winSize winSize]; 
            end
            n=max([1 1],2*ceil((winSize./[obj.y_pixdim obj.x_pixdim]-1)/2)+1);
            avgK=ones(n)/(n(1)*n(2));
            obj.data=conv2(obj.data,avgK,'same');
        end
        
        function showImage(obj, varargin)
            obj.show; 
            axH = get(gcf,'CurrentAxes');
            set(axH, 'visible', 'on');
            set(axH, 'YDir', 'normal');
            
            LABEL={'X', 'Y'};
            for k=1:2
                [tick{k}, ticklabel{k}] = obj.getAxesTick(LABEL{k}, 5);
            end
            set(axH, 'XTick',   tick{1}, 'XTickLabel', ticklabel{1});  
            set(axH, 'YTick',   tick{2}, 'YTickLabel', ticklabel{2});   
            xlabel([LABEL{1} ' (cm)'] ); 
            ylabel([LABEL{2} ' (cm)'] ); 
            if numel(varargin)>=1
                set(axH, varargin{:});
            end
            try
            showPortalBox(obj, axH);
            catch err
            end
        end
        
        
        % Med Phys. 2010 Jun;37(6):2425-34. Berry SL, Polvorosa CS, Wuu CS.
        % A field size specific backscatter correction algorithm for accurate EPID dosimetry.
        function result = applyBackScatterCorrection(obj, varargin)
            result = 0; 
            if isempty(obj.YJawPositions)
                return; 
            end
            Y1 = obj.YJawPositions(1); 
            if Y1>0; return; end
            Y1 = abs(Y1); 
            
%             m = 2.87e-4 *Y1^4 -1.14e-2 *Y1^3 + 1.65e-1 * Y1^2 - 1.12*Y1 + 3.42; 
%             m(m<0) = 0; 

            options = OptionsMap(varargin{:}); 
            beamEnergy = options.getOption('BeamEnergy', 6); 
            
            switch beamEnergy
                case 6
                    m = 3.0427*exp(-0.25851*Y1); 
%                      m = 4.0427*exp(-0.25851*Y1); 
                otherwise
                    m = 1.6394*exp(-0.23758*Y1);
            end
            
            yData = obj.yData; 
            cf = 1+ yData*m/100;
            cf(yData>0) = 1; 
            obj.data = obj.data .* repmat(cf', [1, obj.x_dim]); 
            result = 1;
        end
    end
    
    methods (Static)
        %read sequence images into one file
         function tmpimg = readxims(folder)
            res = DosUtil.rdir(folder, '*.xim');
            fnames = {res(:).name};
            tmpimg = PortalImage;
            tmpimg.readxim(fnames{1}); 
            numimgs = numel(fnames); 
            data = zeros(tmpimg.y_dim, tmpimg.x_dim, numimgs); 
            for k=1:numimgs
                try
                tmpimg.readxim(fnames{k});
                data(:, :, k) = tmpimg.data; 
                catch err
                    disp(['error in read file ' fnames{k}]); 
                end
            end
            tmpimg.setData(data); 
         end
        
         function convertxims(xims, tempdrr, varargin)
%             xims = PortalImage.readxims(folder); 
            options = OptionsMap(varargin{:}); 
            sid = options.getoptioni('sid', 150); 
            sad = options.getoptioni('sad', 100); 
            divfactor = sad/sid; 
            xims.x_pixdim = xims.x_pixdim *divfactor; 
            xims.y_pixdim = xims.y_pixdim *divfactor; 
            xims.alignXYCenter; 
            drr = PortalImage(tempdrr); 
            data = repmat(drr.data, [1 1 xims.z_dim]); 
            drr.setData(data); 
            drr.z_pixdim = xims.z_pixdim; drr.z_start = xims.z_start;  
            xims.reform(drr); 
            xims.data = xims.data*max(drr.data(:))/max(xims.data(:));
            xims.data = flipdim(xims.data, 1); 
         end
         
        function compareImages(imgs, para, varargin)
            K = numel(imgs); 
            for k=1:K
                imgs{k} = PortalImage(imgs{k}); 
            end
            
            if K>=2
                imgs{2}.reform(imgs{1}); 
                imshow_overlay(imgs{1}.data, imgs{2}.data, para);
            else
                imgs{1}.show;
            end
            
            axH = get(gcf,'CurrentAxes');
            set(axH, 'visible', 'on');
            set(axH, 'YDir', 'normal');
            
            LABEL={'X', 'Y'};
            for k=1:2
                [tick{k}, ticklabel{k}] = imgs{1}.getAxesTick(LABEL{k}, 5);
            end
            set(axH, 'XTick',   tick{1}, 'XTickLabel', ticklabel{1});  
            set(axH, 'YTick',   tick{2}, 'YTickLabel', ticklabel{2});   
            xlabel([LABEL{1} ' (cm)'] ); 
            ylabel([LABEL{2} ' (cm)'] ); 
            if numel(varargin)>=1
                set(axH, varargin{:});
            end
        end
        
        function dst = calcSumImage(src0, refSID)
            if exist('refSID', 'var') && ~isempty(refSID)
                for k=1:numel(src0)
                    src0{k}.resetSID(refSID); 
                end
            end
            
            for k=1:numel(src0)
                if ~isEqualDim(src0{1}, src0{k}) || any(src0{k}.translation(1:2) ~=0)
                    src0{k}.reform(src0{1}); 
                end
            end
            
            dst = VolHeaderImage.calcSumImage(src0, @PortalImage); 
            
            %reset the jaw position to be the maximum of all fields
            fn = {'X', 'Y'};
            for m=1:2
                name = fn{m}; 
                Jaw0.(name) = StructBase.getfieldx(src0{1}, [name 'JawPositions']); 
            end
            
            for k=2:numel(src0)
                for m=1:2
                    name = fn{m}; 
                    Jaw.(name) = StructBase.getfieldx(src0{k}, [name 'JawPositions']); 
                    if (Jaw.(name)(1)<Jaw0.(name)(1))
                        Jaw0.(name)(1)=Jaw.(name)(1);
                    end
                    if (Jaw.(name)(2)>Jaw0.(name)(2))
                        Jaw0.(name)(2)=Jaw.(name)(2);
                    end
                end
            end
            
            for m=1:2
                name = fn{m} ; 
                dst.([name 'JawPositions']) = Jaw0.(name); 
            end
        end
        
        
        function tempImage=createTemplateHeader(pixRes, physDim)
            if ~exist('physDim', 'var')
                physDim = [40 40]; 
            end
            if ~exist('pixRes', 'var')
                pixRes = 0.2; 
            end
            
            tempImage=VolHeader; % template image
            tempImage.x_dim=physDim(1)/pixRes;
            tempImage.y_dim=physDim(2)/pixRes;
            tempImage.x_pixdim=pixRes; % cm
            tempImage.y_pixdim=pixRes;
            tempImage.reverseY=0;
            tempImage.alignXYCenter;
        end
        
%         function tempImage=createTemplateImage(varargin)
%             header = PortalImage.createTemplateHeader(varargin{:});
%             tempImage=PortalImage(header);
%         end


    end
    
end

