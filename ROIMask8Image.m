classdef ROIMask8Image <ROIMaskImage   
    properties
        
    end
    
    methods
        function obj = ROIMask8Image(varargin)
            obj@ROIMaskImage(varargin{:});
            obj.imageType='roimask8'; 
        end
        
        function AddRoiMask(self, mask, roiname, varargin)
            roinames = self.ROINames;
            if ismember(roiname, roinames)
                self.ReplaceRoiMask(roiname, mask);
            else
                self.AddNewRoiMask(mask, roiname, varargin{:});
            end
        end
        
        function AddNewRoiMask(self, mask,  name, varargin)
            rois = self.rois; 
            if isempty(rois)
                self.setIntType();
                rois ={};
            end
            
            options = OptionsMap(varargin{:}); 
           
            roiindices = cellfun(@(x)(x.ROIIndex), rois);
            numrois = numel(rois); 
            roi = options.getoptioni('roi');
            if isempty(roi) || ~isstruct(roi)
                index =  numrois+1; 
                index = options.getoptioni('roi.index', index);  
                if ~exist('name', 'var')
                    name = num2str(index);
                end
                if numrois==0
                    roi = struct('ROIIndex', index, 'ROIName', name); 
                else
                    roi = self.rois{numrois}; 
                    roi.('ROIIndex')= index;
                    roi.('ROIName') = name;
                end

                roicolors =RtStruct2.DefaultColors();
                roi.('ROIColor') = options.getoptioni('roi.color', roicolors{mod(numrois, numel(roicolors))+1}); 
            end
            
            % roiindex0= uint64(roi.ROIIndex);
            % if isempty(roiindex0)
            %     roiindex0 = uint64(numrois+1);
            % end

            level= -1; 
            occupied= true;
            nbits= 8;  
            maskval = bitshift(1, nbits)-1; %255;
            while(occupied && level<7)
                level     = level+1;
                shifts    = nbits*level; 
                for roiindex0=1:255
                    roiindex  = bitshift(roiindex0, shifts); 
                    occupied1  = ismember(roiindex, roiindices);
                    if ~occupied1
                        basemask  = bitand(bitshift(self.data(:), -shifts), maskval);
                        occupied  = any(basemask>0 & mask(:));
                        break;
                    end
                end
            end
            
            %Data     = Data+ uint64(roiindex)*mask;
            roi.ROIIndex = roiindex; 
            numrois=numrois+1;
            self.rois{numrois} = roi;
            self.setIntType();
            self.data     = self.data + cast(mask, class(self.data))*cast(roiindex, class(self.data));
        end 
         
        function ReplaceRoiMask(self, name, newmask)
            [~, loc] = ismember(name, self.ROINames);
            if loc==0 
                return;
            end
            index= self.rois{loc}.ROIIndex;
            mask = self.getROIMask(index);
            diff = newmask-mask;
            self.data = cast(double(self.data) + diff*index, self.data_type);
        end
        
        function setIntType(obj, maxindex)
            if ~exist('maxindex', 'var')
                maxindex = 0; 
            end
            
            if ~isempty(obj.rois)
                roiindexes = cellfun(@(x)(x.ROIIndex), obj.rois); 
                maxindex   = double(max(maxindex, max(roiindexes))); 
            end
            
            maxbit = floor(log2(maxindex)/8)*8;
            if maxbit<16
                obj.setDataType('uint16');
            elseif maxbit<32
                obj.setDataType('uint32');
            elseif maxbit<64
                obj.setDataType('uint64');
            else
                error('number of bits exceed 64'); 
            end
            
            if isempty(obj.data)
                obj.ZeroData();
            else
                obj.data = cast(obj.data, obj.data_type); 
            end
        end
    end
end

