classdef ROIMaskImage <VolHeaderImage
   % Copyleft (c) Weiguo Lu
   % Version: 1.0
    
    properties
%         ROINames; 
        rois; 
    end
    
    methods
        function self = ROIMaskImage(varargin)
            self = self@VolHeaderImage(varargin{:});
            if isstruct(self.rois)
                self.rois = {self.rois};
            end
            
            for k=1:numel(self.rois)
                try 
                   if ~ischar(self.rois{k}.ROIName)
                       if isnumeric(self.rois{k}.ROIName)&& numel(self.rois{k}.ROIName)==1
                           self.rois{k}.ROIName = num2str(self.rois{k}.ROIName); 
                       else
                           self.rois{k}.ROIName = 'unknown'; 
                       end
                   end
                catch
                end
            end
            if isempty(self.imageType)
                self.imageType = 'ROIMask';
            end
        end
        
        function header = MhdHeader(self)
            header      = self.MhdHeader@VolHeaderImage();
            
            header.rois = self.rois; 
        end
        
        function header = FromMhdHeader(self, header)
            self.FromMhdHeader@VolHeaderImage(header);
            self.rois = header.rois; 
            if isstruct(self.rois)
                self.rois = arrayfun(@(x)(x), self.rois, 'uniformoutput', false);
            end
        end
        
        function ResetDicomAffineMatrix(obj, A)
            rois1  = obj.rois; 
            N = numel(rois1);
            ROICenter = nan(3, N); 
            for k=1:N 
                c = StructBase.getfieldx_default(rois1{k}, 'ROICenter',nan(3, 1)); 
                ROICenter(:, k) =c(:);
            end
            ROICenter_img = obj.Dicom2ImageCoor(ROICenter); 
            obj.ResetDicomAffineMatrix@VolHeaderImage(A); 
            ROICenter = obj.Image2DicomCoor(ROICenter_img);
            for k=1:N 
                obj.rois{k}.ROICenter = ROICenter(:, k)'; 
            end
        end

        function AssignRois(self, rois, varargin)
            if iscell(rois) && isstruct(rois{1})
                self.rois = rois; 
                return;
            end
            
            roinames= rois;
            if ischar(roinames)
                roinames = strsplit(roinames); 
            end
            N       = numel(roinames); 
            options = OptionsMap(varargin{:}); 
            roiindices = options.getOption('ROIIndex', [1:N]); 
            roiindices = StructBase.toCell(roiindices); 
            self.rois = cellfun(@(x, y)struct('ROIName', x, 'ROIIndex', y), roinames, roiindices, 'uniformoutput', false); 
            SetDistinguishableColors(self);
            ROIStatTable(self); 
        end
        
        function IndexROINames(self, names, indexes, varargin)
            options = OptionsMap(varargin{:}); 
            names0  = self.ROINames; 
            indexs0 = self.ROIIndexs;
            
            if ~exist('names', 'var') ||isempty(names)
                names = names0; 
            end
            
            [~, loc] = ismember(names, names0);
            if ~exist('indexs', 'var') || isempty(indexs)
                indexes = indexs0(loc);
            end
%             prefix  = options.getoptioni('indexprefix', ''); 
%             postfix = options.getoptioni('indexpostfix', ' '); 
            replace     = options.getoptioni('replaceexisting', 1); 
            %isprefix  =  options.getoptioni('isprefix', 1); 
            matchstrpat = options.getoptioni('matchstrpat', '^\d+'); 
            indexstrpat = matchstrpat(2:end);
            indexstrpat = strrep(indexstrpat, '\d+', '$0'); 
            %indexstrpat = options.getoptioni('indexstrpat', '^$0 '); 
            %repexpstr   = strrep(indexstrpat, '$0', '\d+'); 
            isprefix    = strcmp(matchstrpat(1), '^'); 
            
            for k=1:numel(names)
                name = names{k}; 

                indexstr = regexprep(num2str(indexes(k)), '^\d+', indexstrpat); 
                if replace
                    name = regexprep(name, matchstrpat, ''); 
                end
                
                if isprefix
                    %newnames{k} = [prefix num2str(indexes(k)) postfix name];
                    newnames{k} = [indexstr name];
                else
                    newnames{k} = [name indexstr];
                end
            end
            
            ResetROINames(self, names, newnames);
        end
        
        function res = GetROILaterality_LR(self, roiname, dcmcenterx)
            res = '';
            if ~exist('dcmcenterx', 'var')||isempty(dcmcenterx)
                c   = dcmcoor_center(self);
                dcmcenterx=c(1);
            end
            try
            
            [index, loc] = getROIIndex(self, roiname);
            ROICenter = self.rois{loc}.ROICenter; 
            if ROICenter(1)<dcmcenterx
                res = 'R';
            else
                res = 'L';
            end
            catch
            end
        end

        function [orignames, newnames]=ResetROINames_LR(self, resetstr, varargin)
            options = OptionsMap(varargin{:}); 
            dcmcenterx=options.getoptioni('DcmCenterX', []);
            if ischar(resetstr)||iscell(resetstr)
                    names  = resetstr;
                    matpats= '';
            elseif isstruct(resetstr)
                    names = StructBase.getfieldx_default(resetstr, 'ROIName', ''); 
                    matpats = StructBase.getfieldx_default(resetstr, 'MatchPattern', ''); 
                    if isempty(dcmcenterx) && isfield(resetstr, 'RefCenter')
                        refcenter = StructBase.getfieldx(resetstr, 'RefCenter');
                        if isstruct(refcenter)
                            refroiname = StructBase.getfieldx(refcenter, 'RefROIName');
                            refroiprop = StructBase.getfieldx_default(refcenter, 'RefROIProp', 'ROICenter');
                            [~, loc] = ismember(refroiname, self.ROINames);
                            if loc>0
                                val = self.rois{loc}.(refroiprop);
                                offset= StructBase.getfieldx_default(refcenter, 'RefCenterOffset', [0 0 0]);
                                dcmcenterx=val(1)+offset(1);
                            end
                        else
                            dcmcenterx=refcenter(1);
                        end
                    end
            end

            if ischar(names)
                names = strsplit(names, '|');
            end
            
            if ~exist('matpats', 'var') || isempty(matpats)
                %matpats = cellfun(@(name)(['^' name '.*']), names, 'uniformoutput', false);
                %matpats = cellfun(@(name)(['^(?!.*' name 's)^' name '.*']), names, 'uniformoutput', false);
                matpats = cellfun(@(name)(['^' name '(_IL|_CL)?$']), names, 'uniformoutput', false);
                Lpats   = cellfun(@(name)(['^' name '(_Lt|_Left)$']), names, 'uniformoutput', false);
                Rpats   = cellfun(@(name)(['^' name '(_Rt|_Right)$']), names, 'uniformoutput', false);
            end
            
            if ischar(matpats)
                matpats =  strsplit(matpats, '|');
            end

            roinames = self.ROINames; 
            m=0; orignames=[]; newnames=[];
            for k=1:numel(names)
                name = names{k}; 
                Lpat = Lpats{k}; 
                I = find(cellfun(@(x)(~isempty(regexp(x, Lpat, 'ONCE'))), roinames));
                if ~isempty(I)
                    roinames1  = roinames(I);
                    roinames(I)=[];
                    m=m+1;
                    orignames{m} = roinames1{1}; 
                    newnames{m}  = [name '_L'];
                end

                Rpat = Rpats{k};
                I = find(cellfun(@(x)(~isempty(regexp(x, Rpat, 'ONCE'))), roinames));
                if ~isempty(I)
                    roinames1  = roinames(I);
                    roinames(I)=[];
                    m=m+1;
                    orignames{m} = roinames1{1}; 
                    newnames{m}  = [name '_R'];
                end

                matpat = matpats{k};
                I = find(cellfun(@(x)(~isempty(regexp(x, matpat, 'ONCE'))), roinames));
                if ~isempty(I)
                    roinames1  = roinames(I);
                    roinames(I)=[];
                    for n=1:numel(I)
                        roiname=roinames1{n};
                        lat = GetROILaterality_LR(self, roiname, dcmcenterx);
                        if ~isempty(lat)
                            m=m+1;
                            orignames{m} = roiname; 
                            newnames{m}  = [name '_' lat];
                        end
                    end
                end
            end

            if m>0
                ResetROINames(self, orignames, newnames);
            end
        end

        % matchpatterns and newnames are cell
        function [matchnames, newnames] = ResetROINames_match(self, matchpatterns, newnames0)
            m=0; 
            newnames = []; matchnames=[];
            for k=1:numel(newnames0)
                %newname = newnames{k};
                [names] = GetMatchNames(self, matchpatterns{k});
                if isempty(names)
                    continue;
                end
                m=m+1; 
                matchnames{m} = names{1};
                newnames{m} =  newnames0{k};
            end
            ResetROINames(self, matchnames, newnames);
        end

        function ResetROINames(self, names, newnames)
            if ischar(names)
                names = strsplit(names, '|'); 
            end
            if ischar(newnames)
                newnames = strsplit(newnames, '|'); 
            end
            rois1  = self.rois; 
            names0 = self.ROINames; 
            for k=1:numel(names)
                [~, loc] = ismember(names{k}, names0); 
                if loc>0
                    rois1{loc}.ROIName = newnames{k};
                end
            end
            self.rois = rois1; 
        end
        
%         function ResetROIIndex(self, names, indices)
%             if ischar(names)
%                 names = strsplit(names, '|'); 
%             end
%             rois1  = self.rois; 
%             names0 = self.ROINames; 
%             for k=1:numel(names)
%                 [~, loc] = ismember(names{k}, names0); 
%                 if loc>0
%                     rois1{loc}.ROIIndex = indices(k);
%                 end
%             end
%             self.rois = rois1; 
%         end
%         
%         function header = readMhdHeader(self, fname)
%             header = self.readMhdHeader@VolHeaderImage(fname); 
%             self.rois = header.rois; 
%         end
%         
%         function header = writeMhdHeader(self, fname, varargin)
%             header = self.writeMhdHeader@VolHeaderImage(fname, varargin{:}); 
%             header.rois = self.rois; 
%             writeStruct2File(fname , header, 1);
%         end
               
        function hdr = readNiftiImage(self, fname)
            hdr = self.readNiftiImage@VolHeaderImage(fname);
            try
                name = regexp(fname,'.*(?=.nii)','once','match');
                fname1 = [name '.mhd'];
                if exist(fname1, 'file')
                    %header = readMhdHeader(self, fname1);
                    header = parseTextFile(fname1);
                    self.rois = header.rois; 
                else
                    fname1 = [name '.json'];
                    if ~exist(fname1, 'file')
                        [path] = fileparts(fname1);
                        fname1 = [path filesep 'templateroimask.json'];
                    end
                    
                    if exist(fname1, 'file')
                        text = fileread(fname1);
                        header = jsondecode(text);
                        Rois = header.rois; 
                        if isstruct(Rois)
                        for k=1:numel(Rois)
                            self.rois{k} = Rois(k); 
                        end
                        elseif iscell(Rois)
                            self.rois = Rois; 
                        end
                        
                        if isfield(header, 'ImageType')
                            self.imageType = header.ImageType; 
                        end
                    end
                end
            catch
            end
        end
        
        
        function writeNiftiImage(self, fname,varargin)
            self.writeNiftiImage@VolHeaderImage(fname, varargin{:});
            %self.writeMhdHeader([fname '.mhd'], 1);
            self.writeJsonHeader([fname '.json']);
        end
        
        function flag = ExistROI(self, name)
         flag = ismember(name, ROINames(self));
        end
        
        function names = ROINames(self)
            function A = errorFunc(S, varargin)
                    A='';
            end
            if isempty(self.rois)
                names = {};
            else
                names  = cellfun(@(x)(x.ROIName), self.rois, 'UniformOutput', false, 'ErrorHandler', @errorFunc);  
            end
        end
        
        function res = ROIIndexs(self)
            function A = errorFunc(S, varargin)
                    A=NaN;
            end
            
            if isempty(self.rois)
                res = [];
            else
                res = cellfun(@(x)(x.ROIIndex), self.rois, 'UniformOutput', true, 'ErrorHandler', @errorFunc);  
            end
        end

        function types = ROIInterpretedTypes(self)
            types = cellfun(@(x)(x.RTROIInterpretedType), self.rois, 'UniformOutput', false);
        end
        
        function img = GetExternalMaskImage(self)
            img = GetMaskImage_ROIInterpretedType(self, 'EXTERNAL');
        end
        
        function img = GetMaskImage_ROIInterpretedType(self, type)
            types = ROIInterpretedTypes(self);
            [flag, loc]=ismember(upper(types), upper(type));
            I   = find(loc);
            img = [];
            if ~isempty(I)
                img = VolHeaderImage(self);
                img.data(:)=0; 
                for k=1:numel(I)
                    index = self.rois{I(k)}.ROIIndex; 
                    img.data(self.getROIMask(index))=1;
                end
            end
        end
        
        function res = NumberOfRois(self)
            res = numel(self.rois); 
        end
        
        function flag = isTargetROI(self,index)
            if ischar(index)
                index = find(ismember(self.ROINames, index)); 
            end
            
            roi = self.rois{index};
            flag = false; 
            
            try
                type = roi.RTROIInterpretedType;
                typeflag = strcmpi(type, 'ptv') || strcmpi(type, 'gtv') || strcmpi(type, 'ctv');
                flag = flag || typeflag;
            catch 
            end
            try
                name = roi.ROIName;
                nameflag = strcmpi(name, 'ptv') || strcmpi(name, 'gtv') || strcmpi(name, 'ctv') || strcmpi(name, 'tumor');
                flag = flag || nameflag;
            catch
            end
        end
        
        function num = NumberOfTargets(self)
            numrois = NumberOfRois(self);
            num = 0; 
            for k=1:numrois
                if isTargetROI(self, k)
                    num = num+1; 
                end
            end
        end
        
        function num = NumberOfOars(self)
            numrois = NumberOfRois(self);
            num = numrois - NumberOfTargets(self);
        end
        
        function roimask = Convert2ROIMask(self, varargin)
            if strcmpi(self.imageType, 'roimask') && nargin==1
                if nargout==0
                    return
                else
                    roimask=ROIMaskImage(self); return;
                end
            end
            options = OptionsMap(varargin{:});
            selectedroinames = options.getoptioni('SelectedROINames', self.ROINames);
            roimask = self.GetSubMaskImage(selectedroinames, {'outputmasktype', 'roimask'});
            
            mergeops = options.getOption('MergeOperation');
            if ~isempty(mergeops)
                mergeops = StructBase.toCell(mergeops);
            end

            for k=1:numel(mergeops)
                merge = mergeops{k};
                [mask, names] = self.getMergedMask(merge.SourceROI);
                roimask.AddRoiMask(mask, merge.ROIName); 
            end
            roimask.setIntType; 

            if nargout==0
                self.imageType='roimask';
                self.data = roimask.data; 
                self.rois = roimask.rois; 
                self.setIntType();
            end
        end
        
        function rs = toRtStruct(self, rs, roinames)
            if ~exist('roinames', 'var')|| isempty(roinames)
                roinames = self.ROINames;
            end
            
            if ischar(roinames)
                roinames = {roinames};
            end
            
            if ~exist('rs', 'var') || isempty(rs)
                rs = RtStruct2; 
            end
            
            rs.rois = containers.Map('KeyType', 'char', 'ValueType', 'any');
            xmesh = self.x_start + (0:(self.x_dim-1)) * self.x_pixdim;
            ymesh = self.y_start + (0:(self.y_dim-1)) * self.y_pixdim;
            zmesh = self.z_start + (0:(self.z_dim-1)) * self.z_pixdim;
            names = self.ROINames;
            for k=1:numel(roinames)
                name = roinames{k};
                [~, loc] = ismember(lower(name), lower(names));
                if loc ==0
                    continue;
                end
                
                roi = self.rois{loc};
                   
%                 roi = self.rois{k}; 
                %                 name = self.rois{k}.ROIName; 
%                 roi.ROIName  = name;
                if isempty(roi)||~isstruct(roi)
                    continue; 
                end
                
                name  = roi.ROIName; 
                try
                index = roi.ROIIndex; 
                catch
                    index = k; 
                end
%                 mask = double(getROIMask(self, index)); mask = permute(mask, [2 1 3]); 
%                 roi.zContours = RtStruct2.mask2zContours(xmesh, ymesh, zmesh, mask);
                mask = double(getROIMask(self, index)); 
                roi.zContours = Mask2zContours(self, mask);
                switch lower(self.imageType)
                    case 'roimask8'
                        nbits = floor(log2(double(index))/8)*8;
                        index = bitshift(index, -nbits);
                end
                
                roi.ROINumber = index; 
                roi.ROIIndex  = index; 
                rs.rois(name) = roi; 
            end
        end
        
        function zContours = Mask2zContours(self, mask, outputformat)
            sliceContours = cell(self.z_dim, 1);
            x = 1:self.x_dim; 
            y = 1:self.y_dim; 
            z = 1:self.z_dim; 
            unit = self.LinearMeasureUnit;
            
            % polygon reduction with distance = 0.05 cm (half mm)
            polythreshold = 0.05; 
            if strcmpi(unit, 'mm')
                 polythreshold = 0.5;
            end
            
            for kSlice = 1:self.z_dim
                contours2d = contourcs(x, y, mask(:,:,kSlice), [0.5 0.5]);
                if isempty(contours2d)
                    continue; 
                end
                polys2d = arrayfun(@(s)polysimp([s.x(:) s.y(:)], polythreshold), contours2d, 'UniformOutput', false);
                polys3d = cellfun(@(p)horzcat(p(:,1), p(:,2), z(kSlice) * ones(size(p,1),1)), polys2d, 'UniformOutput', false);
                sliceContours{kSlice} = polys3d(:);
            end
            zContours = vertcat(sliceContours{:});
            
            if isempty(zContours)
                return
            end
            
            % remove "contours" with less than 3 points
            zContours = zContours(cellfun(@(c)size(c,1)>=3, zContours));
            if ~exist('outputformat', 'var') ||isempty(outputformat)
                outputformat='dicom';
            end
            
            if strcmpi(outputformat, 'dicom')
                %convert to dicom coordinates
                for k=1:numel(zContours)
                    dicomcoors   = self.Image2DicomCoor(zContours{k}');
                    zContours{k} = dicomcoors'; 
                end
            end
        end
        
        function fromMask2D_TCS(self, roiname, zmask, xmask, ymask, varargin)
%             S = size(maskimg0.data); 
%             zmask =maskimg0.data(:, :, C(3)); 
%             xmask =maskimg0.data(:, C(2), :);  
%             ymask =maskimg0.data(C(1), :, :); 
            S = [self.y_dim, self.x_dim, self.z_dim];
            zmask = reshape(zmask, [S(1), S(2), 1]); 
            ymask = reshape(ymask, [1, S(2), S(3)]);
            xmask = reshape(xmask, [S(1),1, S(3)]);
            ZMask = repmat(zmask, [1, 1, S(3)]); 
            XMask = repmat(xmask, [1, S(2),1]); 
            YMask = repmat(ymask, [S(1), 1, 1]); 
            SumMask = XMask +YMask+ZMask; 
            %MaskTCS = ROIMaskImage(VolHeader(maskimg0)); MaskTCS.ZeroData; 
            self.AddRoiMask(SumMask>=3, roiname, varargin{:}); 
        end
        
        function reformed = fromRTStruct(obj, rs, varargin)
            if ischar(rs) && exist(rs, 'file')
                rs =  RtStruct2(rs, obj);
            end
            vhrs = rs.GetVolHeader();
            reformed = false; 
            if abs(vhrs.z_pixdim-obj.z_pixdim)<1e-5
                obj.fromRTStruct0(rs, varargin{:});
            else
                if strcmpi( obj.imageType, 'roimask8')
                    roimask = ROIMask8Image(vhrs);
                else
                roimask = ROIMaskImage(vhrs);
                end
                roimask.fromRTStruct0(rs, varargin{:});
                roimask.ReformCS(obj);           
                obj.setData(roimask.data);
                obj.setDataType(roimask.data_type);
                obj.rois = roimask.rois; 
                reformed = true; 
            end
        end
        
%         function fromRTStruct0(obj, rs, varargin)
%             options = OptionsMap(varargin{:});
%             %ROINames = options.getOption('ROINames', rs.ROINames);
%             ROINames0 = rs.ROINames;
%             ROINames = options.getOption('ROINames');
%             if ~isempty(ROINames)
%                 I = cellfun(@(x)(~ismember(x, ROINames0)), ROINames);
%                 ROINames(I)=[];
%             else
%                 ROINames=ROINames0;
%             end
%             
%             numRois      = numel(ROINames); 
%             if numRois ==0; 
%                 return; 
%             end
%             
%             for k=1:numRois
%                 name = ROINames{k}; 
%                 roi  = rs.rois(name); 
% %                 if isfield(roi, 'index')
% %                     index = roi.index; 
% %                 else
% %                     index = k;
% %                 end
%                 rois{k} = struct('ROIIndex', roi.ROIIndex,...
%                     'ROIName', name,...
%                     'ROIColor', roi.ROIColor,...
%                     'RTROIInterpretedType',   roi.RTROIInterpretedType,...
%                     'ROIPhysicalProperty',    roi.ROIPhysicalProperty, ...
%                     'ROIPhysicalPropertyValue', roi.ROIPhysicalPropertyValue); 
%             end
%             obj.rois = rois;
%             
%             %setIntType(obj, numRois);
%             roiIndices=cellfun(@(x)(x.ROIIndex), rois);
%             maxROIIndex = max(roiIndices); 
%             setIntType(obj, maxROIIndex);
%             
% 
%                 Data = zeros(obj.y_dim, obj.x_dim, obj.z_dim, obj.data_type); 
%                 for m=1:numRois 
% %                 name = ROINames{m};
%                     name = obj.rois{m}.ROIName;
%                     roi = rs.rois(name); 
%                     
%                     if ~isfield(roi, 'zContours') || isempty(roi.zContours)
%                         continue; 
%                     end
%                     
%                     mask = obj.RtContours2Mask(roi.zContours);
%                     mask = cast(mask, obj.data_type);
%                     index= obj.rois{m}.ROIIndex;
% %                     val  = cast(2^(index-1), obj.data_type);
% %                     obj.data = obj.data + mask*val; 
%                     val = cast(2^(index-1), obj.data_type); 
% %                     Data = Data + mask*val;
%                     Data = bitor(Data, mask*val, obj.data_type); 
%                     
%                 end
%                 obj.data = Data; 
% 
%         end
        function fromRTStruct0(obj, rs, varargin)
            if isempty(obj.data)
                imagetype = obj.imageType; 
                obj.copyStruct(rs.GetVolHeader());
                obj.ZeroData();
                obj.imageType = imagetype; %recover the original image type
            end
            
            options  = OptionsMap(varargin{:});
            roinames = rs.ROINames;
            roinames1= options.getoptioni('ROINames');
            if ~isempty(roinames1)
                if ischar(roinames1)
                    roinames1 = strsplit(roinames1, '|'); 
                end
                roinames  = intersect(roinames1,roinames,'stable');
            end
            keeprsindex = options.getoptioni_numeric('rs.keeproiindex', 0); 
            dstroiindex = options.getoptioni('rs.dstroiindex'); 
            %ROINames  = rs.ROINames;
            numRois= numel(roinames); 
            M    =   numel(obj.rois);
            for m= 1:numRois 
                name= roinames{m};
                roi = rs.rois(name); 
                if ~isfield(roi, 'zContours') || isempty(roi.zContours)
                    continue; 
                end
                mask = obj.RtContours2Mask(roi.zContours);
                if keeprsindex
                    roiindex0 = roi.ROIIndex;
                elseif ~isempty(dstroiindex)
                    roiindex0 = dstroiindex(m);
                else
                    roiindex0 = M+m;
                end
                roi1 = struct('ROIIndex', roiindex0,...
                    'ROIName', name,...
                    'ROIColor', roi.ROIColor,...
                    'RTROIInterpretedType',     roi.RTROIInterpretedType,...
                    'ROIPhysicalProperty',      roi.ROIPhysicalProperty, ...
                    'ROIPhysicalPropertyValue', roi.ROIPhysicalPropertyValue); 
                options.setOption('roi', roi1);
                obj.AddRoiMask(mask, name, options, {'keeproiindex', 1});
            end  
        end
         
        function mask = SampleROIMask(self, roinames, samplerates)
            [~, order] = sort(samplerates, 'descend');
            samplerates = samplerates(order); 
            roinames = roinames(order); 
            [X, Y, Z] = meshgrid(0:(self.x_dim-1), 0:(self.y_dim-1), 0:(self.z_dim-1)); 
            mask = zeros(size(self.data)); 
            for m=1:numel(roinames)
                name = roinames{m}; 
                rate = samplerates(m); 
                mask0= self.getROIMask(name);
                I = mask0 & (mod(X, rate)==0) & (mod(Y, rate)==0) & (mod(Z, rate)==0); 
                mask(I) = 1; 
            end
        end
        
        function SetROIProperty(self, roiname, propname, propvalue)
            [~, loc] = ismember( roiname, self.ROINames); 
            if loc>0
                self.rois{loc}.(propname)=propvalue;
            end
        end
        
        function setIntType(obj, numRois)
            if ~exist('numRois', 'var')
                numRois = NumberOfRois(obj);
            end
            
            maxindex = numRois;
            try
                roiindexes = cellfun(@(x)(x.ROIIndex), obj.rois); 
                maxindex   = double(max(maxindex, max(roiindexes))); 
            catch
     
            end
            
            switch lower(obj.imageType)
                case {'optmask','labelmask', 'perimmask'}
                    maxbit = 15; 
                    
                case {'roimask1'}
                    maxbit = ceil(log2(maxindex)); 
                    
                case {'roimask4'}
                    maxbit = ceil(log2(maxindex)/4)*4; 
                    
                case {'roimask8'}
                    maxbit = ceil(log2(maxindex)/8)*8; 
                    
                otherwise
                    maxbit = maxindex; 
            end
            
            if maxbit<16
                obj.setDataType('uint16');
            elseif maxbit<32
                obj.setDataType('uint32');
            elseif maxbit<64
                obj.setDataType('uint64');
            else
                error('number of bits exceed 64'); 
            end
            obj.data = cast(obj.data, obj.data_type); 
        end
    
        
        
        function [optmask, index] = toWtkOptMask(self, varargin)
            roinames0 = self.ROINames; 
            
            options = OptionsMap(varargin{:});
            tosort = options.getoptioni('tosort', 1);
            hastarget = options.getoptioni('hastarget', 1);
            
            selectedTargets = options.getoptioni('selectedTargets', {});
            selectedOARs    = options.getoptioni('selectedOARs', {});
            
            for k=1:numel(roinames0)
                roiname = roinames0{k}; 
                mask = getROIMask(self, roiname);  
                I{k} = find(mask>0); vol(k) = numel(I{k}); 
            end
            
            if tosort
                [vol, index] = sort(vol(:), 1, 'descend'); 
            else
                index = 1:numel(roinames0); 
            end
            
            I = I(index); %optmask.rois = optmask.rois(index); 
            targetIndex=0; oarIndex = 0;
            Targets = zeros(size(self.data)); 
            Oars    = zeros(size(self.data)); 
%             rois = self.rois(index); 
            roinames = roinames0(index); 
            m = 0; 
            for k=1:numel(roinames)
                roiname = roinames{k}; 
                mask = I{k};
                roi = self.rois{index(k)}; 
                if ~isempty(selectedTargets)||~isempty(selectedOARs)
                    [~, loc] = ismember(roiname, selectedTargets);
                    if loc>0
                        targetIndex   = loc;
                        Targets(mask) = targetIndex; 
                        m = m+1; 
                        rois{m} = roi;
                        rois{m}.ROIIndex = 256*targetIndex;
                    end
                    
                    [~, loc] = ismember(roiname, selectedOARs);
                    if loc>0
                        oarIndex=loc;
                        Oars(mask) = oarIndex; 
                        m = m+1; 
                        rois{m} = roi;
                        rois{m}.ROIIndex = oarIndex;
                    end

                else
                    if  hastarget&& self.isTargetROI(roiname)
                        targetIndex   = targetIndex+1;
                        Targets(mask) = targetIndex; 
                        m = m+1; 
                        rois{m} = roi;
                        rois{m}.ROIIndex = 256*targetIndex;
                    else
                        oarIndex=oarIndex+1;
                        Oars(mask) = oarIndex; 
                        m = m+1; 
                        rois{m} = roi;
                        rois{m}.ROIIndex = oarIndex;
                    end   
                end
            end
            
            optmask = OptMaskImage(self);
            optmask.setIntType(15);
            optmask.imageType='optmask'; 
            data0 = Targets*256+ Oars; 
            optmask.setData(data0); 
            optmask.setDataType('short'); 
            optmask.rois = rois; 
        end
        
        function [constraints] = toWtkOptConstraints(self, weights)
            constraints = WTKOptConstraints;
            roinames = ROINames(self);
            for k=1:numel(roinames)
                name = roinames{k}; 
                istarget = self.isTargetROI(name); 
                importance = weights.getoptioni(name, 1); 
                constraints.addROI(istarget, name, importance);  
            end
        end
        
        
        function res = maxRoiIndex(self)
            index = cellfun(@(x)(x.ROIIndex), self.rois);
            res   = max(index);
        end
        
        function [index, loc] = getROIIndex(obj, name)
%             [~, index] = ismember(name, obj.ROINames); 
           index =[]; 
           names  = ROINames(obj); 
           [~, loc] = ismember(lower(name), lower(names));
           if loc==0
               return
           end
           if ~isempty(loc)
               try
                    index = obj.rois{loc}.ROIIndex;
               catch
                   index = loc;
               end
           end
        end

        function res = getROIName_InterpretedType(obj, type)
           res =[]; 
           types  = ROIInterpretedTypes(obj); 
           [flag, loc] = ismember(type, types);
           
           if flag
                names  = ROINames(obj); 
                res = names(loc);
           end
        end
        
        function res = getROIName_regexpi(self, name)
           names  = ROINames(self); 
           res = regexpi(names, name); 
           loc =[]; 
           for k=1:numel(res)
               if ~isempty(res{k})
                   loc = [loc k]; 
               end
           end
           res = names(loc); 
        end
        
        function outmask = ExtractPlaceHoldROIs(self, cfg_SubROIs)
            if ischar(cfg_SubROIs)
                outmask = self.GetSubMaskImage(cfg_SubROIs, {'PlaceHold', 1});
            else
                outmask = self.getMergedROIImage(cfg_SubROIs);
            end
            %outmask.ROIStatTable_regionprops();
        end

         function maskimg = getMergedROIImage(obj, cfgs, varargin)
            options = OptionsMap(varargin{:});
            outmasktype= options.getoptioni('outputmask.imagetype', 'roimask');
            maskimg = ROIMaskImage(VolHeader(obj));
            maskimg.imageType=outmasktype;
            maskimg.setShort; 
            maskimg.ZeroData();
            
            if isstruct(cfgs)
                cfgs = arrayfun(@(x)(x), cfgs, 'UniformOutput',false);
            end

            for k=1:numel(cfgs)
                cfg = cfgs{k};
                try
                maskname = cfg.ROIName; 
                if iscell(maskname) || ismember('|', maskname)
                    maskimg1 = GetSubMaskImage(obj, maskname);
                    maskimg.AddRoiMaskImage(maskimg1, varargin{:})
                else
                    mask = getMergedMask(obj, cfg);
                    maskimg.AddNewROI(mask,  maskname, varargin{:});
                end
                catch
                end
            end
            maskimg.SetDistinguishableColors();
         end

         function [maskimg, names] = getMergedMaskImage(obj, names, maskname, varargin)
            options = OptionsMap(varargin{:});
            maskimg =options.getoptioni('OutputMaskImage'); 
            if isempty(maskimg)
                outmasktype= options.getoptioni('outputmask.imagetype', 'roimask');
                maskimg = ROIMaskImage(VolHeader(obj));
                maskimg.imageType=outmasktype;
                maskimg.setShort; 
                maskimg.ZeroData();
            end
            mask = getMergedMask(obj, names);
            maskimg.AddNewROI(mask,  maskname, varargin{:});
        end

        function [mask, names] = getMergedMask(obj, names)
            roinames = obj.ROINames; 
            if isstruct(names)
%                 Ch2 = names;
%                 Names = obj.ROINames;
%                 expr  = StructBase.getfieldx(Ch2, "ROIMatchStr"); 
%                 excludestr  = StructBase.getfieldx(Ch2, "ROIExcludeStr"); 
%                 if ~isempty(excludestr)
%                     J = find(cellfun(@(x)(~isempty(regexpi(x, excludestr))), Names));
%                     Names(J)=[];
%                 end
%                 matchmode = StructBase.getfieldx(Ch2, "ROIMatchMode");
%                 switch lower(matchmode)
%                     case 'regexp'
%                         I = find(cellfun(@(x)(~isempty(regexp(x, expr))), Names));
%                     case 'strcmpi'
%                         I = find(cellfun(@(x)(strcmpi(x, expr)), Names));
%                     case 'strcmp'
%                         I = find(cellfun(@(x)(strcmp(x, expr)), Names));
%                     otherwise
%                         I = find(cellfun(@(x)(~isempty(regexpi(x, expr))), Names));
%                 end
%                 %[matched,noMatch] = regexp(Name,'CTV','match','split','forceCellOutput');
%                 names = Names(I);
                matchstr = names; 
                [rois1, I] = obj.GetMatchROIs(obj.rois, matchstr);
                names = roinames(I);
            elseif ischar(names)
                names = strsplit(names, '|');
            end
                
            names = intersect(names, roinames);
            mask = zeros(size(obj.data), 'like', false);
%             roinames = obj.ROINames; 
            for k=1:numel(names)
                name = names{k};
                % if ~ismember(name, roinames)
                %     continue;
                % end
                mask = mask | getROIMask(obj, name);
            end
        end

        function mask = getROIMask(obj, index)
            mask =[]; 
            if ischar(index)
                index = obj.getROIIndex(index); 
            end
            if isempty(index)
                return
            end
            
%             if index>0 && index<32
%                 mask = bitand(uint32(obj.data), uint32(2^(index-1)))>0;
%             elseif index>0 && index<64

            switch lower(obj.imageType)
                case {'optmask'}
                    if index>=256
                        mask = floor(obj.data/256)== floor(index/256); 
                    else
                        mask = mod(round(obj.data), 256)==index; 
                    end
                    
                case {'perimmask', 'labelmask'}
                    mask = obj.data==index;
                    
                case {'roimask1'}
                     mask = bitand(uint64(obj.data), uint64(index))==uint64(index);

                case {'roimask4'}
                    maskval = (2^4-1)*16^floor(log2(double(index))/4); 
                    mask = bitand(uint32(obj.data), uint32(maskval))==uint32(index);
                
                case {'roimask8'}
                    %maskval = (2^8-1)*256^floor(log2(double(index))/8); 
                    maskval = bitshift(uint64(255), 8*floor(log2(double(index))/8));
                    mask = bitand(uint64(obj.data), uint64(maskval))==uint64(index);

                otherwise
                    if index>0
                        %mask = bitand(uint64(obj.data), uint64(2^(index-1)))>0;
                        if isfloat(obj.data)
                            mask = bitand(uint64(obj.data), uint64(2^(index-1)))>0;
                        else
                            %mask = bitand(obj.data, 2^(index-1))>0; 
                            val = cast(2^(index-1), 'like', obj.data); 
                            mask = bitand(obj.data, val)>0; 
                        end
                    else
                        mask = obj.data==0; %background
                    end
            end
        end
        
        function rgb = getRGBSlice(obj, varargin)
            options = OptionsMap(varargin{:}); 
            slicenum= options.getoptioni('slicenum', round(obj.z_dim/2)); 
            I       = cellfun(@(x)(~isempty(x)), obj.rois); 
            rois    = obj.rois(I); 
            colors= options.getoptioni('colors', cellfun(@(x)([x.ROIColor]), rois, 'uniformoutput', false));
            roinames= options.getoptioni('roinames', obj.ROINames); 
                        
            data0 = int32(obj.data(:, :, slicenum));
            
            counts = zeros(size(data0)); 
            R = zeros(size(data0));
            G = zeros(size(data0));
            B = zeros(size(data0));
            for k=1:numel(roinames)
                index = obj.getROIIndex(roinames{k}); 
                if isempty(index) || index<=0 
                    continue; 
                end
                mask0 = bitand(data0,2^(index-1))>0;
                counts = counts+mask0; 
                color = colors{k};
                R = R+mask0*color(1); 
                G = G+mask0*color(2); 
                B = B+mask0*color(3); 
            end
            I = counts>0; 
            R(I) = R(I)./counts(I); G(I) = G(I)./counts(I);B(I) = B(I)./counts(I);
            rgb = cat(3, R, G, B); 
        end
        
        function maskimg = getROIMaskImage(obj, index)
            maskimg=[];
            mask = getROIMask(obj, index);
            if ~isempty(mask)
                maskimg = VolHeaderImage(obj); 
                maskimg.setData(mask); 
            end
        end
        
        function mask = getPTVImage(obj)
            names = obj.getROIName_InterpretedType('PTV');
            if isempty(names)
                names = obj.getROIName_regexpi('PTV'); 
            end
            if ~isempty(names)
                mask = getROIMaskImage(obj, names{1});
            end
        end
        
        function map = getDistance2PTVMap(obj)
            mask = getPTVImage(obj);
            map = VolHeaderImage(mask); 
            map.setData(bwdist(mask.data)); 
            map.data = map.data*mask.x_pixdim; 
        end

        function writeFile(obj, fname)
            obj.writeFile@VolHeaderImage(fname, [], 1); 
        end
        
        function writeMaskFiles(obj, folder, varargin)
            options = OptionsMap(varargin{:}); 
            names = options.getoptioni('selectedroinames', obj.ROINames); 
            ext   = options.getoptioni('output.format', ''); 
            for k=1:numel(names)
                name = names{k};
                maskdata = obj.getROIMask(name);
                mask = VolHeaderImage(obj);
                mask.setData(maskdata);
                mask.setDataType('uchar');
                mask.SaveImage([folder name], ext); 
            end
        end
        

        
        function [coeffs, refNames] = maskDices(self, mask2, varargin)
            options = OptionsMap(varargin{:}); 
            refNames= options.getOption('ROINames'); 
            if isempty(refNames)
                refNames = self.ROINames; 
            end
            tesNames= options.getOption('TestROINames'); 
            
            if isempty(tesNames)
                tesNames = refNames; 
            end
            coeffs =[];
            for k=1:numel(refNames)
                ref = self.getROIMask(refNames{k}); 
                tes = mask2.getROIMask(tesNames{k});
                if ~isempty(ref) && ~isempty(tes)
                    coeffs(k) = self.calcDiceCoeff(ref, tes);
                end
            end
        end
        
        function c  = ROIProperty(self, roiname, propname)
            [flag, k] = ismember(roiname, self.ROINames); 
            if ~flag
                c=[]; return
            end
            c = StructBase.getfieldx(self.rois{k}, propname); 
            if ~isempty(c)
                return;
            end
            T = ROIStatTable(self, propname, roiname); 
            c = T.(propname); 
        end
        
        function c  = ROICenter(self, roiname)
           c = ROIProperty(self, roiname, 'ROICenter');
        end
        
        function c  = ROIVolume(self, roiname)
           c = ROIProperty(self, roiname, 'ROIVolume');
        end
        
        function T = MetaStatTable(self, propnames, roinames, varargin)
            if ~exist('roinames', 'var') || isempty(roinames)
                roinames = self.ROINames; 
            end
            if ~exist('propnames','var') || isempty(propnames)
                propnames = 'ROIIndex|ROIName|ROIColor|RTROIInterpretedType|ROIPhysicalProperty|ROIPhysicalPropertyValue|ROICenter|ROIVolume';
            end
            T = ROIMaskImage.ROIStatTable_header(self, propnames, roinames, varargin{:});
        end
        
        function T = ROIStatTable_regionprops_slice(self, propnames, roinames, varargin)
            options = OptionsMap(varargin{:}); 
            if ~exist('roinames', 'var') || isempty(roinames)
                roinames= self.ROINames;
            end
            if ischar(propnames)
                propnames = strsplit(propnames, '|'); 
            end
            if ischar(roinames)
                roinames = strsplit(roinames, '|'); 
            end
            T=[];
            M = numel(roinames); N= numel(propnames); 
            if M==0 || N==0
                return;
            end
            slicedirection=options.getOption('SliceDirection', 'XY');
            arrayStatFun=options.getOption('ArrayStatFun', @max);
            pixelResolution   =self.getPixelDim;
            T = table; ROIName=[];
            nanstruct = cell2struct(repmat({NaN},1,numel(propnames)), propnames, 2);
            for k=1:numel(roinames)
                roiname = roinames{k};
                BW3     = self.getROIMask(roiname);
                [rawstats] = self.regionprops2D(BW3, propnames, pixelResolution, slicedirection);
                I = cellfun(@(x)(~isempty(x)), rawstats); 
                statarr = cellfun(@(x)x, rawstats(I));
                % if isempty(statarr)
                %     continue; 
                % end
                if ~isempty(statarr)
                    Sstat = StructBase.structArrayStat(statarr, arrayStatFun);
                else
                    Sstat = nanstruct;
                end
                T1 = struct2table(Sstat);                    
                T  = cat(1, T, struct2table(Sstat));
            end
           
            I = ismember('XYZ', upper(slicedirection));
            minpixelsize = min(pixelResolution(I));
            
            linearprops = {'MajorAxisLength', 'MinorAxisLength', 'EquivDiameter', 'Perimeter'};
            linearprops=intersect(propnames, linearprops);
            for k=1:numel(linearprops)
                propname = linearprops{k};
                T.(propname) = T.(propname)* minpixelsize;
            end
            areaprops = {'Area'};
            areaprops=intersect(propnames, areaprops);
            for k=1:numel(areaprops)
                propname = areaprops{k};
                T.(propname) = T.(propname)* minpixelsize* minpixelsize;
            end

            T.ROINames = roinames(:); 
            T=movevars(T, 'ROINames', 'before', 1);
        end
        
        function [T, propnames_2D] = ROIStatTable_regionprops2D(self, propnames, roinames, varargin)
            if ~exist('roinames', 'var') || isempty(roinames)
                roinames= self.ROINames;
            end

            % Get slice orientation mapping from DICOM header
            try
                [~, planeMapping] = self.getSliceOrientation();
            catch
                % Default mapping if orientation cannot be determined
                planeMapping = struct('XY', 'AX', 'YZ', 'SAG', 'ZX', 'COR');
            end

            % Create mapping from coordinate to clinical directions
            coordToClinical = containers.Map();
            coordToClinical('XY') = planeMapping.XY;
            coordToClinical('YZ') = planeMapping.YZ;
            coordToClinical('ZX') = planeMapping.ZX;

            % Process both clinical (AX, SAG, COR) and coordinate (XY, YZ, ZX) suffixes
            directions  = {'XY', 'YZ', 'ZX'};
            % clinical_directions = {'AX', 'SAG', 'COR'};
            % all_directions = [directions, clinical_directions];

            T = []; propnames_2D= [];

            for k=1:numel(directions)
                coord_direction = directions{k};
                clinical_direction = coordToClinical(coord_direction);

                % Find properties with coordinate suffix (e.g., _XY)
                I_coord = cellfun(@(x)(endsWith(x, ['_' coord_direction])), propnames);
                propnames_coord = propnames(I_coord);

                % Find properties with clinical suffix (e.g., _AX)
                I_clinical = cellfun(@(x)(endsWith(x, ['_' clinical_direction])), propnames);
                propnames_clinical = propnames(I_clinical);

                % Combine both types
                propnames1 = [propnames_coord, propnames_clinical];

                if isempty(propnames1)
                    continue;
                end

                propnames_2D = cat(2, propnames_2D, propnames1);

                % Strip suffixes to get base property names
                propnames2_coord = cellfun(@(x)(x(1:end-3)), propnames_coord, 'UniformOutput', false);
                propnames2_clinical = cellfun(@(x)(x(1:end-length(clinical_direction)-1)), propnames_clinical, 'UniformOutput', false);
                propnames2 = [propnames2_coord, propnames2_clinical];

                % Compute statistics for this direction
                T1 = self.ROIStatTable_regionprops_slice(propnames2, roinames, {'SliceDirection', coord_direction}, varargin{:});

                % Rename variables back to original names (with suffixes)
                T1 = renamevars(T1, propnames2, propnames1);

                if isempty(T)
                    T = T1;
                else
                    T = cat(2, T, T1(:,2:end));
                end
            end
        end
    
        function T = ROIStatTable_regionprops(self, propnames, roinames, varargin)
            options = OptionsMap(varargin{:}); 
            if ~exist('roinames', 'var') || isempty(roinames)
                roinames= self.ROINames;
            end
            if ~exist('propnames','var') || isempty(propnames)
                propnames = 'ROICenter|ROIVolume';
            end
            if ischar(propnames)
                propnames = strsplit(propnames, '|'); 
            end
            if ischar(roinames)
                roinames = strsplit(roinames, '|'); 
            end
            T=[];
            M = numel(roinames); N= numel(propnames); 
            if M==0 || N==0
                return;
            end
            
            try
                [T_2D, propnames_2D] = ROIStatTable_regionprops2D(self, propnames, roinames, varargin{:});
                propnames=setdiff(propnames,  propnames_2D, 'stable');
            catch
                T_2D=[]; propnames_2D={};
            end
            CC = struct('Connectivity', 26, 'ImageSize', size(self.data), ...
                        'NumObjects', M); 
            for k=1:M
                PixelIdxList{k} = find(self.getROIMask(roinames{k})); 
            end
            CC.PixelIdxList=PixelIdxList; 
            propnames1 = propnames; 
            
            propnames1 = strrep(propnames1, 'ROICenter', 'Centroid');
            propnames1 = strrep(propnames1, 'ROIVolume', 'Volume');
            propnames1 = strrep(propnames1, 'ROISurfaceArea', 'SurfaceArea');
            propnames1 = strrep(propnames1, 'ROIPALength', 'PrincipalAxisLength');
            propnames1 = strrep(propnames1, 'ROIOrientation', 'Orientation');
            if ismember('BBSize', propnames1)||ismember('BBCenter', propnames1)
                propnames1 = union(propnames1, {'BoundingBox'});
            end
            propnames1 = setdiff(propnames1, {'BBCenter', 'BBSize', 'ROICenter', 'ROIVolume'}, 'stable'); 
            
            propnames1 = intersect(propnames1, self.regionprops3_varnames(), 'stable');
            
            T = regionprops3(CC, propnames1); 
            T = T(:, propnames1);
            try
                bb= T.BoundingBox;
                I = find(any(bb(:, 4:6)==0, 2));
                bb(I, :)=NaN(numel(I), 6); 
                T.BoundingBox=bb;
            end
            if ismember('ROIVolume', propnames)
                T.Volume = T.Volume*self.VoxelSize(); 
                %T = renamevars(T, 'Volume', 'ROIVolume'); 
                T.ROIVolume = T.Volume;
            end

            if ismember('ROISurfaceArea', propnames)
                pixelarea = mean([self.x_pixdim,self.y_pixdim,self.z_pixdim])^2; 
                T.ROISurfaceArea = T.SurfaceArea*pixelarea; 
            end
            
            if ismember('ROIPALength', propnames)
                voxlength = mean([self.x_pixdim,self.y_pixdim,self.z_pixdim]);
                T.ROIPALength = T.PrincipalAxisLength*voxlength; 
            end
            
            if ismember('ROIOrientation', propnames)
                T.ROIOrientation = T.Orientation; 
            end
            
            if ismember('ROICenter', propnames)
                Centroid = T.Centroid; 
                ROICenter=self.Image2DicomCoor(Centroid'); 
%                 T.Centroid = ROICenter'; 
%                 T = renamevars(T, 'Centroid', 'ROICenter');
                T.ROICenter = ROICenter'; 
                %T.Properties.VariableNames{loc} = 'ROICenter';
            end
            
            if ismember('BBCenter', propnames)
                C = (T.BoundingBox(:, 1:3) + T.BoundingBox(:, 4:6)/2);
                dicomCoor = self.Image2DicomCoor(C');
                T.BBCenter = dicomCoor'; 
            end
            
            if ismember('BBSize', propnames)
                M = size(T, 1); 
                pixdim = [self.x_pixdim self.y_pixdim self.z_pixdim];
                bbsize = T.BoundingBox(:, 4:6).*repmat(pixdim, [M, 1]);
                T.BBSize = bbsize; 
            end
            
%             varnames = T.Properties.VariableNames; 
%             I = ~ismember(varnames, propnames);
%             T(:, I)=[];
            T = T(:, propnames); 
            
            T.ROINames = roinames(:);
            N = size(T, 2);
            T = T(:, [N, 1:N-1]); 
%             if ~ismember('BoundingBox', propnames) && ismember('BoundingBox', propnames1)
%                 T = removevars(T, 'BoundingBox');
%             end
            if ~isempty(T_2D)
                T = cat(2, T, T_2D(:, 2:end));
            end

            defaultmodifymetadata =0; 
            if nargout<1
                defaultmodifymetadata=1; 
            end
            modifymetadata = options.getoptioni_numeric('modifymetadata', defaultmodifymetadata);
            if modifymetadata
                [~, loc] = ismember(T.ROINames, self.ROINames);
                loc = loc(:)'; %loc(loc==0)=[];
                for m=1:size(T, 1)
                    k = loc(m);
                    if k==0
                        continue; 
                    end
                    roi = self.rois{k};
                    for n=1:numel(propnames)
                        propname = propnames{n};
                        roi.(propname) = T{m, propname};
                    end
                    self.rois{k} = roi; 
                end
            end
        end
        
        function T = ROIStatTable(self, propnames, roinames, varargin)
            options = OptionsMap(varargin{:}); 
            img     = options.getOption('image');
            imgtype = lower(options.getOption('imagetype', 'intensity'));
            
            if ~exist('roinames', 'var') || isempty(roinames)
                roinames= self.ROINames;
            end
            if ~exist('propnames','var') || isempty(propnames)
                propnames = 'ROICenter|ROIVolume';
            end
            if ischar(propnames)
                propnames = strsplit(propnames, '|'); 
            end
            if ischar(roinames)
                roinames = strsplit(roinames, '|'); 
            end
            
            

            T  = table; 
            M = numel(roinames); N = numel(propnames);
            val= cell(M, N);
            for m=1:M
            %parfor (m=1:M, 2)
                mask = self.getROIMaskImage(roinames{m});
                if isempty(mask)
                    continue;
                end
                if ~isempty(img)
                    imgdata = img.data(mask.data); 
                end
                for n=1:N
                    statname = propnames{n};
                    switch lower(statname)
                        case {'numvox'}
                            val{m, n} = sum(mask.data(:));
                        case {'volume', 'roivolume'}
                            val{m, n} = sum(mask.data(:))*mask.VoxelSize(); 
                        case {'roicenter', 'center'}
                            val{m, n} = mask.CenterOfMass(1);
                        case {'centerofmass'}
                            val{m, n} = mask.CenterOfMass();
                        case {'roiindex'}
                             val{m, n} = self.getROIIndex(roinames{m});
                        case {'boundingbox'}
                            stats     = regionprops3(mask.data,'BoundingBox');
                            bb = stats.BoundingBox;
                            if size(bb, 1)>1
                                left = bb(:, 1:3); right = bb(:, 1:3)+bb(:, 4:6);
                                left = min(left, [], 1); right = max(right, [], 1); 
                                width = right-left; 
                                bb = [left width];
                            end
                            val{m, n} = bb;
                        case {'ccvolume'}
                             stats     = regionprops3(mask.data,'Volume');
                             val{m, n} = stats.Volume(:)';
                        case {[imgtype '_mean']}
                            val{m, n} = mean(imgdata(:));
                        case {[imgtype '_std']}
                            val{m, n} = std(imgdata(:));
                        case {[imgtype '_min']}
                            val{m, n} = min(imgdata(:)); 
                        case {[imgtype '_max']}
                            val{m, n} = max(imgdata(:));  
                        case {[imgtype '_median'],[imgtype '_prctile50'], [imgtype '50']}
                            val{m, n} = prctile(imgdata(:), 50); 
                        case {[imgtype '_prctile5'], [imgtype '5']}
                            val{m, n} = prctile(imgdata(:), 5);
                        case {[imgtype '_prctile95'], [imgtype '95']}
                            val{m, n} = prctile(imgdata(:), 95);
                        case {[imgtype '_prctile10'], [imgtype '10']}
                            val{m, n} = prctile(imgdata(:), 10);
                        case {[imgtype '_prctile90'], [imgtype '90']}
                            val{m, n} = prctile(imgdata(:), 90);
                        case {'palength'}
                            pixdims = [self.x_pixdim, self.y_pixdim, self.z_pixdim];
                            [PALength, orientation] = self.PrincipalAxisLength(mask.data, pixdims);
                            val{m, n} = PALength;
                        case {'paorientation'}
                            pixdims = [self.x_pixdim, self.y_pixdim, self.z_pixdim];
                            [PALength, orientation] = self.PrincipalAxisLength(mask.data, pixdims);
                            val{m, n} = orientation;    
                    end       
                end
            end
            T = cell2table(val); 
            T.Properties.VariableNames=propnames;
            T.ROINames = roinames(:);
            T = T(:, [N+1, 1:N]); 
            
            options        = OptionsMap(varargin{:});
            defaultmodifymetadata =0; 
            if nargout<1
                defaultmodifymetadata=1; 
            end
            modifymetadata = options.getoptioni_numeric('modifymetadata', defaultmodifymetadata);
            if modifymetadata
                [~, loc] = ismember(T.ROINames, self.ROINames);
                loc = loc(:)'; %loc(loc==0)=[];
                for m=1:size(T, 1)
                    k = loc(m);
                    if k==0
                        continue; 
                    end
                    roi = self.rois{k};
                    for n=1:numel(propnames)
                        propname = propnames{n};
                        roi.(propname) = T{m, propname};
                    end
                    self.rois{k} = roi; 
                end
            end
        end
        
        
        function  stat = roisStat(self)
            roinames = self.ROINames; 
            for k=1:numel(roinames)
                try
                stat{k} = roiStat(self, roinames{k}); 
                catch err
                end
            end
        end
        
        function stat = roiStat(self, name)
             mask = self.getROIMaskImage(name);
             stat.name            = name;  
             stat.centerofmass    = mask.CenterOfMass(); 
             stat.volume = sum(mask.data(:))*mask.VoxelSize(); 
        end
        
        function res = targetDoseStat(self, doseData, name, prescription, varargin)
%             options = OptionsMap(varargin{:}); 
            maskData = self.getROIMask(name);
            [res.conformity, res.isoVolume, res.coverageVolume] = ROIMaskImage.getConformityIndex(doseData,maskData, prescription);
            [res.uniformity, res.targetVolume] = ROIMaskImage.getUniformIndex(doseData, maskData);
            res.coverage = res.coverageVolume/res.targetVolume;
        end
        
        function res = roiDoseStats(self, doseData, names, percVs, varargin)
            if isempty(names)
                names = self.ROINames;
            end
            
            if ischar(names) 
                names = {names}; 
            end
            dvhs = DVHCurves; 
            dvhs.calculate(doseData, self);
            
            for k=1:numel(names)
                name = names{k};
                try
                dvh = dvhs.getDVHCurve(name);
                for m=1:numel(percVs)
                    percV=percVs(m); 
                    D(m) = dvh.getDxx_percV(percV);
                end
                res.(name).volume = percVs; 
                res.(name).dose   = D; 
                if self.isTargetROI(name)
                    res.(name).target = targetDoseStat(self, doseData, name, 95);
                end
                catch err
                    %disp(getReport(err));
                end
            end
        end
        
        function AddNewROI_shape(self, roi)
            shape = lower(roi.ROIShape); 
            c = roi.ROICenter; 
            
            [X, Y, Z] = meshgrid(self.xData, self.yData, self.zData);
            X = X-c(1); Y = Y-c(2); Z = Z-c(3);
            mask = zeros(size(self.data), 'like', false); 
            switch shape
                case {'ball', 'ellipsoid'}
                    s = roi.ROISize; 
                    r = s/2; 
                    I = ((X/r(1)).^2 + (Y/r(1)).^2 + (Z/r(3)).^2) <=1;
                    mask(I)=1;
                case {'cube', 'box'}
                    s = roi.ROISize; 
                    r = s/2; 
                    I = abs(X)<=r(1) & abs(Y)<=r(2)&abs(Z)<r(3);
                    mask(I)=1; 
            end
            self.AddNewROI(mask, roi.ROIName, {'roi', roi});
        end
        
        function AddEmptyROI(self,  name, varargin)
            AddNewROI(self, [],  name, varargin{:});
        end
        
        function AddNewROI(self, mask,  name, varargin)
            options = OptionsMap(varargin{:}); 
            numrois  = NumberOfRois(self); 
            roi = options.getoptioni('roi');
            
            
            if isempty(roi) || ~isstruct(roi)
                %index =  numrois+1; 
                if(numrois==0) index = 1; 
                else
                    index = max(self.ROIIndexs)+1;
                end
                index = options.getoptioni('roi.index', index);  
                if ~exist('name', 'var')
                    name = num2str(index);
                end
                if numrois==0
                    roi = struct('ROIIndex', index, 'ROIName', name); 
                else
                    roi = self.rois{numrois}; 
                    fns = fieldnames(roi);
                    for k=1:numel(fns)
                        fn = fns{k};
                        roi.(fn) = [];
                    end
                    roi.('ROIIndex')= index;
                    roi.('ROIName') = name;
                    roi.('ROIVolume') = 0;
                    roi.('ROICenter') = NaN(1, 3);
                    %fns = {'zContours', 'xContours', 'yContours'};
                end

%                 roicolors =RtStruct2.DefaultColors();
%                 %roi.('ROIColor') = roicolors{mod(numrois, numel(roicolors))+1}; 
%                 roi.('ROIColor') = options.getoptioni('roi.color', roicolors{mod(numrois, numel(roicolors))+1}); 
                colors = distinguishable_colors(numrois+2);
                defaultcolor = colors(end, :);   
                roi.('ROIColor') = options.getoptioni('roi.color',defaultcolor); 
            end
            
            index    = roi.ROIIndex;
            self.rois{numrois+1} = roi;
            self.setIntType();
%             options = OptionsMap(varargin{:}); 
            if isempty(mask) 
                return;
            end
            
            switch lower(self.imageType)
                case {'optmask', 'labelmask', 'perimmask'}
                    self.data(logical(mask)) = cast(index, class(self.data));

                case {'roimask1', 'roimask4', 'roimask8'}
                    self.data = self.data+ cast(mask, class(self.data))*cast(index, class(self.data));
                
                otherwise
                     overlap = options.getoptioni('overlap', 1);
                     mask  = cast(mask, class(self.data));
                     index = cast(index, class(self.data));
                    if overlap
                        self.data = self.data+ mask*2^(index-1);
                    else %replacement
                        I = mask>0; 
                        self.data(I) = mask(I)*2^(index-1);
                    end
            end
        end
        
        function mask = RemoveROI(self, index)
            labelindexs = cellfun(@(x)(x.ROIIndex), self.rois);
            labenames   = cellfun(@(x)(x.ROIName),  self.rois, 'uniformoutput', false);
            if ischar(index)
                name    = index; 
                [~, loc]= ismember(name, labenames);
                if loc==0
                    return
                end
                index  = labelindexs(loc);
            else
               [~, loc]= ismember(index, labelindexs);
               if loc==0
                    return
               end
               name = labenames{loc};
            end
            
            mask = double(self.getROIMask(index));
            
            switch lower(self.imageType)
                case {'optmask','labelmask', 'roimask1', 'roimask4', 'roimask8'}
                    self.data = cast(double(self.data) - index * mask, self.data_type);

                otherwise
                    self.data = cast(double(self.data) - 2^(index-1)*mask, self.data_type);
            end
            self.rois(loc)=[];
        end
        
         function removednames = RemoveEmptyROIs(self)
            removednames={};
            try
                ROIVolume = cellfun(@(x)(x.ROIVolume), self.rois);
            catch
                T         = ROIStatTable(self, {'ROIVolume'},[], {'modifymetadata', 1});
                ROIVolume = T.ROIVolume; 
            end

            I = find(ROIVolume<=0); 
            if ~isempty(I)
                roinames = self.ROINames; 
                removednames=roinames(I);
                self.rois(I)=[];
            end
        end


        function removednames = RemoveROIs_Volume(self, volthreshold)
%             options     = OptionsMap(varargin{:});
%             volthresold = options.getoptioni('volumethreshold', [0 Inf]);
            if ~exist('volthreshold', 'var')
                volthreshold=[0 Inf];
            end
            
            if numel(volthreshold)==1
                volthreshold(2)=Inf; 
            end
            
            removednames={};
            try
                ROIVolume = cellfun(@(x)(x.ROIVolume), self.rois);
            catch
                T         = ROIStatTable(self, {'ROIVolume'},[], {'modifymetadata', 1});
                ROIVolume = T.ROIVolume; 
            end

            I = find(ROIVolume<=volthreshold(1) | ROIVolume>=volthreshold(2)); 
            if ~isempty(I)
                roinames = self.ROINames; 
                removednames=roinames(I);
                %self.rois(I)=[];
                RemoveROIs(self, removednames);
            end
        end

        function mask = RemoveROIs(self, names)
            if ischar(names)
                names = strsplit(names, '|');
            end
            
            if isnumeric(names)
                indexs = names; 
            else
                labelindexs = cellfun(@(x)(x.ROIIndex), self.rois);
                labenames   = cellfun(@(x)(x.ROIName),  self.rois, 'uniformoutput', false);
                [flag, loc] = ismember(names, labenames); 
                loc(loc==0)=[];
                indexs = labelindexs(loc); 
            end
            
            mask = zeros(size(self.data), 'like', false);
            for k=1:numel(indexs)
                mask = mask | RemoveROI(self, indexs(k));
            end
        end
        
        function MergeROIs(self, names, newname, varargin)
            if ischar(names)
                names = strsplit(names, '|'); 
            end
            names = intersect(names, self.ROINames);
            if isempty(names)
                return
            end
            %[newindex, loc] = self.getROIIndex(names{1});
            loc=0; 
            for k=1:numel(names)
                [newindex, loc] = self.getROIIndex(names{k});
                if loc>0
                    break;
                end
            end
            if loc==0
                return;
            end
            mask  = RemoveROIs(self, names);
            
            AddNewROI(self, mask,  newname,{'roi.index', newindex}, varargin{:});
        end
        
        function ReplaceRoiMasks(self, srcroimask, varargin)
            options = OptionsMap(varargin{:});
            selectedroinames = options.getoptioni('selectedroinames', '');
            if isempty(selectedroinames)
                selectedroinames=srcroimask.ROINames;
            end
            selectedroinames=intersect(self.ROINames, selectedroinames);
            for k=1:numel(selectedroinames)
                name = selectedroinames{k};
                newmask = srcroimask.getROIMask(name);
                if ~isempty(newmask)
                    ReplaceRoiMask(self, name, newmask, varargin{:});
                end
            end
        end

        %new mask must be a logical array
        function ReplaceRoiMask(self, name, newmask, varargin)
            [~, loc] = ismember(name, self.ROINames);
            if loc==0 
                return;
            end
            index = self.rois{loc}.ROIIndex;
            mask = self.getROIMask(index);
            %diff = logical(newmask)-mask;
            %diff = cast(logical(newmask)-mask, self.data_type);
            %diff = cast(logical(newmask)-mask, class(self.data));
            %diff = cast(newmask-mask, class(self.data));
            % diff = logical(newmask)-mask;
            % switch lower(self.imageType)
            %     case {'optmask', 'labelmask', 'perimmask', 'roimask1', 'roimask4', 'roimask8'}
            %         %self.data = cast(double(self.data) + diff*index, self.data_type);
            %         self.data = self.data + diff*index;
            %     otherwise
            %         %self.data = cast(double(self.data) + diff*2^(index-1), self.data_type);
            %         self.data = self.data + diff*2^(index-1);
            % end
%             switch lower(self.imageType)
%                 case {'optmask'}
%                     self.data = self.data - index * mask + index*newmask;
%                 otherwise
%                     self.data = self.data - 2^(index-1)*mask + 2^(index-1)*newmask;
%             end
            options  = OptionsMap(varargin{:});
            overwriteoccupied= options.getoptioni_numeric('overwriteoccupied', 0);
            datatype = class(self.data);
            switch lower(self.imageType)
                case {'labelmask', 'perimmask', 'roimask1', 'roimask4'}
                    %self.data = cast(double(self.data) + diff*index, self.data_type);
                    if ~overwriteoccupied
                    newmask = newmask & (mask | self.data==0);
                    self.data = self.data  + cast(index*newmask, datatype) - cast(index*mask, datatype);
                    else
                        self.data(newmask)=index;
                    end
                case {'optmask',  'roimask8'}
                    n = floor(log2(index)/8)*8;
                    occupied = bitshift(self.data, -n);
                    occupied = bitand(occupied, 255);
                    newmask = newmask & (mask | ~occupied);
                    self.data = self.data  + cast(index*newmask, datatype) - cast(index*mask, datatype);
                otherwise
                    %self.data = cast(double(self.data) + diff*2^(index-1), self.data_type);
                    self.data = self.data + cast(2^(index-1)*newmask, datatype)-cast(2^(index-1)*mask, datatype);
            end
        end
        
        function AddRoiMaskImage(self, maskimg, varargin)
            if isempty(maskimg)
                return; 
            end
            if isempty(self.data)
                imagetype = self.imageType; 
                self.copyStruct(VolHeader(maskimg));
                self.ZeroData();
                self.imageType = imagetype; %recover the original image type
            end
            
            roinames = maskimg.ROINames; 
            rois = maskimg.rois; 
            options = OptionsMap(varargin{:});
            if ~options.isoptioni('keeproiindex')
                options.setOption('keeproiindex', 0);
            end
            useorigindex = options.getoptioni_numeric('keeproiindex'); 
            dstroiindex  = options.getoptioni_numeric('dstroiindex'); 
            
            selectedroinames = options.getoptioni('selectedroinames', '');
            if ~isempty(selectedroinames)&&ischar(selectedroinames)
                selectedroinames=strsplit(selectedroinames, '|');
            end
            
            if ~isempty(selectedroinames)
                [roinames, IA, IB] = intersect(selectedroinames, roinames, 'stable');
                rois = rois(IB);
                if ~isempty(dstroiindex)
                    dstroiindex=dstroiindex(IA);
                end
            end

            for k=1:numel(roinames)
                roiname = roinames{k};
%                 if ~isempty(selectedroinames) && ~ismember(roiname, selectedroinames)
%                     continue;
%                 end
                mask = maskimg.getROIMask(roiname); 
                %roi  = maskimg.rois{k};
                roi = rois{k};
                if ~isempty(dstroiindex)
                    roi.ROIIndex = dstroiindex(k);
                elseif ~useorigindex
                    %roi.ROIIndex = numel(self.rois)+1;
                    roi.ROIIndex = MaxROIIndex(self)+1;
                end
                    
                %options.setOption('roi', roi);
                
                AddRoiMask(self, mask, roiname, {'roi', roi}, {'keeproiindex', 1});
            end
        end

        function index = MaxROIIndex(self)
            index = max(self.ROIIndexs);
            if isempty(index)
                index =0;
            end
        end
        
        function AddRoiMask(self, mask, oarname, varargin)
            roinames = self.ROINames;
            if ismember(oarname, roinames)
                self.ReplaceRoiMask(oarname, mask);
            else
                options = OptionsMap(varargin{:});
                roi     = options.getOption('roi');
                roi.ROIName = oarname; 
                useorigindex = options.getoptioni_numeric('keeproiindex', 0); 
                switch lower(self.imageType)
                    case {'roimask1'}
                        nbits = 1; 
                    case {'roimask4'}
                        nbits = 4; 
                    case {'roimask8'}
                        nbits = 8;     
                    otherwise
                        nbits = 0; 
                end
                roiindexs = self.ROIIndexs; 
                
                if useorigindex &&isfield(roi, 'ROIIndex')
                    roiindex0 = roi.ROIIndex; 
                else
                    %roiindex0 = numel(roiindexs)+1;
                    if isempty(roiindexs)
                        roiindex0 = 1;
                    else
                        roiindex0 = max(roiindexs)+1;
                    end
                end

                roi.ROIIndex = roiindex0; 
                if nbits >0
                    level = -1; 
                    occupied= true;
                    maskval = bitshift(1, nbits)-1; 
                    maxval  = 0; 
                    while(occupied||maxval==maskval)
                        level     = level+1;
                        %offsetval = (2^nbits)^level; 
                        shifts    = nbits*level; 
                        
                        if nbits>=8
                            roiindex  = bitshift(roiindex0, shifts); 
                        else
                            bits          = bitshift(roiindexs, -shifts); 
                            bits(bits>maskval)=[]; maxbit = max(bits);
                            if isempty(maxbit) 
                                maxbit = 0; 
                            end
                            roiindex= bitshift(maxbit+1, shifts); 
                        end
                        occupied  = ismember(roiindex, roiindexs);
                        if ~occupied
                            basemask  = bitand(bitshift(self.data(:), -shifts), maskval); 
                            occupied  = any(basemask>0 & mask(:));
                        end
                    end
                    roi.ROIIndex = roiindex;
                end
                if ~isfield(roi, 'ROIColor')
                    N      = numel(self.ROINames)+1;
                    colors = distinguishable_colors(N);
                    roi.ROIColor=colors(N, :); 
                end
                options.setOption('roi', roi);
                self.AddNewROI(mask, oarname, options);
            end
        end

        function ToStdName(self, parser)
            if ~exist('parser', 'var')
                parser = ai.ROIName; 
            end
            for k=1:numel(self.rois)
                roi = self.rois{k};
                origname = roi.ROIName; 
                name     = parser.ParseROI(origname);
                roi.OrigName = origname; 
                roi.ROIName  = name; 
                self.rois{k} = roi; 
            end
        end
        


        function res = GetSubMaskImage(self, names, varargin)
            options     = OptionsMap(varargin{:});
            useorigindex = options.getoptioni_numeric('keeproiindex', 0); 
            if ischar(names)
                names = strsplit(names, '|'); 
            elseif isnumeric(names)
                indexs     = names; 
                roiindexs  = cellfun(@(x)(x.ROIIndex), self.rois, 'UniformOutput', true, 'ErrorHandler', @errorFunc); 
                roinames   = cellfun(@(x)(x.ROIName),  self.rois, 'UniformOutput', false, 'ErrorHandler', @errorFunc);  
                [~, loc]   = ismember(indexs, roiindexs); 
                names = roinames(loc);
            elseif isstruct(names)
                str       = names;
                fieldname = StructBase.getfieldx_default(str, 'FieldName', 'ROIName'); 
                fieldvalue= StructBase.getfieldx(str, 'FieldValue'); 
                matchmode = StructBase.getfieldx_default(str, 'MatchMode', 'regexp');
                roinames  = self.ROINames; 
                switch lower(matchmode)
                    case {'regexp'}
                        I=cellfun(@(x)(~isempty(regexp(x.(fieldname), fieldvalue, 'match', 'once'))), self.rois); 
                        names = roinames(I); 
                    case {'regexpi'}
                        I=cellfun(@(x)(~isempty(regexpi(x.(fieldname), fieldvalue, 'match', 'once'))), self.rois); 
                        names = roinames(I); 
                    case {'strcmp'}
                        I=cellfun(@(x)(strcmp(x.(fieldname), fieldvalue)), self.rois); 
                        names = roinames(I); 
                    case {'strcmpi'}
                        I=cellfun(@(x)(strcmpi(x.(fieldname), fieldvalue)), self.rois); 
                        names = roinames(I); 
                    case {'ismember'}
                        I=cellfun(@(x)(ismember(x.(fieldname), fieldvalue)), self.rois); 
                        names = roinames(I); 
                end
            end
            
            outputmasktype = options.getoptioni('outputmasktype', self.imageType);
            res  = ROIMaskImage(VolHeader(self)); 
            res.imageType=outputmasktype; 
            res.ZeroData();
            placehold=options.getoptioni_numeric('PlaceHold', 0);
            if placehold
                K = numel(names);
                for k=1:K
                    rois0{k}=struct('ROIIndex', k,...
                        'ROIName', names{k},...
                        'ROIColor', [],...
                        'RTROIInterpretedType',    '',...
                        'ROIPhysicalProperty',      '', ...
                        'ROIPhysicalPropertyValue', []); 
                end
                res.rois = rois0; 
                res.SetDistinguishableColors;
            end
%             data = zeros(size(self.data)); 
%             newrois={};
            for k=1:numel(names)
                name   = names{k}; 
                [index, m] = self.getROIIndex(name);
                if m<=0
                    continue;
                end
                roi = self.rois{m};
                roi.ROIName  = name; 
                if ~useorigindex
                    roi.ROIIndex = k;
                end
                %newrois{k} = roi;  
                
                mask = self.getROIMask(index);
                
%                 switch lower(self.imageType)
%                     case {'optmask', 'perimmask', 'labelmask'}
%                         data(mask) = roi.ROIIndex;
%                     case {'roimask4', 'roimask8'}
%                         data = data+ mask*roi.ROIIndex;
%                     otherwise
%                         data = data+ mask*2^(roi.ROIIndex-1);
%                 end
                res.AddRoiMask(mask, name, {'roi', roi}, {'keeproiindex', 1});
            end
%             res.rois = newrois; 
%             res.setData(data); 
            res.setIntType(); 
        end
        
        function res = GetMapRoiMask(self, map, varargin)
            options  = OptionsMap(varargin{:}); 
            matchmode= options.getoptioni('matchmode', 'regexp'); 
            res = ROIMaskImage(self); 
            names=[]; expstrs=[];
            ROIIndex = [];
            if isa(map, 'OptionsMap')
                names      = map.getOptionNames(); 
                expstrs    = cellfun(@(x)(map.getOption(x)), names, 'uniformoutput', false); 
                
            elseif istable(map) %map is from ai.rtstruct.Nomenclature
                names = map.StandardID; 
                switch lower(matchmode)
                    case {'regexp', 'regexpi'}
                        expstrs = map.RegExpPattern;
                    otherwise
                        expstrs = map.MatchROIName;
                        expstrs = cellfun(@(x)(['^' x '$']), expstrs,  'uniformoutput', false); 
                end
            end
            
            ROIIndex   = options.getoptioni('ROIIndex', [1:numel(names)]); 
            
            orignames  = self.ROINames;
            data = zeros(size(self.data)); 
            for k=1:numel(names)
                name   = names{k}; 
                exp    = expstrs{k}; 
                a  = regexpi(orignames, exp);
                mm = find(cellfun(@(x)(~isempty(x)), a)); 
                if numel(mm)>1
                    disp(['warning: more than one rois map to', name]); 
                end
                if ~isempty(mm)
                    m   = mm(1);
                    roi = self.rois{m};
                    roi.OrigName = roi.ROIName; 
                    roi.ROIName  = name; 
                    roi.OrigNumber=roi.ROIIndex;
                    index = ROIIndex(k); 
                    roi.ROIIndex= index;
                    newrois{k}  = roi;  
                    mask = self.getROIMask(roi.OrigNumber);
                    if strcmpi(self.imageType, 'optmask') ||strcmpi(self.imageType, 'labelmask')
                        data(mask) = index; 
                    else
                        data = data+ mask*2^(index-1);
                    end
                end
            end
            res.rois = newrois; 
            res.setData(data); 
            res.setIntType(); 
        end
        
        
        function SetDistinguishableColors(self)
            N = NumberOfRois(self);
            colors = distinguishable_colors(N); 
            for k=1:N
                self.rois{k}.ROIColor = colors(k, :); 
            end
        end

        function MorphRoi(self, roiname, fun, se)
            mask = self.getROIMask(roiname);
            newmask = fun(mask, se);
            self.ReplaceRoiMask(roiname, newmask);
        end
        
        function maskimgs = SplitBits(self, nbits)
            Index    = cellfun(@(x)(x.ROIIndex), self.rois);
            maxIndex = max(Index);
            N = ceil(maxIndex/nbits);
            for n=1:N
                %rois = self.rois(I);
                data = bitand(self.data, (2^(nbits)-1)*2^((n-1)*nbits));
                maskimg = ROIMaskImage(self);
                maskimg.setData(data);
                I    = (Index>(n-1)*nbits) & (Index<=n*nbits);
                maskimg.rois = self.rois(I);
              if nbits<=16
                maskimg.setDataType('uint16');
              elseif nbits<=32
                maskimg.setDataType('uint32');
              end
              maskimgs{n} = maskimg; 
            end
        end
        
        function roiview(self, varargin)
            self.imview({'roi1', self}, varargin{:});
        end
        
        function ct = CreatePseudoCT(self, roinames,  varargin)
             if ~exist('roinames', 'var') || isempty(roinames)
                roinames = self.ROINames; 
             end
             ct = OverrideDensity(self, [], roinames, varargin{:});
             ct.imageType = ct; 
             ivdt = CTImage.DefaultIVDT_kVCT;
             prof = ProfileData(ivdt(2, :), ivdt(1, :));
             ct.data = round(prof.getValue(ct.data, 0));
             ct.setShort; 
        end

        function density = OverrideDensity(self, density0, roinames, varargin)
            options = OptionsMap(varargin{:});
            nomen   = options.getOption('Nomenclature');
            
            if ~exist('roinames', 'var') || isempty(roinames)
                roinames = self.ROINames; 
                if isempty(nomen)
                    I = find(cellfun(@(x)(strcmpi(x.ROIPhysicalProperty, 'REL_ELEC_DENSITY')), self.rois));
                    roinames=roinames(I);
                end
            end
            
            if ~exist('density0', 'var') || isempty(density0)
                density = VolHeaderImage(self); 
                density.imageType='density';
                density.data(:)=0; 
                density.data =double(density.data);
                density.setFloat();
            else
                density = VolHeaderImage(density0);
            end
            
            if numel(roinames)==0
                return
            end
            
            T = self.ROIStatTable({'numvox'}, roinames);
            numvox  = T.numvox; 
            [val, I]= sort(numvox, 'descend');
            J   = val<=0; 
            I(J)= [];
            roinames=roinames(I);

            
           
            map = containers.Map(self.ROINames,self.rois);
            if isempty(nomen)
                
                for k=1:numel(roinames)
                    %             ROIPhysicalProperty: 'REL_ELEC_DENSITY'
    %     ROIPhysicalPropertyValue: 0.0100
                    roiname = roinames{k};
                    try
                        roi = map(roiname);
                    
                        if ~strcmpi(roi.ROIPhysicalProperty, 'REL_ELEC_DENSITY')
                            continue;
                        end
                        denval = roi.ROIPhysicalPropertyValue;
                        mask = self.getROIMask(roi.ROIName);
                        density.data(mask)=denval;
                    catch
                    end
                end
            else
                [T, unmatched] = nomen.MapROINames(roinames);
                MatchROIName = T.MatchROIName;
                [I, loc] = ismember(roinames, MatchROIName);
                roinames = roinames(I);
                loc = loc(I);
                RelElecDensity=T.RelElecDensity(loc);
                for k=1:numel(roinames)
                    roiname = roinames{k};
                    try
                        roi = map(roiname);
                        denval = RelElecDensity(k);
                        if ~isnan(denval)
                            mask = self.getROIMask(roi.ROIName);
                            density.data(mask)=denval;
                        end
                    catch
                    end
                end
            end
        end
        
        %dilate roi into empty space
        function roidilate(self, se, roinames, varargin)
            if ~exist('roinames', 'var') || isempty(roinames)
                roinames = self.ROINames; 
            end
            %bg = self.data==0; 
            for k=1:numel(roinames)
                roiname = roinames{k};
                maskimg = self.getROIMaskImage(roiname);
                bg1 = self.data==0 | maskimg.data>0;
                maskimg.immorph('dilate', se, varargin{:});
                newmask = maskimg.data>=0.5 & bg1;
                self.ReplaceRoiMask(roiname, newmask);
            end
        end
        
        function outmaskimage = roimorph(self, roinames, varargin)
            if ~exist('roinames', 'var') || isempty(roinames)
                roinames = self.ROINames; 
            end
            if ischar(roinames)
                roinames = strsplit(roinames, '|');
            end
            roinames = intersect(roinames, self.ROINames, 'stable'); 
            if isa(self, 'OptMaskImage')
                outmaskimage = OptMaskImage(VolHeader(self));
                outmaskimage.imageType='optmask';
            else
                outmaskimage = ROIMaskImage(VolHeader(self));
                outmaskimage.imageType = self.imageType; 
            end
            
            outmaskimage.ZeroData();
            for k=1:numel(roinames)
                roiname = roinames{k};
                maskimg = self.getROIMaskImage(roiname);
                maskimg.immorph(varargin{:});
                newmask = maskimg.data>=0.5;
                if nargout==0
                    self.ReplaceRoiMask(roiname, newmask);
                else
                    outmaskimage.AddRoiMask(newmask,roiname);
                end
            end
        end
        
        function outmaskimage = roimorph_2D(self, roinames, varargin)
            if ~exist('roinames', 'var') || isempty(roinames)
                roinames = self.ROINames; 
            end
            if ischar(roinames)
                roinames = strsplit(roinames, '|');
            end
            roinames = intersect(roinames, self.ROINames, 'stable'); 
            outmaskimage = ROIMaskImage(VolHeader(self));
            outmaskimage.imageType = self.imageType; 
            outmaskimage.ZeroData();
            for k=1:numel(roinames)
                roiname = roinames{k};
                maskimg = self.getROIMaskImage(roiname);
                maskimg.immorph_2D(varargin{:});
                newmask = maskimg.data>=0.5;
                if nargout==0
                    self.ReplaceRoiMask(roiname, newmask);
                else
                    outmaskimage.AddRoiMask(newmask,roiname);
                end
            end
        end
        
        
        

        %the following code only works for optmask
         function [TalName, TalValue]= ImageCoor2ROIName(labelimg, Centroid)
            if ~strcmpi(labelimg.imageType, 'optmask') && ~strcmpi(labelimg.imageType, 'labelmask')
                return;
            end
            
            Cindex      = labelimg.imageCoor2index(round(Centroid));
            TalValue    = labelimg.data(Cindex);
            labelindexs = cellfun(@(x)(x.ROIIndex), labelimg.rois);
            labenames   = cellfun(@(x)(x.ROIName),  labelimg.rois, 'uniformoutput', false);
            [~, loc1]   = ismember(TalValue, labelindexs);
            TalName     = cell(numel(loc1), 1); I = loc1>0; 
            TalName(I)  = labenames(loc1(I));
            TalName(~I) = arrayfun(@(x)('unknown'),ones(sum(~I), 1), 'uniformoutput', false);
        end
        
        function T = ROIRegionProps(self, roiname, propsnames)
            if ~exist('propsnames', 'var') || isempty(propsnames)
                propsnames = 'all';
            end
            mask = self.getROIMask(roiname); 
            T = regionprops3(mask,propsnames);
        end
        
        function RemoveOutOfBody(self,bodystr)
            if ~exist('bodystr', 'var') || isempty(bodystr)
                bodystr = 'Body';
            end
            bodynames = self.GetMatchNames(bodystr); 
            if isempty(bodynames)
                return;
            end
            mask = self.getROIMask(bodynames{1});
            self.data(~mask)=0; 
        end
        
        function RemoveOutOfBox(self, boxcenter, boxsize, roinames)
            boxsize=boxsize(:)'; boxcenter = boxcenter(:)';
            if numel(boxsize)==3
                boxsize=[boxsize boxsize];
            end
            [X, Y, Z] = meshgrid(self.xData, self.yData, self.zData); 
            Ix = X<boxcenter(1)-boxsize(1) | X>boxcenter(1)+boxsize(4);
            Iy = Y<boxcenter(2)-boxsize(2) | Y>boxcenter(2)+boxsize(5);
            Iz = Z<boxcenter(3)-boxsize(3) | Z>boxcenter(3)+boxsize(6);
            oob= Ix |Iy |Iz; 
            if ~exist('roinames', 'var')
                self.data(oob)=0;
            else
                if ischar(roinames)
                    roinames = strsplit(roinames, '|');
                end
                for k=1:numel(roinames)
                    roiname = roinames{k};
                    mask=self.getROIMask(roiname);
                    mask(oob)=0;
                    self.ReplaceRoiMask(roiname, mask); 
                end
            end
        end

        function outmaskimg = DecomposeLR(self, srcroiname, dstroiname0, bodystr, varargin)
            options = OptionsMap(varargin{:});
            %mask    = self.getROIMask(srcroiname);
            [mask, srcroiname] = self.getMergedMask(srcroiname);
            if isempty(mask)
                outmaskimg=[];
                return;
            end
            % if exist('bodystr', 'var') &&~isempty(bodystr)
            %     bodynames = self.GetMatchNames(bodystr); 
            %     bodymask = self.getROIMask(bodynames{1}); 
            %     C = self.centerOfMassIndex(bodymask);
            %     cx = C(2);
            % else
            %     cx = self.x_dim/2;
            % end
            % [X] = meshgrid(1:self.x_dim, 1:self.y_dim, 1:self.z_dim); 


            X = self.DicomCoorGrid;
            if exist('bodystr', 'var') &&~isempty(bodystr)
                bodynames = self.GetMatchNames(bodystr); 
                bodymask = GetSubMaskImage(self, bodynames);
                %bodymask = self.getMergedMaskImage(bodystr);
                T = bodymask.ROIStatTable_regionprops('ROICenter');
                c = T.ROICenter;
                cx = c(1, 1);
                % bodymask = self.getROIMask(bodynames{1}); 
                % C = self.centerOfMassIndex(bodymask);
                % cx = C(2);
            else
                c = self.dcmcoor_center();
                cx= c(1);
            end


            mask_L = mask; mask_L(X<cx)=0;
            mask_R = mask; mask_R(X>=cx)=0;
            
            if nargout==0
                self.AddRoiMask(mask_L,[dstroiname0 '_L']);
                self.AddRoiMask(mask_R,[dstroiname0 '_R']);
                keepsrc = options.getoptioni('KeepSrcROI', 1); 
                if ~keepsrc
                    self.RemoveROIs(srcroiname); 
                end
            else
                outmaskimg=ROIMaskImage(VolHeader(self));
                outmaskimg.ZeroData();
                outmaskimg.AddRoiMask(mask_L,[dstroiname0 '_L']);
                outmaskimg.AddRoiMask(mask_R,[dstroiname0 '_R']);
            end
        end
        
        function outmaskimg = CreateROIMargin(self, srcroiname, margin,varargin)
            options = OptionsMap(varargin{:});
            is2D = options.getoptioni('Is2D', false);  
            if ~is2D
                se = self.strel3d_noniso(margin);
            else
                se = self.strel2d_noniso(margin);
            end
            
            isInner = options.getoptioni('IsInnerMargin', false);  
            
            if isInner
                if is2D
                    outmaskimg = self.roimorph_2D(srcroiname,'erode', se);
                else
                    outmaskimg = self.roimorph(srcroiname,'erode', se);
                end
            else
                if is2D
                    outmaskimg = self.roimorph_2D(srcroiname,'dilate', se);
                else
                    outmaskimg = self.roimorph(srcroiname,'dilate', se);
                end
            end
            isShell = options.getoptioni('IsShell', false);
            if isShell
                mask = uint16(self.getROIMask(srcroiname)); 
                if isInner
                    outmaskimg.data = mask-outmaskimg.data;
                else
                    outmaskimg.data = outmaskimg.data-mask;
                end
            end
            avoidroiname = options.getoptioni('AvoidROIName'); 
            if ~isempty(avoidroiname)
                avoidmask = self.getMergedMask(avoidroiname);
                outmaskimg.data(avoidmask)=0;
            end
            OutROIName = options.getoptioni('DstROIName', ''); 
            if ~isempty(OutROIName)
                outmaskimg.ResetROINames(srcroiname, OutROIName);
            end
        end
        
        function KeepLargestComponent2D(roimask, roinames)
            if ~exist('roinames', 'var')
                roinames = roimask.ROINames; 
            end
            if ischar(roinames)
                roinames = strsplit(roinames, '|'); 
            end
            T1 = roimask.ROIStatTable_regionprops('BoundingBox|Volume', roinames);
            volumes = T1.Volume; I = volumes<=0; T1(I, :)=[]; roinames = T1.ROINames; bb = T1.BoundingBox;
            propsnames = {'Area',  'PixelIdxList'};
            numslices = roimask.z_dim; 
            mask2D = zeros(roimask.y_dim, roimask.x_dim, 'like', false); 
            for k=1:numel(roinames)
                roiname= roinames{k};
                
                mask0  = roimask.getROIMask(roiname); 
                mask1  = mask0; 
                slices=round(bb(k, 3))+[0:(bb(k, 6)-1)];
                %for n=1:numslices
                for n=slices
                    T = regionprops('table', mask0(:, :, n), propsnames);
                    if size(T, 1)<2
                        continue;
                    end
                    try
                    Volume = T.(propsnames{1}); 
                    [~, I] = sort(Volume, 'descend'); 
                    m = I(1); 
                    list   = T.( propsnames{2}){m};
                    mask2D(:)=0;
                    mask2D(list)=1; 
                    mask1(:, :, n)=mask2D; 
                    catch
                        disp("error: KeepLargestComponent2D");
                    end
                end
                roimask.ReplaceRoiMask(roiname, mask1);
            end
        end
        
        function KeepLargestComponent(roimask, roinames, varargin)
            options = OptionsMap(varargin{:});
            is2d = options.getoptioni_numeric('Is2D', 0); 
            if is2d
                KeepLargestComponent2D(roimask, roinames); 
                return;
            end
            
            if ~exist('roinames', 'var')
                roinames = roimask.ROINames; 
            end
            if ischar(roinames)
                roinames = strsplit(roinames, '|'); 
            end
            propsnames = {'Volume',  'VoxelIdxList'};
            for k=1:numel(roinames)
                roiname= roinames{k};
                T = ROIRegionProps(roimask, roiname, propsnames);
                Volume = T.Volume; 
                if isempty(Volume)
                    continue;
                end
                [~, I] = sort(Volume, 'descend'); 
                m = I(1); 
                list   = T.VoxelIdxList{m};
                mask1  = zeros(size(roimask.data), 'like', false); 
                mask1(list)=1; 
                roimask.ReplaceRoiMask(roiname, mask1);
            end
        end
        
        function rois = Convert2ImageContours(roimask, dirs, smoothwin, varargin)
            if ~exist('dirs', 'var')
                dirs = 'zxy'; 
            end
            if ~exist('smoothwin', 'var') || isempty(smoothwin)
                smoothwin = 0; 
            end 
%             if ~exist('zstride', 'var') || isempty(zstride)
%                 zstride = 1; 
%             end
            M = numel(dirs);
            N = numel(smoothwin); smoothwin((N+1):M)=smoothwin(N); 
%             N = numel(zstride); zstride((N+1):M)=zstride(N); 
            for m=1:numel(dirs)
                switch lower(dirs(m))
                    case 'z'
                        pixdim = (roimask.x_pixdim+roimask.y_pixdim)/2;
                    case 'x' 
                        pixdim = (roimask.z_pixdim+roimask.y_pixdim)/2;
                    case 'y'
                        pixdim = (roimask.z_pixdim+roimask.x_pixdim)/2;
                end
                smoothwins(m) = smoothwin(m) /pixdim; 
            end
            
            options = OptionsMap(varargin{:}); 
            ContourStride = options.getoptioni( 'ContourStride', 1);
            ContourStrideMatchName = options.getoptioni( 'ContourStrideMatchName', '.*');
            %ContourMinPoints = options.getoptioni( 'ContourMinNumPoints', 3);
            roinames = roimask.ROINames;
            rois = roimask.rois; 
            for k=1:numel(roinames)
                roiname = roinames{k};
                mask    = double(roimask.getROIMask(roiname));
                zstride = 1; 
                if ~isempty(regexpi(roiname, ContourStrideMatchName, 'ONCE'))
                    zstride = ContourStride;
                end
                N = numel(zstride); zstride((N+1):M)=zstride(N); 
                for m=1:M
                    dir1 = dirs(m); contname = [dir1 'Contours']; 
                    if isempty(StructBase.getfieldx(rois{k}, contname))
                        rois{k}.(contname) = roimask.Mask2ImageContours(mask, dir1,   smoothwins(m), zstride(m), varargin{:});
                    end
                end
            end
            
            if nargout<1
                roimask.rois = rois; 
            end
        end
        
        function RemoveROIFields(self, fns)
            if ischar(fns)
                fns = strsplit(fns, '|'); 
            end
            N = numel(self.rois); 
            for k=1:N
                try
                    fns1 = intersect(fieldnames(self.rois{k}), fns); 
                    self.rois{k} = rmfield(self.rois{k}, fns1); 
                catch
                end
            end
        end
        
        function flag = With_zContours(self)
            flag = isfield(self.rois{1}, 'zContours'); 
        end
        
        function [names] = GetMatchNames(self, str)
            [names] = self.GetMatchROINames(self.ROINames, str);
        end
        
          %self must be a labelmask
        function MaskedMorph(self, ops, varargin) 
            %se = CT.strel3d_noniso([5 5 3]);
            options = OptionsMap(varargin{:});
            intensitymask = options.getoptioni('intensitymask');
            intensityimage = options.getoptioni('intensityimage'); 
            if isempty(intensitymask) && ~isempty(intensityimage)
                intensitythreshold = options.getoptioni('intensitythreshold'); 

                if isempty(intensitythreshold)
                    intensityprctile = options.getoptioni('intensityprctile', [10 90]); 
                    mask=self.data>0; 
                    intensitydata = intensityimage.data(mask(:));
                    intensitythreshold=[prctile(intensitydata, intensityprctile(1)), prctile(intensitydata, intensityprctile(2))];
                end
                if numel(intensitythreshold)==1
                    intensitythreshold=[intensitythreshold intensitythreshold];
                end
                intensitymask = intensityimage.data>=intensitythreshold(1)&intensityimage.data<intensitythreshold(2);
            end
            
            origdata= self.data; 
            origmask= origdata>0; 
            newdata = origdata; 
            
            if isstruct(ops)
                ops = arrayfun(@(x)(x), ops, 'uniformoutput', false);
            end
            
            for k=1:numel(ops)
                op = ops{k};
                opname = StructBase.getfieldx(op, 'OperationName');
                if isempty(opname)
                    continue;
                end
                paras = StructBase.getfieldx(op, 'Parameters');
                switch lower(opname)
                    case 'maskeddilate'
                        se = self.strel3d_noniso(paras);
                        dilatemask = imdilate(origmask, se); 
                        inclusionmask = dilatemask&~origmask&intensitymask;
                        [~, idx] = bwdist(origmask); 
                        newdata(inclusionmask)=origdata(idx(inclusionmask)); 
                    case 'maskederode'
                        se = self.strel3d_noniso(paras);
                        erodemask  = imerode(origmask, se); 
                        exclusionmask = origmask&~erodemask&~intensitymask; 
                        newdata(exclusionmask)=0; 
                    case 'imopen'
                        se = self.strel3d_noniso(paras);
                        newdata  = imopen(newdata, se); 
                    case 'imclose'
                        se = self.strel3d_noniso(paras);
                        newdata  = imclose(newdata, se); 
                    case 'imdilate'
                        se = self.strel3d_noniso(paras);
                        newdata  = imdilate(newdata, se); 
                    case 'imerode'
                        se = self.strel3d_noniso(paras);
                        newdata  = imerode(newdata, se);      
                end
            end
            self.setData(newdata);
        end
        
        %multiple rois should not appear in the same slice (e.g. sigmoid
        %and rectum)
        function SliceMonopoly(self, roinames, varargin)
            options = OptionsMap(varargin{:});
            priority = options.getoptioni('Priority', 'largest');
            if ischar(roinames)
                roinames = strsplit(roinames, '|');
            end
            numrois   = numel(roinames);  numslices = self.z_dim; 
            labelmask = self.GetSubMaskImage(roinames); 
            if ~strcmpi(labelmask.imageType, 'roimask')
                labelmask = self.roimask2labelmask(labelmask); 
            end
            for n=1:numslices
                mask0 = labelmask.data(:, :, n); 
                if sum(mask0(:))==0
                    continue; 
                end
                T = regionprops('table', mask0>0, {'Area', 'PixelIdxList'});
                M = size(T, 1);
                if M==0
                    continue;
                end
                idxs = T.('PixelIdxList');
                for m=1:M
                    idx = idxs{m};
                    [C,IA,IC] = unique(mask0(idx)); 
                    MM = numel(C); 
                    if MM<=1
                        continue;
                    end
                    switch lower(priority)
                        case 'first'
                            kk=1;
                        case 'last'
                            kk=MM;
                        otherwise
                            counts=zeros(1, MM);
                            for mm=1:MM
                                counts(mm) = sum(IC==mm);
                            end
                            [~, kk] = max(counts); 
                    end
                    mask0(idx)=C(kk); 
                end
                labelmask.data(:, :, n)=mask0; 
            end
            for k=1:numrois
               roiname = roinames{k};
               ReplaceRoiMask(self, roiname, labelmask.getROIMask(roiname));
            end
        end
        
%         function SliceMonopoly0(self, roinames, varargin)
%             options = OptionsMap(varargin{:});
%             if ischar(roinames)
%                 roinames = strsplit(roinames, '|');
%             end
%             monomaskimg = ROIMaskImage(VolHeader(self));
%             monomask = zeros(size(self.data), 'like', false);
%             monomaskimg.setData(monomask); 
%             numrois  = numel(roinames);  numslices = self.z_dim; 
%             for k=1:numrois
%                 roiname = roinames{k};
%                 submasks{k} = self.getROIMask(roiname); 
% %                 numvoxels(:, k) = squeeze(sum(sum(submasks{k}, 1), 2)); 
%                 monomask = monomask|submasks{k}; 
%             end
% 
%             priority = options.getoptioni('Priority', 'largest');
%             monomode = options.getoptioni('MonoMode', 'merge');
%             connectivity = options.getoptioni('Connectivity', 'LargestComponent');
%             if strcmpi(connectivity, 'LargestComponent')
%                 monomaskimg.AddRoiMask(monomask, 'mono');
%                 monomaskimg.KeepLargestComponent2D();
%                 monomask = logical(monomaskimg.data);
%             end
%             
%             for k=1:numrois
%                  submask   = submasks{k}&monomask;
%                  numvoxels(:, k) = squeeze(sum(sum(submask, 1), 2)); 
%             end
% 
%             [maxvoxels, loc] = max(numvoxels, [],2);
%             slicesize = self.x_dim*self.y_dim; 
%             for n=1:numslices
%                 if maxvoxels(n)==0
%                     continue;
%                 end
%                 switch priority
%                     case 'first'
%                        m = 1; 
%                     otherwise
%                        m = loc(n); 
%                 end
%                 I = monomask(:, :, n); I=find(I(:))+(n-1)*slicesize;
%                 for k=1:numrois
%                     if k==m
%                         if strcmpi(monomode, 'merge')
%                             submasks{k}(I)= 1; 
%                         end
%                     else
%                         submasks{k}(I)= 0;
%                     end
%                 end
%             end
%             
%            for k=1:numrois
%                roiname = roinames{k};
%                ReplaceRoiMask(self, roiname, submasks{k});
%            end
%         end
    end
    
    methods (Static)
        function res = DefaultInterpMethod()
            res = 'nearest'; 
        end
        
    %A and B are binary image
        function coeff = calcDiceCoeff(A, B)
%             D = abs(A-B); 
%             coeff = 1-2*sum(D(:))./(sum(A(:))+sum(B(:))); 
            coeff = 2*sum(A(:)&B(:))/(sum(A(:))+sum(B(:)));
        end
        
        function name = regexpi_match(names, exp)
            res = regexpi(names, exp);
            if ~isempty(res)
                name = res{1}; 
            end
        end
        
        function [conformity, volume, targetVolume] = getConformityIndex(dose, targetMask, prescription)
            dose = dose(:);
            targetMask = targetMask(:);
            I = dose>prescription;
            volume = sum(I); 
            targetVolume = sum(I & targetMask);
            conformity = volume/targetVolume; 
        end
        
        function [uniformity, volume] = getUniformIndex(dose, targetMask, maxPrt, minPrt)
            if ~exist('maxPrt', 'var')
                maxPrt = 98; 
            end
            
            if ~exist('minPrt', 'var')
                minPrt = 2; 
            end
            
            I = targetMask(:)>0; 
            dose = dose(I);
            volume = sum(I); 
            mindose = prctile(dose, minPrt); 
            maxdose = prctile(dose, maxPrt); 
            uniformity = maxdose/mindose; 
        end
        
        function [newdata] = seperateBits(data0, threshbit)
            k=0; 
            data = data0; 
            while(1)
                k=k+1;
                newdata{k} = bitand(data, 2^threshbit-1);
                data = (data-newdata{k})/2^threshbit;
                if max(data(:))<=0 
                    break; 
                end
            end
        end
        
        function [newdata] = combineBits(data0, threshbit)
            newdata = data0{1}; 
            multiplier = uint64(2^threshbit); 
            for k=2:numel(data0)
               newdata = newdata + data0{k}*multiplier^(k-1);
            end
        end
        
        function IntImageTransform(tesimg, fun, varargin)
            workdata = ROIMaskImage.seperateBits(tesimg.data, 32); 
            workimg = VolHeaderImage(tesimg); 
            workimg.setInt(); 
            for k=1:numel(workdata)
                workimg.setData(workdata{k}); 
                maskdata{k} = fun(workimg, varargin{:}); 
            end
            tesdata = ROIMaskImage.combineBits(maskdata, 32);
            tesimg.setData(tesdata); 
        end
        
        function shelldata=GetSurfaceMask(bw)
            S = size(bw); S((numel(S)+1):3) = 1; 
            if S(3)==1
                se = strel('disk',  1);
            else
                se = strel('sphere',1);
            end
            inner = imerode(bw, se); 
            shelldata = bw&~inner;  
        end
        
        function shelldata=GetExteriorSurfaceMask(bw, board)
            S = size(bw); S((numel(S)+1):3) = 1; 
%             if S(3)==1
%                 se0 = strel('disk',  board-1);
%                 se1 = strel('disk',  board);
%             else
%                 se0 = strel('sphere',board-1);
%                 se1 = strel('sphere',board);
%             end
            
            if S(3)==1
                eltype = 'disk';
            else
                eltype = 'sphere';
            end
            
            if nargin<2
                board = 1; 
            end
            
            se1 = strel(eltype,board);
            outer = imdilate(bw, se1);
            
% %             se0 = strel(eltype,board-1);
% %             inner = imdilate(bw, se0);
%             se0 = strel(eltype,1);
%             inner = imerode(outer, se0);
%  
%             shelldata = outer&~inner;  
             if board==1
             shelldata = outer&~bw; 
             else
                se0 = strel(eltype,1);
                inner = imerode(outer, se0);
                shelldata = outer&~inner;  
             end
        end
        
         function [map, M] = CreateROIProbMap(roimasks, oarnames)
            map      = VolHeaderImage(roimasks{1}); map.data(:)=0;
            map.setFloat();
            if ischar(oarnames)
                oarnames = strsplit(oarnames, '|');
            end
            M = 0; 
            for k=1:numel(roimasks)
                roimask  = roimasks{k};
                if ischar(roimask)
                    roimask = ROIMaskImage(roimask);
                end

                roinames = cellfun(@(x)(x.ROIName), roimask.rois, 'uniformoutput', false);  
                [~, loc] =  ismember(oarnames, roinames);
                loc(loc==0)=[];
                if numel(loc)~=numel(oarnames)
                    continue; 
                end
                M = M+1;
                submask  = roimask.GetSubMaskImage(oarnames); 
                map.data = map.data+double(submask.data>0);
            end
            map.data = map.data/M; 
         end
         
         function zContours = Mask2ImageContours(mask, dir1, smoothwin, zstride, varargin)
            %options = OptionsMap(varargin{:});
            options = OptionsMap(varargin{:}); 
            if ~exist('smoothwin', 'var') || isempty(smoothwin)
                smoothwin = 0; 
            end
            if ~exist('zstride', 'var') || isempty(zstride)
                zstride = 1; 
            end
            
            smoothwin = round(smoothwin);     
            dimorder=[];
            if ~strcmpi(dir1,'z') 
                dirs='yxz';
                [~,dirNum]=ismember(dir1,dirs);
                dimorder  =circshift([1 2 3], 3-dirNum); 
                mask      =double(permute(mask,dimorder));
            end
            
            z_dim = size(mask, 3);
            sliceContours = cell(z_dim, 1);
            x = 1:size(mask, 2); y=1:size(mask, 1); z=1:size(mask, 3); 

            % polygon reduction with distance = 0.05 cm (half mm)
            polythreshold = 0.5;
            minpoints = options.getoptioni_numeric('ContourMinNumPoints', 3); 
            ContourSmoothFactor = options.getoptioni_numeric('ContourSmoothFactor', 0); 
            for kSlice = 1:z_dim
                if mod(kSlice-1, zstride)~=0
                    continue; 
                end
                if ContourSmoothFactor<=0
                    contours2d = contourcs(x, y, mask(:,:,kSlice), [0.5 0.5]);
%                 if isempty(contours2d)
%                     continue; 
%                 end
                    contours2d = contours2d(arrayfun(@(c)c.length>=minpoints, contours2d));
                else
                    contours2d = contourcs_smooth_periodic(mask(:,:,kSlice), 0.5, ContourSmoothFactor, 10, minpoints);
                end

                if isempty(contours2d)
                    continue; 
                end
                polys2d = arrayfun(@(s)polysimp([s.x(:) s.y(:)], polythreshold), contours2d, 'UniformOutput', false);

                if smoothwin>0 &&ContourSmoothFactor<=0
                    %polys2d = cellfun(@(x)(movmean(x, smoothwin, 1)), polys2d, 'UniformOutput', false);
                    polys2d = cellfun(@(x)(ROIMaskImage.SmoothContour(x, smoothwin)), polys2d, 'UniformOutput', false);
                end

                polys3d = cellfun(@(p)horzcat(p(:,1), p(:,2), z(kSlice) * ones(size(p,1),1)), polys2d, 'UniformOutput', false);
                sliceContours{kSlice} = polys3d(:);
            end
            zContours = vertcat(sliceContours{:});
            
            if isempty(zContours)
                return
            end
            
            % remove "contours" with less than min number of  points
            % (default is 3)
           
            zContours = zContours(cellfun(@(c)size(c,1)>=3, zContours));
            if ~isempty(dimorder)
                zContours = cellfun(@(x)(x(:, dimorder)), zContours, 'uniformoutput', false); 
            end
         end
        
         function c = SmoothContour(c, win)
            N = size(c, 1);
            ends = mod(0:win-1, N)   + 1;
            starts = mod(N-win+1:N, N) + 1;
            c1 = [c(starts, :); c; c(ends, :)];  
            c = movmean(c1, win, 1);
            K = size(c, 1)-N; K1 = round(K/2); 
            c(1:K1, :)=[]; c((N+1):end, :)=[];
            c = [c; c(1, :)];
         end
            
         function img = SafeLoad(fname, pausetime)
            try 
                img = ROIMaskImage(fname);
            catch
                if ~exist('pausetime', 'var')
                    pausetime=10; 
                end
                pause(pausetime); 
                img = ROIMaskImage(fname);
            end
         end
        
         function [rois1, I] = GetMatchROIs(rois, matchstr)
            if isstruct(rois)
                rois = arrayfun(@(x)(x), rois, 'uniformoutput', false);  
            end
            
            fieldname = StructBase.getfieldx_default(matchstr, 'ROIMatchField'); 

            if isempty(fieldname)
                fieldname = StructBase.getfieldx_default(matchstr, 'FieldName', 'ROIName'); 
            end
            
            matchnames  = cellfun(@(x)(x.(fieldname)), rois, 'uniformoutput', false);  
            [names1, I] = ROIMaskImage.GetMatchROINames(matchnames, matchstr);
            rois1 = rois(I);
         end
         
         function [names, I] = GetMatchROINames(roinames, str)
            names =[]; I=[];
            if ischar(roinames)
                roinames = strsplit(roinames, '|'); 
            end
            
            if ischar(str)
                I = ismember(roinames, str);
                I = find(I);
                if ~isempty(I)
                    names = roinames(I);
                end
                return;
            end
            
%             fieldname = StructBase.getfieldx_default(str, 'FieldName', 'ROIName'); 
            fieldvalue= StructBase.getfieldx(str, 'ROIMatchStr');
            if isempty(fieldvalue)
                fieldvalue= StructBase.getfieldx(str, 'FieldValue'); 
            end
            
            matchmode = StructBase.getfieldx_default(str, 'ROIMatchMode');
            if isempty(matchmode)
                matchmode = StructBase.getfieldx_default(str, 'MatchMode', 'regexp');
            end
            
            %roinames  = self.ROINames;
            
            switch lower(matchmode)
                case {'regexp'}
                    I=cellfun(@(x)(~isempty(regexp(x, fieldvalue, 'match', 'once'))), roinames); 
%                     names = roinames(I); 
                case {'regexpi'}
                    I=cellfun(@(x)(~isempty(regexpi(x, fieldvalue, 'match', 'once'))), roinames); 
%                     names = roinames(I); 
                case {'strcmp'}
                    I=cellfun(@(x)(strcmp(x, fieldvalue)), roinames); 
%                     names = roinames(I); 
                case {'strcmpi'}
                    I=cellfun(@(x)(strcmpi(x, fieldvalue)), roinames); 
%                     names = roinames(I); 
                case {'ismember'}
                    I=cellfun(@(x)(ismember(x, fieldvalue)), roinames); 
%                     names = roinames(I); 
            end
            I = find(I); names = roinames(I);
         end


         function val = IntensityStat(propnames, imgdata, imgtype)
            N = numel(propnames);
            val = NaN(1, N);
            for n=1:N
                statname = propnames{n};
                res = NaN; 
                switch lower(statname)
                    case {[imgtype '_mean']}
                        res = mean(imgdata);
                    case {[imgtype '_std']}
                        res = std(imgdata);
                    case {[imgtype '_min']}
                        res = min(imgdata); 
                    case {[imgtype '_max']}
                        res = max(imgdata);  
                    case {[imgtype '_median'],[imgtype '_prctile50'], [imgtype '50']}
                        res = prctile(imgdata, 50); 
                    case {[imgtype '_prctile5'], [imgtype '5']}
                        res = prctile(imgdata, 5);
                    case {[imgtype '_prctile95'], [imgtype '95']}
                        res = prctile(imgdata, 95);
                    case {[imgtype '_prctile10'], [imgtype '10']}
                        res = prctile(imgdata, 10);
                    case {[imgtype '_prctile90'], [imgtype '90']}
                        res = prctile(imgdata, 90);
                end  
                val(1, n)=res; 
            end
         end
        
        function newnames = RegexprepNames(oldnames, expstr, replacestr)
            newnames = cellfun(@(x)(regexprep(x, expstr, replacestr)), oldnames, 'uniformoutput', false); 
        end
        
        function names = DecorateNames(names, ROINameDecoration)
            Prefix = StructBase.getfieldx_default(ROINameDecoration, 'Prefix', ''); 
            if ~isempty(Prefix) && ischar(Prefix)
                expstr='^(\S)'; replacestr = [Prefix '$1']; 
                names = ROIMaskImage.RegexprepNames(names, expstr, replacestr);
            end
            Postfix= StructBase.getfieldx_default(ROINameDecoration, 'Postfix', ''); 
            if ~isempty(Postfix) && ischar(Postfix)
                expstr='(\S)$'; replacestr = ['$1' Postfix]; 
                names = ROIMaskImage.RegexprepNames(names, expstr, replacestr);
            end
            
            expstr     = StructBase.getfieldx_default(ROINameDecoration, 'MatchPattern', '');  
            replacestr = StructBase.getfieldx_default(ROINameDecoration, 'Replacement', '');
            if ~isempty(expstr)
                names = ROIMaskImage.RegexprepNames(names, expstr, replacestr);
            end
        end
        
        function rawT = CollectMetaInfo(roimasks, propnames, roinames,varargin)
            options      = OptionsMap(varargin{:});
            
            if ~iscell(roimasks)
                roimasks = arrayfun(@(x)(x), roimasks, 'uniformoutput', false); 
            end
            for k=1:numel(roimasks)
                fname = roimasks{k}; 
                if ischar(fname)
                    if isempty(regexpi(fname, '.json$'))
                        fname = [fname '.json']; 
                    end
                    roimasks{k} = ROIMaskImage; roimasks{k}.readJsonHeader(fname);
                end
            end
            if ~exist('roinames', 'var')||isempty(roinames)
                roinames ='';
                for k=1:numel(roimasks)
                    roinames1=roimasks{k}.ROINames();
                    roinames = cat(1, roinames, roinames1(:)); 
                end
            end
            roinames = unique(roinames, 'stable');
            nanT=ROIMaskImage.DefaultROIInfoTable(propnames, roinames);
            infoT = xls.RecordKeepTable({'table.uniquekey', 'ROIName'});
            infoT.SetRawTable(nanT); 
            for k=1:numel(roimasks)
                roimask=roimasks{k};
                roinames1 = intersect(roimask.ROINames, roinames); 
                T1      = roimask.MetaStatTable(propnames, roinames1);
                infoT.UpdateFromTable(T1); 
            end
            try
            volthreshold = options.getoptioni('VolumeThreshold', 0);
            rawT =infoT.GetRawTable;
            if ~isempty(volthreshold)
                ROIVolume=rawT.ROIVolume;
                I = ROIVolume<=volthreshold;
%                 ROIVolume(I)=NaN;
%                 rawT.ROIVolume=ROIVolume;
                T1 = nanT(I, :); 
                infoT.UpdateFromTable(T1); 
            end
            rawT =infoT.GetRawTable;
            catch
            end
        end
        
        function T = DefaultROIInfoTable(propnames, roinames)
            if ischar(roinames)
                roinames = strsplit(roinames, '|');
            end
            
            if ischar(propnames)
                propnames = strsplit(propnames, '|');
            end
            T=table; 
            T.ROIName = roinames(:);
            N = numel(roinames); 
            for k=1:numel(propnames)
                propname = propnames{k};
                val = ROIMaskImage.DefaultROIInfoValue(propname);
                if isempty(val)
                    T.(propname) = cell(N, 1); 
                elseif isnumeric(val)||islogical(val)
                    T.(propname) = repmat(val, [N, 1]);
                end
            end
        end
        
        function val = DefaultROIInfoValue(propname)
            switch lower(propname)
                case {'roivolume'}
                    val = NaN;
                case {'roicenter', 'bbsize', 'bbcenter'}
                    val = [NaN NaN NaN];
                case {'boundingbox'}
                    val = [NaN NaN NaN NaN NaN NaN];
                otherwise
                    val = [];
            end
        end

        function res = CreateTemplateROIMask(vh, names, outputmasktype)
            res  = ROIMaskImage(vh); 
            res.imageType=outputmasktype; 
            res.setShort(); 
            res.ZeroData();

            if ~isempty(names)
                if ischar(names)
                    names =strsplit(names, '|');
                end
                K = numel(names);
                for k=1:K
                    rois0{k}=struct('ROIIndex', k,...
                        'ROIName', names{k},...
                        'ROIColor', [],...
                        'RTROIInterpretedType',    '',...
                        'ROIPhysicalProperty',      '', ...
                        'ROIPhysicalPropertyValue', []); 
                end
                res.rois = rois0; 
                res.SetDistinguishableColors;
            end
        end

        function [dstmask] = CombineROIMasks(srcroimasks, dstnames, srcnames0,  outputmasktype)
            for k=1:numel(srcroimasks)
                if ischar(srcroimasks{k})
                    srcroimasks{k} = ROIMaskImage(srcroimasks{k});
                end
            end

            dstmask = ROIMaskImage.CreateTemplateROIMask(VolHeader(srcroimasks{1}), dstnames, 'roimask');
            for k=1:numel(srcroimasks)
                srcroimask = srcroimasks{k};
                srcnames=srcnames0{k};
                dstmask.AddRoiMaskImage(srcroimask, {'selectedroinames',srcnames});
            end

            if strcmpi(outputmasktype, 'labelmask')
                dstmask = dstmask.GetSubMaskImage(dstnames, {'outputmasktype', 'labelmask'});
            end
        end
        
        %for labelmask image
        
        function [PALengths, orientations] = PrincipalAxisLength(BW, pixdims)
            % Get the principal axis lengths and orientations in voxel units
            if ~exist('pixdims', 'var')||isempty(pixdims)
                pixdims = [1 1 1];
            end
            stats = regionprops3(BW, 'PrincipalAxisLength', 'Orientation');
            principalAxisLengths_voxels = stats.PrincipalAxisLength;
            orientations = stats.Orientation;

            %% the following code is buggy, use a simple approximation

            
            % Convert the principal axis lengths to mm
%             PALengths = zeros(size(principalAxisLengths_voxels));          
%             % Scaling matrix (3x3 diagonal)
%             S = diag( pixdims);
%             
%             for i = 1:size(principalAxisLengths_voxels, 1)
%                 % Get the principal axis lengths in voxels
%                 lengths_voxels = principalAxisLengths_voxels(i, :);
% 
%                 % Get the orientation matrix (3x3) from the 1x3 orientation vector
%                 % The 1x3 orientation vector is in degrees
%                 orientation_vector = deg2rad(orientations(i, :));
% 
%                 % Construct the rotation matrix from the orientation vector
%                 R = eul2rotm(orientation_vector);
% 
%                 % Apply the scaling and rotation
%                 lengths_mm = R * (S * lengths_voxels(:));
% 
%                 % Assign the converted lengths to the output
%                 PALengths(i, :) = lengths_mm;
%             end
           pixsize = mean(pixdims); 
           PALengths = principalAxisLengths_voxels*pixsize; 
        end
        
      
        function [stats] = regionprops2D(BW3, propnames, pixelResolution, direction)
            if ~exist('pixelResolution', 'var') 
                pixelResolution=[1 1 1]; 
            end
            if ~exist('direction', 'var') 
                direction='XY'; 
            end
            switch upper(direction)
                case {'Z', 'XY'}
                    stats = ROIMaskImage.regionprops_slice(BW3,  propnames, pixelResolution);
                case {'X', 'YZ'}
                    stats = ROIMaskImage.regionprops_slice(permute(BW3, [2 3 1]),propnames, pixelResolution([2 3 1]));
                case {'Y', 'ZX'}
                    stats = ROIMaskImage.regionprops_slice(permute(BW3, [3 1 2]),propnames, pixelResolution([3 1 2]));
            end
        end

        function [rawstats] = regionprops_slice(BW3,  propnames, pixelResolution)
            % Evaluate slice-wise major axis lengths in physical units
            %
            % BW3             : 3D binary volume (X x Y x Z)
            % pixelResolution : [dy dx] pixel spacing in Y and X directions (e.g. mm)
            %
            % majorLengths    : vector (Z x 1), max major axis length per slice in physical units
            if ~exist('pixelResolution', 'var')
               pixelResolution=[1 1];
            end

            sz = size(BW3);
            %majorLengths = NaN(sz(3),1);
            rawstats = cell(sz(3), 1);
            minres=min(pixelResolution(1:2)); 
            for k = 1:sz(3)
                BW2 = BW3(:,:,k);
                if any(BW2(:))
                    if pixelResolution(1)~=pixelResolution(2)
                        % normalize XY resolution to isotropic grid
                        scaleY = pixelResolution(1) / minres;
                        scaleX = pixelResolution(2) / minres;
                        BW2 = imresize(BW2, [round(sz(1)*scaleY), round(sz(2)*scaleX)], 'nearest');
                    end
                    CC = struct('Connectivity', 8, 'ImageSize', size(BW2), ...
                        'NumObjects', 1); 
                    CC.PixelIdxList{1} = find(BW2);
                    rawstats{k} = regionprops(CC, propnames);
                end
            end
            % I = cellfun(@(x)(~isempty(x)), rawstats); 
            % stats = cellfun(@(x)x, rawstats(I));
            % for k=1:numel(propnames)
            %     name = propnames{k};
            %     maxstat.(name) = max([stats(:).(name)], [], 1);
            % end
            %maxlen = max(majorLengths);
        end

        function varnames = regionprops3_varnames()
            STATS = regionprops3(0,'all');
            varnames = STATS.Properties.VariableNames;
        end
        function varnames = regionprops_varnames()
            STATS = regionprops('table', 0,'all');
            varnames = STATS.Properties.VariableNames;
        end
    end
end


        
