classdef ROIPerimImage <ROIMaskImage   
    properties
        
    end
    
    methods
        function obj = ROIPerimImage(varargin)
           obj@ROIMaskImage(varargin{:});
           if nargin<1
               obj.imageType = 'perimmask';
           end
        end
        
        function AddRoiMaskImage(obj, roimask, varargin)
           if strcmpi(roimask.imageType, 'perimmask')
               obj.AddRoiMaskImage@ROIMaskImage(roimask, varargin{:});
               return;
           end

           options = OptionsMap(varargin{:}); 
           perimmode = options.getoptioni('perim.method', 'erode2d'); 
           conn = options.getoptioni_numeric('perim.conn', 4); 
           if isempty(obj.data)
               obj.copyStruct(VolHeader(roimask));
               obj.imageType='perimmask';
               obj.ZeroData();
               obj.setShort();
               obj.rois = [];
               roinames0={};
           else
               roinames0 = obj.ROINames;
           end
  
           if obj.z_dim>1
                se = strel('sphere', 1);
           else
                se = strel('disk', 1);
                %se = true(2);
           end

           rois = roimask.rois; 
           roinames =roimask.ROINames; 
           options = OptionsMap(varargin{:});
            if ~options.isoptioni('keeproiindex')
                options.setOption('keeproiindex', 0);
            end
            useorigindex = options.getoptioni_numeric('keeproiindex'); 

            selectedroinames = options.getoptioni('selectedroinames', '');
            if ~isempty(selectedroinames)&&ischar(selectedroinames)
                selectedroinames=strsplit(selectedroinames, '|');
            end
            
            if ~isempty(selectedroinames)
                [roinames, IA, IB] = intersect(selectedroinames, roinames, 'stable');
                rois = rois(IB);
            end
           
           for k=1:numel(rois)
               roi  = rois{k};
               name = roi.ROIName; 
               
               
               mask = roimask.getROIMask(name);
               switch lower(perimmode)
                   case 'bwperim'
                        label=bwperim(mask, conn); 
                   case 'erode2d'
                        se = strel('disk', 1);
                        label = mask-imerode(mask, se);
                   case 'erode3d'
                        se = strel('sphere', 1);
                        label = mask-imerode(mask, se);
               end
               
               if ismember(name, roinames0)
                    obj.ReplaceRoiMask(name, label);
               else
                   if ~useorigindex
                       numrois0 = numel(obj.rois); 
                       roi.ROIIndex = numrois0+1; 
                   end

                   obj.AddNewROI(label,  name, {'roi', roi});
               end
           end
        end
    end
end

