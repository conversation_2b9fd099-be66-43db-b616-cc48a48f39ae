# Enhanced ROIStatTable_regionprops2D with Clinical Naming Convention

## Overview

The enhanced `ROIStatTable_regionprops2D` method now supports **clinical anatomical naming conventions** (AX, SAG, COR) in addition to the original coordinate-based naming (XY, YZ, ZX). This makes the function more intuitive for clinical users who think in terms of anatomical planes.

## Key Enhancements

### 1. Clinical Naming Support
- **AX** (Axial) - Transverse/horizontal slices
- **SAG** (Sagittal) - Left-right slices  
- **COR** (Coronal) - Front-back slices

### 2. Automatic Orientation Detection
Uses `VolHeader.getSliceOrientation()` to determine the actual anatomical orientation of coordinate planes based on DICOM `ImageOrientationPatient` tag.

### 3. Dual Naming Support
Supports both naming conventions simultaneously:
- Traditional: `MajorAxisLength_XY`, `MajorAxisLength_YZ`, `MajorAxisLength_ZX`
- Clinical: `MajorAxisLength_AX`, `MajorAxisLength_SAG`, `MajorAxisLength_COR`

## Usage Examples

### Example 1: Clinical Naming for Tumor Analysis
```matlab
% Define properties using clinical anatomical terms
propnames = {
    'MajorAxisLength_AX',    % Maximum extent in axial (transverse) plane
    'MajorAxisLength_SAG',   % Maximum extent in sagittal plane
    'MajorAxisLength_COR',   % Maximum extent in coronal plane
    'Area_AX',               % Cross-sectional area in axial plane
    'Eccentricity_AX'        % Shape eccentricity in axial plane
};

% Analyze tumor ROIs
roinames = {'GTV', 'CTV', 'PTV'};
[T, props_2D] = roimask.ROIStatTable_regionprops2D(propnames, roinames);

% Results will have clinically meaningful column names
disp(T.Properties.VariableNames);
% Output: {'ROINames', 'MajorAxisLength_AX', 'MajorAxisLength_SAG', 'MajorAxisLength_COR', 'Area_AX', 'Eccentricity_AX'}
```

### Example 2: Mixed Naming Convention
```matlab
% Mix clinical and coordinate naming
propnames = {
    'MajorAxisLength_AX',    % Clinical: axial extent
    'MinorAxisLength_XY',    % Coordinate: same as axial for standard orientation
    'Area_SAG',              % Clinical: sagittal cross-section area
    'Perimeter_ZX'           % Coordinate: coronal perimeter
};

[T, props_2D] = roimask.ROIStatTable_regionprops2D(propnames, {'Liver', 'Kidney'});
```

### Example 3: Comprehensive Organ Analysis
```matlab
% Comprehensive analysis of organ geometry
propnames = {
    'Area_AX', 'Area_SAG', 'Area_COR',                    % Cross-sectional areas
    'MajorAxisLength_AX', 'MajorAxisLength_SAG', 'MajorAxisLength_COR',  % Maximum extents
    'MinorAxisLength_AX', 'MinorAxisLength_SAG', 'MinorAxisLength_COR',  % Minimum extents
    'Eccentricity_AX', 'Eccentricity_SAG', 'Eccentricity_COR',          % Shape measures
    'Solidity_AX', 'Solidity_SAG', 'Solidity_COR'         % Convexity measures
};

% Analyze multiple organs
roinames = {'Heart', 'Liver', 'Lung_L', 'Lung_R', 'Kidney_L', 'Kidney_R'};
[T, props_2D] = roimask.ROIStatTable_regionprops2D(propnames, roinames);

% Custom aggregation (mean instead of maximum across slices)
[T_mean, props_2D] = roimask.ROIStatTable_regionprops2D(propnames, roinames, ...
    'ArrayStatFun', @mean);
```

## Clinical Applications

### 1. Radiation Therapy Planning
```matlab
% Analyze target volumes for treatment planning
target_props = {
    'MajorAxisLength_AX',    % Superior-inferior extent (for beam angles)
    'MajorAxisLength_SAG',   % Anterior-posterior extent (for field size)
    'MajorAxisLength_COR',   % Left-right extent (for field size)
    'Area_AX',               % Axial cross-section (for dose calculation)
    'Eccentricity_AX'        % Shape irregularity (for margin assessment)
};

targets = {'GTV_Primary', 'CTV_Primary', 'PTV_Primary'};
[target_stats, ~] = roimask.ROIStatTable_regionprops2D(target_props, targets);

% Calculate treatment margins
margin_SI = target_stats.MajorAxisLength_AX * 0.1;  % 10% margin superior-inferior
margin_AP = target_stats.MajorAxisLength_SAG * 0.1; % 10% margin anterior-posterior
margin_LR = target_stats.MajorAxisLength_COR * 0.1; % 10% margin left-right
```

### 2. Organ Shape Analysis
```matlab
% Analyze organ shape characteristics
organ_props = {
    'Area_AX', 'Area_SAG', 'Area_COR',           % Cross-sectional areas
    'Solidity_AX', 'Solidity_SAG', 'Solidity_COR' % Shape regularity
};

organs = {'Liver', 'Spleen', 'Pancreas'};
[organ_stats, ~] = roimask.ROIStatTable_regionprops2D(organ_props, organs);

% Calculate shape indices
axial_aspect_ratio = organ_stats.MajorAxisLength_AX ./ organ_stats.MinorAxisLength_AX;
volume_anisotropy = organ_stats.Area_AX ./ organ_stats.Area_SAG;
```

### 3. Longitudinal Studies
```matlab
% Compare organ changes over time
time_props = {
    'Area_AX', 'Area_SAG', 'Area_COR',
    'MajorAxisLength_AX', 'MajorAxisLength_SAG', 'MajorAxisLength_COR'
};

% Baseline scan
[baseline_stats, ~] = roimask_baseline.ROIStatTable_regionprops2D(time_props, {'Tumor'});

% Follow-up scan  
[followup_stats, ~] = roimask_followup.ROIStatTable_regionprops2D(time_props, {'Tumor'});

% Calculate changes
area_change_axial = (followup_stats.Area_AX - baseline_stats.Area_AX) ./ baseline_stats.Area_AX * 100;
length_change_SI = (followup_stats.MajorAxisLength_AX - baseline_stats.MajorAxisLength_AX) ./ baseline_stats.MajorAxisLength_AX * 100;
```

## Technical Implementation Details

### Orientation Mapping
The method automatically determines the mapping between coordinate planes and anatomical orientations:

```matlab
% Example mappings for different scan orientations:

% Standard Axial CT (most common)
% XY plane → AX (axial/transverse)
% YZ plane → SAG (sagittal) 
% ZX plane → COR (coronal)

% Sagittal MRI
% XY plane → SAG (sagittal)
% YZ plane → COR (coronal)
% ZX plane → AX (axial)

% Coronal MRI  
% XY plane → COR (coronal)
% YZ plane → AX (axial)
% ZX plane → SAG (sagittal)
```

### Error Handling
- Falls back to default mapping if orientation cannot be determined
- Supports both clinical and coordinate naming simultaneously
- Handles empty property lists gracefully

## Benefits

1. **Clinical Intuition**: Uses familiar anatomical terminology
2. **Backward Compatibility**: Still supports original XY/YZ/ZX naming
3. **Automatic Adaptation**: Works correctly regardless of scan orientation
4. **Comprehensive Analysis**: Enables multi-planar shape characterization
5. **Treatment Planning Integration**: Direct support for clinical workflows

This enhancement makes the ROI analysis more accessible to clinical users while maintaining full backward compatibility with existing code.
