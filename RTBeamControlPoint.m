classdef RTBeamControlPoint <HandleStructBase
    %RTBeamControlPoint
    %author: Weiguo Lu
    
    properties
%         ControlPointIndex       %: 0
        
        NominalBeamEnergy       %: 18
        DoseRateSet             %: 600
%         WedgePositionSequence   %: [1x1 struct]
%         BeamLimitingDevicePositionSequence  %: [1x1 struct]
        GantryAngle             %: 150
        GantryRotationDirection %= 'NONE'
        BeamLimitingDeviceAngle %: 0
        BeamLimitingDeviceRotationDirection %= 'NONE'
        PatientSupportAngle     %: 0
        PatientSupportRotationDirection     %= 'NONE'
        TableTopEccentricAngle %: 0
        TableTopEccentricRotationDirection %= 'NONE'
        TableTopVerticalPosition %: -82
        TableTopLongitudinalPosition %: 1150
        TableTopLateralPosition     %: -29
        IsocenterPosition       %: [6.6000 36.6000 79]
        SourceToSurfaceDistance %: 937.4000
%         ReferencedDoseReferenceSequence %: [1x1 struct]
        CumulativeMetersetWeight %: 0
        
        XJawPositions; 
        YJawPositions; 
        MLCXPositions; 
        MLCYPositions;
    end
    
    methods
        function obj = RTBeamControlPoint(varargin)
            obj = obj@HandleStructBase; 
            if nargin>=1
                if ischar(varargin{1})
                    obj.readTextFile(varargin{1}); 
                elseif isa(varargin{1}, 'RTBeamControlPoint') || isstruct(varargin{1})
                    obj.copy(varargin{1}); 
                end
            end 
        end
        
        function refresh(obj)
            fn = fieldnames(RTBeamControlPoint); 
            for k=1:numel(fn)
                obj.(fn{k}) = 0; 
            end
            obj.IsocenterPosition =zeros(1, 3); 
            obj.XJawPositions=zeros(1, 2); 
            obj.YJawPositions=zeros(1, 2); 
%             obj.MLCXPositions=zeros(1, 120); 
        end
        
        function fromDicomControlPointItem(obj, dicomCP)
            obj.copy(dicomCP); 
            sequence = StructBase.getfieldx(dicomCP, 'BeamLimitingDevicePositionSequence'); 
            if isempty(sequence)
                return; 
            end
            %convFact = LinearMeasureConversionFactor(obj); 
            convFact = 1; 
            fns=fieldnames(sequence);
            %jaw_xdata=[]; jaw_ydata=[]; mlc_xdata=[];
            for j=1:length(fns)
                fn=fns{j};
                subItem= sequence.(fn);
                devType=subItem.RTBeamLimitingDeviceType;
                devType = upper(devType); 
                devType = devType(devType>='A' & devType<='Z');
                if strcmpi(devType,'ASYMX') || strcmpi(devType,'X')
                    obj.XJawPositions=subItem.LeafJawPositions*convFact;
                elseif strcmpi(devType,'ASYMY') || strcmpi(devType,'Y')
                    obj.YJawPositions=subItem.LeafJawPositions*convFact;
                elseif strcmpi(devType,'MLCX')
                    obj.MLCXPositions=subItem.LeafJawPositions*convFact;
                    % mlc_xdata splits into 2 sides: 1:60 is for leaves
                    % on the left side, 61:120 is for leaves on the
                    % right side.
%                     mlc_xdataL=mlc_xdata(1:n);
%                     mlc_xdataR=mlc_xdata(n+1:2*n);
                elseif strcmpi(devType,'MLCY')
                    obj.MLCYPositions=subItem.LeafJawPositions*convFact;
                end
            end
        end
    end
    
    methods (Static)
        function fn = LinearMeasureFieldNames
            fn = {'TableTopVerticalPosition', 'TableTopLongitudinalPosition', 'TableTopLateralPosition', 'IsocenterPosition', ...
                  'SourceToSurfaceDistance',  'XJawPositions', 'YJawPositions', 'MLCXPositions'}; 
        end
        
        function fn = DirectionFieldNames
            fn = {'GantryRotationDirection', 'BeamLimitingDeviceRotationDirection', 'TableTopEccentricRotationDirection', 'PatientSupportRotationDirection'};
        end
        
        function result = convertGantryAngles(gantryAngles)
            N = numel(gantryAngles);
            if N==0
                result = []; 
            elseif all(gantryAngles==gantryAngles(1))
                result = ['STATIC ' num2str(gantryAngles(1))]; 
            else   
                d = diff(gantryAngles);
                deltaAngle = mode(d); 
                str = [num2str(gantryAngles(1)) ':'  num2str(deltaAngle, '%5.1f') ':'  num2str(gantryAngles(end))]; 
                if deltaAngle>0
                    result = ['CW '  str];
                elseif deltaAngle<0
                    result = ['CCW ' str];
                end
            end  
        end

    end
end

