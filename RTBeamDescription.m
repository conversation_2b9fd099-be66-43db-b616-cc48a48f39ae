classdef RTBeamDescription <HandleStructBase
    %UNTITLED4 Summary of this class goes here
    %   Detailed explanation goes here
    
    properties
        TreatmentMachineName %= BNO23iX
        PrimaryDosimeterUnit %= MU
        SourceAxisDistance   %= 1000

        BeamNumber %= 1
        BeamName   %= 01 RT LAT
        BeamDescription %= 01 RT LAT
        BeamType    %= STATIC
        RadiationType %= PHOTON
        TreatmentDeliveryType %= TREATMENT
        NumberOfWedges %= 0
        NumberOfCompensators %= 0
        NumberOfBoli %= 0
        NumberOfBlocks %= 0
        
        WedgeSequence
        BlockSequence
        BolusSequence
        CompensatorSequence
        
        FinalCumulativeMetersetWeight %= 1
        NumberOfControlPoints %= 2
        
        BeamDose     %= 1.250000e+000
        BeamMeterset %= 1.405109e+002
        SetupTechnique; 
    end
    methods
        function obj = RTBeamDescription(varargin)
            if numel(varargin)>=1 
                if ischar(varargin{1})
                    obj.readTextFile(varargin{1}); 
                elseif isstruct(varargin{1}) || isa(varargin{1}, 'RTBeamDescription')
                    obj.copy(varargin{1}); 
                end
            end
        end
    end
    
    methods (Static)
        function fn = LinearMeasureFieldNames
            fn ={'SourceAxisDistance'}; 
        end
    end
    
end

