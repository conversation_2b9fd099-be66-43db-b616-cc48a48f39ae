classdef SimpleGeometry <handle
    %UNTITLED2 Summary of this class goes here
    %   Detailed explanation goes here
    
    properties (Constant)
        EPS = 1e-8;
    end
    
    methods (Static)
        function p  = triangle_center(p0, p1, p2)
            p=(p0+p1+p2)/3.0; 
        end
        
        function dir= triangle_direction(p0, p1, p2)          
            x0 = p1-p0; 
            x1 = p2-p0; 
            dir = cross(x0, x1);
        end 
    end
end

