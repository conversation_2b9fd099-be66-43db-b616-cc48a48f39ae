classdef SimpleLine <handle
   properties (Constant)
        
   end
   
   properties
        direction; %Point<T, 3>
        origin;    %Point<T, 3> 
   end
   
   methods
        function obj = SimpleLine(direction, p)
            obj.direction = direction/norm(direction); 
            obj.origin = p-dot(p,obj.direction)*obj.direction; 
        end

        function  from_two_points(obj, p0,  p1)
            obj.direction = (p1-p0)/norm(p1-p0);
            t = dot(p0, obj.direction);
            obj.origin = p0 - obj.direction*t;
        end

        function p = getPoint(obj,  t) 
            p = obj.origin+obj.direction*t;
        end

        function p = point_projection(obj, p0)
            t = dot(p0-obj.origin, obj.direction);
            p = getPoint(obj,  t);
        end
        
        function d = distance2Point(obj, p0)
            d=norm(p0-obj.point_projection(p0)); 
        end

        function flag= isParallel(obj, line,  eps)  
            if ~exist('eps', 'var')
                eps = SimpleGeometry.EPS;
            end
            flag = norm(obj.direction-line.direction)<eps;
        end
        
        function flag = isEqual(obj, line) 
            flag = norm(obj.direction-line.direction)<SimpleGeometry.EPS && norm(obj.origin-line.origin)<SimpleGeometry.EPS;
        end

        function val =  directionCosine(obj, line) 
            val =  dot(obj.direction, line.direction);
        end
    end
    
end
