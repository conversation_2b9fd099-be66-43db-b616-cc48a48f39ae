classdef SimplePlane <handle

    properties
         direction;        %Point 
         distance2Origin;  %scalar, could be positive or negative	
    end

    methods 
        function obj = SimplePlane(varargin)
            if nargin ==1
                obj.direction = varargin{1}/norm(varargin{1}); 
                obj.distance2Origin=0; 
            elseif nargin ==2
                obj.direction=varargin{1}/norm(varargin{1}); 
                if numel(varargin{2})==1
                    obj.distance2Origin=varargin{2};
                else
                    obj.from_direction_and_point(varargin{1}, varargin{2})
                end
            elseif nargin==3
                obj.from_three_points(varargin{1}, varargin{2}, varargin{3}); 
            end
        end

        function from_direction_and_point(obj, direction, p)
            obj.direction= direction/norm(direction);
            obj.distance2Origin= dot(p, obj.direction);
        end

        function from_three_points(obj,  p0, p1, p2)
            dir  = SimpleGeometry.triangle_direction(p0, p1, p2);
            center	   = SimpleGeometry.triangle_center(p0, p1, p2);
            obj.from_direction_and_point(dir, center); 
        end

        function p = getOrigin(obj)
            p = obj.direction*obj.distance2Origin;
        end

        function x = point_projection( p0) 
            t = obj.distance2Point(p0);
            x = p0 + obj.direction*t; 
        end

        function t = distance2Point(p0) 
            p = obj.getOrigin(); 
            t = dot(obj.direction(), p-p0);
        end 

        function [t, p] = lineIntersection(obj, line) 
            % let p be the intersection point
            % then p=line.origin+t*line.direction, and
            % dot(p,obj.direction)=obj.distance2Origin
            t=(obj.distance2Origin-dot(line.origin,obj.direction))/ ...
                dot(line.direction,obj.direction);
%             t= dot(obj.direction, obj.origin-line.origin) / dot(obj.direction, line.direction);  
            if nargout>1
                p=  line.getPoint(t); 
            end
        end

        function line = planeIntersection(obj, plane)
            dir  = cross(obj.direction, plane.direction);
            dir0 = cross(obj.direction, dir);
            dir1 = plane.direction;
            P0 = obj.origin; 
            t0 = dot(dir1, plane.getOrigin()-P0)/dot(dir1, dir0);
            A = P0 + dir0*t0; 
            line =  SimpleLine(dir, A); 
        end
        
        function flag = isParallel(obj, plane)
%             flag = norm(obj.direction-plane.direction)<SimpleGeometry.EPS;
            % assume .direction is a unit vector.
            flag = acos(obj.direction.*plane.direction)<SimpleGeometry.EPS;
        end
        
        function flag = isEqual(obj, plane)
            flag = norm(obj.direction-plane.direction)<SimpleGeometry.EPS && norm(obj.distance2Origin-plane.distance2Origin)<SimpleGeometry.EPS;
        end
    end
end