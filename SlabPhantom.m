classdef SlabPhantom <PhantomImage
    properties
        SlabMaterial;
        SlabThickness; 
        SlabDensity
    end
    
    methods
        function ct = SlabPhantom(varargin)
            
            
            ct = ct@PhantomImage(varargin{:}); 
            options = OptionsMap(varargin{:}); 
            
            phantomType = options.getOption('PhantomType', 'Slab_Classic'); 
            try
            slab  = ct.(phantomType);
            catch
                slab = ct.Slab_Generic(phantomType);
            end
            
            ct.SlabMaterial       = options.getOption('SlabMaterial', slab.mat); 
            ct.SlabThickness      = options.getOption('SlabThickness', slab.thick*ct.y_dim*ct.y_pixdim); 
            ct.SlabDensity        = options.getOption('SlabDensity'); 
            tt = ct.MatTable();
            
            M0=1; 
            M1=0; 
            
            A = zeros(ct.y_dim, ct.x_dim); 
            
            if isempty(ct.SlabDensity)
                ct.SlabDensity = tt{ct.SlabMaterial, 'density'};
            end

            for k=1:numel(ct.SlabMaterial)
                M = round(ct.SlabThickness(k)/ct.y_pixdim); 
                if M==0
                    continue; 
                end
                M1 = M1+M; 
                M1 = min(M1, ct.y_dim); 
                A(M0:M1, :)= ct.SlabDensity(k); 
                M0=M1; 
            end
            halfslab = options.getoptioni('halfslab');
            if halfslab
                N=size(A, 2)/2+1; 
                A(:,N:end) = 1;  
            end
            ct.setData(repmat(A, [1, 1, ct.z_dim])); 
            
            
        end
        
        function axH = compareIsoDoses(self, doses, varargin)
            for k=1:numel(doses)
                midslice = round(doses(k).z_dim/2); 
                doseData{k} = doses(k).data(:, :, midslice); 
            end
            
            plotSlab_2D(self); hold on; 
            axH = compareIsoLines(doseData, varargin{:});
        end
        
        function lH = comparePDDs(self, doses, varargin)
            for k=1:numel(doses)
                pdd(k) = ProfileData(doses(k).getProfile([0, 0, 0], 'y')); 
                pdd(k).dist = pdd(k).dist - doses(k).y_start; 
            end
             lH = compareProfiles(self, pdd, varargin{:});
        end
        
        function lH=compareProfiles(self, profs, varargin)
            lH=ProfileData.compareProfiles(profs, varargin{:}); hold on; 
            plotSlab_1D(self);
            options = OptionsMap(varargin{:}); 
            legendStr = options.getoptioni('legendstr', []);  
            if ~isempty(legendStr)
                legend(lH, legendStr); 
            end
        end
        
        function plotSlab_1D(self, yMax)
            mat = self.SlabMaterial;
            thick = self.SlabThickness;
            symbol = SlabSymbol(self);
            
            if ~exist('yMax', 'var')
                Y = get(gca, 'ylim'); 
            else                
                Y = [0 yMax];  
            end
            x = [0 cumsum(thick)]; 
            for k=1:length(mat)
                line([x(k+1) x(k+1)], Y); text((x(k)+x(k+1))/2, Y(2), symbol{k});
            end  
            xlim([0 x(end)]); xlabel('depth (cm)'); 
        end
        
        function plotSlabDensity_2D(self, varargin)
            data = reshape(self.data(:, round(self.x_dim/2), :), [self.y_dim, self.z_dim]);
            imshow(data); hold on; 
            plotSlab_2D(self, varargin{:});  
        end
        
         function plotSlab_2D(self, varargin)       
            volheader=VolHeader(self);  
            volheader.y_start = 0;   
            symbol = SlabSymbol(self);
            thick  = self.SlabThickness; 
            y = [0 cumsum(thick)]; 
            xrange = volheader.x_start + [0, volheader.x_dim*volheader.x_pixdim]; 
            
            xrange = volheader.physics2image(xrange, 'X');
            y      = volheader.physics2image(y, 'Y');
            
            X = [xrange(1) xrange(2) xrange(2) xrange(1)];
            
            for k=1:numel(symbol)
                Y = [y(k) y(k) y(k+1) y(k+1)]; 
                
                
                self.plotPolygon(X, Y, varargin{:});
                text(xrange(1), (y(k)+y(k+1))/2, symbol{k});
            end          
            
            axH = gca; 
%             xticks=-10:5:10;
%             yticks= 0:5:25;
%             set(axH, 'XTick',xticks,'XTickLabel',xticks, ...
%                      'YTick',yticks,'YTickLabel', yticks,'FontSize',12, ...
%                      'xLim', [xrange(1), xrange(2)], 'yLim', [y(1) y(end)]);
%             xlabel('distance (cm)'), ylabel('depth (cm)');
            
            LABEL={'X', 'Y'};
            for k=1:2
                [tick{k}, ticklabel{k}] = volheader.getAxesTick(LABEL{k}, 5);
            end
            set(axH, 'XTick',   tick{1}, 'XTickLabel', ticklabel{1});  
            set(axH, 'YTick',   tick{2}, 'YTickLabel', ticklabel{2});   
            xlabel('distance (cm)'), ylabel('depth (cm)'); 
            
            axis ij; axis image; 
         end
        
         function symbol = SlabSymbol(self)
            tt = self.MatTable();
            try
            symbol = tt{self.SlabMaterial, 'symbol'};
            catch
                symbol = self.SlabMaterial;
            end
         end
    end
    
    methods(Static)
        function slab  = Slab_Classic
            slab.mat   = {'adipose', 'muscle', 'bone', 'muscle', 'lung', 'muscle',  'bone', 'adipose', 'bone','muscle','adipose'};
            slab.thick = [1 1 1 1 6 1 1 1 1 1 1]/16; 
        end
        
        function slab  = Slab_Water
            slab.mat   = {'water'};
            slab.thick = 1; 
        end
        
        function slab  = Slab_Air
            slab.mat   = {'air'};
            slab.thick = 1; 
        end
        
        function slab = Slab_Water_Lung_Water
            slab.mat   = {'water', 'lung', 'water'};
            slab.thick = [1 1 1]/3; 
        end
        
        function slab = Slab_Water_Air_Water
            slab.mat   = {'water', 'air', 'water'};
            slab.thick = [1 1 1]/3; 
        end

        %e.g. name = 'water1_bone2_water3'
        function slab = Slab_Generic(name)
            a=regexp(name, '_', 'split');
%             b=regexp(a, '(?<mat>\w+)(?<thick>\d+)', 'names');
            b=regexp(a, '(?<mat>[a-zA-Z]+)(?<thick>\d+)', 'names');
             
%             matStr = PhantomImage.MatRegexStr;
%             match = ['(?<mat>' matStr '(?<thick>\d+)'];
%             b=regexp(a, match, 'names');
            
            slab.mat = cellfun(@(x)(x.mat), b, 'UniformOutput', false);
            slab.thick = cellfun(@(x)(str2double(x.thick)), b, 'UniformOutput', true);
            slab.thick = slab.thick/sum(slab.thick); 
        end
    end
end

 function [axH, cxH] = compareIsoLines(dose, V, legendStr)
    if ~iscell(dose)
        dose = {dose}; 
    end
    
    if ~exist('V', 'var') || isempty(V)      
        refdose = max(dose{1}(:)); 
        V=(.1:.1:1)*refdose; % 10% ~ 100% contour lines
    end

    
    linestyle = {'-', '--', '-.'}; 
    contourH=zeros(1,numel(dose));
    for k=1:numel(dose)
        [~,contourH(k)]=contour(dose{k}, V, linestyle{k}, 'LineWidth',1);hold on, 
    end

    axH = gca; 
     if exist('legendStr', 'var') && ~isempty(legendStr)
        legend(contourH,legendStr);
    end
    axis image; axis ij; axis square; 

    %title('isodose comparison'), 
    colorbar;
    cxH=colorbar('vert','FontSize',15);
 end

