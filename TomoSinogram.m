classdef TomoSinogram <VolHeaderImage
    %UNTITLED27 Summary of this class goes here
    %   Detailed explanation goes here
       
    properties (Constant)
        ppr = 51
    end
    properties
        signalConversionFactor = 1; 
    end
    
%     properties (Dependent = true)
%         sinoData
%         
%     end
      
    methods
        function obj=TomoSinogram(varargin)   
            obj = obj@VolHeaderImage(varargin{:}); 
        end
        
%         function val = get.sinoData(obj)
%             val= obj.data;
%         end
%         
        function numProj = totalNumberOfProjections(obj)
            numProj = size(obj.data, 1); 
        end
         
        function [fluence] = toFluenceMap(obj, jawImage)
            %UNTITLED3 Summary of this function goes here
            %   Detailed explanation goes here
            sinoHeader = obj.header; 
            sinoData = obj.data; 

            jawHeader = jawImage.header; 
            jawData = jawImage.data; 
            jawData = jawData(:); 

            yStart = sinoHeader.y_start; 

            ppr = obj.ppr; 
            yLen  = sinoHeader.y_dim * sinoHeader.y_pixdim + jawHeader.x_dim*jawHeader.x_pixdim;
            y_dim = ceil(yLen/jawHeader.x_pixdim);
            fluenceData = zeros(y_dim, sinoHeader.x_dim, ppr); 
            fluenceHeader =  readctheader; 
            fluenceHeader.x_dim = size(fluenceData, 2); 
            fluenceHeader.y_dim = size(fluenceData, 1); 
            fluenceHeader.z_dim = size(fluenceData, 3); 
            fluenceHeader.x_pixdim = sinoHeader.x_pixdim; 
            fluenceHeader.y_pixdim = jawHeader.x_pixdim; 
            fluenceHeader.z_pixdim = 1; 
            fluenceHeader.x_start = sinoHeader.x_start; 
            fluenceHeader.y_start = yStart+jawHeader.x_start; 
            fluenceHeader.z_start = 0; 
            fluenceHeader.data_type = 'float';
            
            C = ceil(jawHeader.x_dim/2);
            yScale = sinoHeader.y_pixdim/fluenceHeader.y_pixdim;

            
            pStart = mod(round(sinoHeader.y_start/sinoHeader.y_pixdim), ppr);
            numproj = size(sinoData, 1); 
            for m = 1:numproj
                p = mod(pStart + m-1, ppr)+1;
                projData = sinoData(m, :);
                ypos = (m-1)*yScale+C; 
                fluenceData(:, :, p) = projData2fluence(fluenceData(:, :, p), projData, jawData, ypos);
            end
            %fluence = permute(fluence, [2, 1, 3]); 
            fluence = VolHeaderImage(fluenceHeader, fluenceData); 
        end
        
    end 
end



function fluence = projData2fluence(fluence, projData, jawProf, zpos)
    M = size(fluence, 1);
    zi = round(zpos);
    a = zpos-zi; 
    N = (length(jawProf)-1)/2;
    I = -N:N; 
    I0 = zi+I; 
    I1 = I0+1;
    J0 = find(I0>=1 & I0<=M); 
    J1 = find(I1>=1 & I1<=M); 
    numleaf = size(fluence, 2) ;
    proj0 = (1-a)*jawProf(J0);
    proj1 = a*jawProf(J1);
    K0 = I0(J0); K1 = I1(J1); 
    for k =1:numleaf
        val = projData(k);
        fluence(K0, k) = fluence(K0, k) + proj0*val;
        fluence(K1, k) = fluence(K1, k) + proj1*val;
    end
end