classdef VectorImage <VolHeaderImage
    properties
    end
    
    methods
        function self = VectorImage(varargin)
            self = self@VolHeaderImage(varargin{:}); 
        end
        
        
        function readData(obj, fname)   
            fid = fopen(fname, 'rb', obj.machineFormat); 
            obj.data = fread(fid, obj.data_type);
            fclose(fid);
            [PATHSTR,NAME,EXT] = fileparts(fname);
            if strcmpi(NAME(end), 'X')
                exts = 'YZ';
                for k=1:numel(exts)
                    NAME(end) = exts(k); 
                    fname1 = fullfile(PATHSTR,[NAME,EXT]); 
                    fid = fopen(fname1, 'r', obj.machineFormat);
                    data1{k} = fread(fid, obj.data_type);
                    fclose(fid); 
                end
                obj.data = cat(1, obj.data, data1{1}, data1{2});
            end
            N   = obj.x_dim*obj.y_dim*obj.z_dim;
            len = numel(obj.data); 
            obj.data((len+1):(3*N))=0; 
        end
        
        
        function res = dataX(self)
            res = self.dataComponent('x'); 
        end
        
        function res = dataY(self)
            res = self.dataComponent('y'); 
        end
        
        function res = dataZ(self)
            res = self.dataComponent('z'); 
        end
        
        function res = dataComponent(self, dir)
            siz = [self.y_dim self.x_dim, self.z_dim]; 
            N   = self.x_dim*self.y_dim*self.z_dim;
            switch lower(dir)
                case 'x'
                    offset = 0; 
                case 'y'
                    offset = 1; 
                case 'z'
                    offset = 2; 
            end
            I   = 1:N; 
            res = permute(reshape(self.data(I+offset*N), siz), [2 1 3]);
        end
        
        function res = imageComponent(self, dir)
            res = VolHeaderImage(self); 
            res.setData(dataComponent(self, dir)); 
        end
        
%         function [dataX, dataY, dataZ] = getVectorData(self)
%             siz = [self.y_dim self.x_dim, self.z_dim]; 
%             N   = self.x_dim*self.y_dim*self.z_dim; 
%             I   = 1:N; 
%             dataX = permute(reshape(self.data(I), siz), [2 1 3]);
%             dataY = permute(reshape(self.data(I+N), siz), [2 1 3]);
%             dataZ = permute(reshape(self.data(I+2*N), siz), [2 1 3]);
%         end
        
        function setVectorData(self, dataX, dataY, dataZ)
            siz = [self.y_dim self.x_dim, self.z_dim]; 
            if ~exist('dataZ', 'var')|| isempty(dataZ)
                dataZ = zeros(siz); 
            end
            
            if ~exist('dataY', 'var')|| isempty(dataY)
                dataY = zeros(siz); 
            end
            
            if ~exist('dataX', 'var')|| isempty(dataX)
                dataX = zeros(siz); 
            end
            
            dataX = permute(dataX, [2, 1, 3]); 
            dataY = permute(dataY, [2, 1, 3]);
            dataZ = permute(dataZ, [2, 1, 3]);
            self.data = cat(1, dataX(:), dataY(:), dataZ(:)); 
        end
        
        function count = writeRawData(obj, imgfname)
            count = 0; 
            if ~isempty(obj.data)
                fid = fopen(imgfname, 'wb');
                count = fwrite(fid,obj.data, obj.data_type);
                fclose(fid);
            end
        end
        
        function imview(self)
            vh = VolHeaderImage(self); 
            vh.setData(100*ones(self.y_dim, self.x_dim, self.z_dim)); 
            vh.imview({'disp', self}); 
        end
        
        function readMhaData(self,header)
            numdim  = header.NumberOfDimensions;
            data = double(mha_read_volume(header, numdim));
            switch numdim
                case 1
                    self.setVectorData(data);
                case 2
                    self.setVectorData(data(:,:,1), data(:,:,2));
                case 3
                    self.setVectorData(data(:,:,:,1), data(:,:,:,2), data(:,:,:,3));
            end
        end
        
        
    end
    

    
    methods (Access=private)
        
    end
end

