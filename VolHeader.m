%% 
classdef VolHeader < HandleStructBase
    %Weiguo Lu, 02/23/2011
    
    properties (Constant)

    end
    
    properties
       imageType
%        read_conversion
% 	   write_conversion
       data_type='float'
       byte_order=0
       bytes_pix=4
       vol_min=0
       vol_max=4096
%        dim_units='cm'
       x_dim=1
       y_dim=1
       z_dim=1
       x_pixdim=1
       y_pixdim=1
       z_pixdim=1
       x_start=0
       y_start=0
       z_start=0
       date
       time
       db_name
       uid
       referenceUID
       originalUID
       translation=[0 0 0]
       rotation=[0 0 0]
       patientPosition
       referenceImageIsocenter
       
       reverseX = false; 
       reverseY = false; 
       reverseZ = false; 
       
       %from dicom
       FrameOfReferenceUID;
%        ImagePositionPatient; 
       ImageOrientationPatient; 
 
       LinearMeasureUnit = 'cm';
       
       TransformMatrix; %a vector of length 9 that define x, y, z axis directions
    end
    
    
    methods
        function obj = VolHeader(varargin)
            if isempty(varargin) 
                return; 
            end
            if ischar(varargin{1})
                fname = varargin{1};
                if exist(fname, 'file')~=2
                    fname = [fname '.header']; 
                else
                    if isempty(regexpi(fname, '.header$'))
                        return; 
                    end
                end
                
                if ~exist(fname, 'file')
                    return;
                end
                
                header = parseTextFile([fname]);
                try
                if isnumeric(header.datatype)
                    switch header.datatype
                        case 1
                            header.data_type='short';
                    end
                end
                catch
                end
                obj = obj.copyStruct(header);
            elseif isstruct(varargin{1}) || isobject(varargin{1})
                obj = obj.copyStruct(varargin{1});
            end 
            
            if isempty(obj.reverseY) 
                if obj.isVolumeImageType
                    obj.reverseY = true; 
                else
                    obj.reverseY = false;
                end
            end
        end
        
        function header = MhdHeader(self)
            unit = self.getLinearMeasureUnit();
            
            self.setLinearMeasureUnit('mm');
            header = struct( 'NDims',  3, ...
                            'BinaryData', 'True', ...
                            'BinaryDataByteOrderMSB', 'False', ...
                            'CompressedData', 'False', ...
                            'TransformMatrix', [1 0 0 0 1 0 0 0 1], ...
                            'Offset', [0 0 0], ...
                            'CenterOfRotation', [0 0 0], ...
                            'AnatomicalOrientation', '', ...
                            'ElementSpacing', [], ...
                            'DimSize', [], ...
                            'ElementType', '', ...
                            'ElementDataFile', '');
            
            switch (upper(self.data_type))
                case {'SHORT'  'INT16'}
                    header.ElementType = 'MET_SHORT'; 
                case {'USHORT'  'UINT16'}
                    header.ElementType = 'MET_USHORT';   
                case {'INT'  'INT32' }
                    header.ElementType = 'MET_INT';    
                case {'UINT'  'UINT32' }
                    header.ElementType = 'MET_UINT';     
                case {'INT64'  'LONG' 'UINT64' }
                    header.ElementType = 'MET_LONG_LONG';       
                case {'FLOAT'  'FLOAT32' 'SINGLE'}
                    header.ElementType = 'MET_FLOAT';    
                case {'DOUBLE'  'FLOAT64'}
                    header.ElementType = 'MET_DOUBLE';     
            end            
                                    
            if numel(self.ImageOrientationPatient)==6
                Z = cross(self.ImageOrientationPatient(1:3),  self.ImageOrientationPatient(4:6)); 
                header.TransformMatrix = [self.ImageOrientationPatient(:)', Z(:)']; 
            elseif numel(self.ImageOrientationPatient)==9
                header.TransformMatrix = [self.ImageOrientationPatient(:)'];
            end

            prefix = 'xyz'; 
            for k=1:3
                header.DimSize(k)           =   self.([prefix(k) '_dim']);
                header.ElementSpacing(k)    =   abs(self.([prefix(k) '_pixdim']));
                header.Offset(k)            =   self.([prefix(k) '_start']);
            end
            
            header.ImageType = self.imageType;
            
            self.setLinearMeasureUnit(unit);
        end
        
        function FromMhdHeader(self, header)
            %             ObjectType: 'Image'
%                      NDims: 3
%                 BinaryData: 'True'
%     BinaryDataByteOrderMSB: 'False'
%             CompressedData: 'False'
%            TransformMatrix: [1 0 0 0 -1 0 0 0 1]
%                     Offset: [0 0 0]
%           CenterOfRotation: [0 0 0]
%      AnatomicalOrientation: 'RPI'
%             ElementSpacing: [0.9380 0.9380 3]
%                    DimSize: [512 512 61]
%                ElementType: 'MET_USHORT'
%            ElementDataFile: 'img200.raw'
            self.LinearMeasureUnit='mm'; 
            try
            self.ImageOrientationPatient=header.TransformMatrix;
            catch 
            end
            try
            self.referenceImageIsocenter=header.CenterOfRotation;
            catch
            end
            prefix = 'xyz'; 
            for k=1:3
                self.([prefix(k) '_dim'])     = header.DimSize(k); 
                try
                self.([prefix(k) '_pixdim'])  = header.ElementSpacing(k);
                catch 
                end
                try
                self.([prefix(k) '_start'])   = header.Offset(k);
                catch
                end
            end
            
            if isfield(header, 'ImageType')
                self.imageType = header.ImageType;
            end
        end
        
        function header = JsonHeader(self, varargin)
            %header = jsonencode(self.MhdHeader(), 'PrettyPrint',true); 
            %header = jsonencode(self.MhdHeader(), 'PrettyPrint',false); 
            header = jsonencode(self.MhdHeader(), varargin{:}); 
        end
        
        function writeJsonHeader(self, fname, varargin)
            header = JsonHeader(self, varargin{:});
            fid = fopen(fname,'wt');
            %fprintf(fid, header);
            fwrite(fid,header);
            fclose(fid);
        end
        
        function readJsonHeader(self, fname)
            text = fileread(fname);
            header = jsondecode(text);
            self.FromMhdHeader(header);
        end
        
        function res = ImagePositionPatient(self)
            res = [self.x_start, self.y_start, self.z_start]; 
        end
        
        function A = GetCoordinateMatrix(self)
            orientation = self.ImageOrientationPatient(:)'; 
            if isempty(orientation)
                orientation = [1 0 0 0 1 0];
            end
%             t = self.ImagePositionPatient(:)'; 
            t = [self.x_start self.y_start self.z_start]; 
            rx =  orientation(1:3);
            ry =  orientation(4:6);
            rz = cross(rx, ry); 
            R =[rx; ry; rz]; 
            A = [R zeros(3, 1); t 1]; 
        end
        
        function copyStructField(obj, info, dstField, srcField, scale, index)
            if ~exist('index', 'var')
                index = []; 
            end
            
            if ~exist('scale', 'var')
                scale = []; 
            end
            
            if ~exist('srcField', 'var')
                srcField = dstField;
            end
            
            if ~isfield(info, srcField) || ~isPropSetable(obj, dstField)
                return;
            end
            
            if isnumeric(info.(srcField))
                info.(srcField)=double(info.(srcField)); 
            end
            
            if ~isempty(index)
                obj.(dstField) = info.(srcField)(index); 
            else
                obj.(dstField) = info.(srcField); 
            end
            
            if ~isempty(scale) && isnumeric(obj.(dstField))
                obj.(dstField)=obj.(dstField)*scale; 
            end
        end
        
        function info = readDicomInfo(obj, fname, varargin)
            info = dicominfo(fname); 
            obj.copyDicomInfo(info, varargin{:});
        end
        
        function copyDicomInfo(obj, info, LengthUnitScale)
            if ~exist('LengthUnitScale', 'var')
                LengthUnitScale = 0.1; %from mm to cm
            end
            
            obj.copyStruct(info); 
            obj.copyStructField(info, 'imageType', 'ImageType');
            obj.copyStructField(info, 'x_dim', 'Width');
            obj.copyStructField(info, 'y_dim', 'Height');
            obj.copyStructField(info, 'x_pixdim', 'PixelSpacing', LengthUnitScale, 1);
            obj.copyStructField(info, 'y_pixdim', 'PixelSpacing', LengthUnitScale, 2);
            obj.copyStructField(info, 'z_pixdim', 'SliceThickness', LengthUnitScale);
        
            obj.copyStructField(info, 'x_start', 'ImagePositionPatient', LengthUnitScale, 1);
            obj.copyStructField(info, 'y_start', 'ImagePositionPatient', LengthUnitScale, 2);
            obj.copyStructField(info, 'z_start', 'ImagePositionPatient', LengthUnitScale, 3);
            
            obj.copyStructField(info, 'referenceImageIsocenter', 'IsocenterPosition', LengthUnitScale);
            obj.copyStructField(info, 'patientPosition', 'PatientPosition');
            
            obj.copyStructField(info, 'uid', 'StudyInstanceUID');
            
            obj.copyStructField(info, 'date', 'StudyDate');
            obj.copyStructField(info, 'time', 'StudyTime');
            obj.copyStructField(info, 'date', 'ContentDate');
            obj.copyStructField(info, 'time', 'ContentTime');   
            
            obj.copyStructField(info, 'ImagePositionPatient', 'ImagePositionPatient', LengthUnitScale);
        end
        
        function [physCoor, imagCoor] = convertDicomCoorPatient(obj, dicomCoor, ImagePositionPatient, ImageOrientationPatient)
           if ~exist('ImagePositionPatient', 'var') || isempty(ImagePositionPatient)
%                ImagePositionPatient=obj.ImagePositionPatient;   
                ImagePositionPatient = StructBase.getfieldx_default(obj,'ImagePositionPatient', [obj.x_start, obj.y_start, obj.z_start]); 
%                ImagePositionPatient = [obj.x_start, obj.y_start, obj.z_start]; %we may take out the property of ImagePositionPatient 
           end
           if ~exist('ImageOrientationPatient', 'var') || isempty(ImageOrientationPatient)
               ImageOrientationPatient=obj.ImageOrientationPatient; 
           end
           
           if isempty(ImageOrientationPatient) || isempty(ImagePositionPatient)
                physCoor = dicomCoor; 
                imagCoor = obj.physics2imageCoor(physCoor); 
                return; 
           end
           
           R(:, 1)= ImageOrientationPatient(1:3)';
           R(:, 2)= ImageOrientationPatient(4:6)';
           R(:, 3)= cross(R(:, 1), R(:, 2)); 
           if numel(ImagePositionPatient)<3
               ImagePositionPatient(3) = obj.z_start; 
           end
           %invR = inv(R); 
           dicomCoor = dicomCoor(:); ImagePositionPatient=ImagePositionPatient(:);
           pixSpacing = [obj.x_pixdim; obj.y_pixdim; obj.z_pixdim]; 
           imagCoor  = (R\(dicomCoor-ImagePositionPatient))./pixSpacing+[1 1 1]'; 
           physCoor  = obj.image2physicsCoor(imagCoor); 
        end
        
        %M is a 4x4 premultiply Frame-Of-Refernce affine transform matrix
        function ApplyFORAffineTransform(obj, M)
            A = DicomAffineMatrix(obj);
            B = M*A; 
            T = B(:, 4);
            R = B(1:3, 1:3); 
            for k=1:3
                s(k) = norm(R(:, k)); 
                R(:, k)=R(:, k)/s(k);
            end
            obj.x_start = T(1);  obj.y_start = T(2);  obj.z_start = T(3); 
            obj.x_pixdim = s(1); obj.y_pixdim = s(2); obj.z_pixdim = s(3); 
            obj.ImageOrientationPatient = R(:)';
        end
        
        %this is 4x4 affine matrix that convert dicomcoor to physicscoor(orientation is [1 0 0 0 1 0])  
        function B = Dicom2PhysicsTransformMatrix(obj)
% %           A=[RS T; 0 1]; A0 = [S T; 0 1]; P0 = A0*x; P = A*x; P0 = A0x=A0*inv(A)*P; B = A0*inv(A);
%             A = obj.DicomAffineMatrix; 
%             Spacing= abs([obj.x_pixdim; obj.y_pixdim; obj.z_pixdim]); 
%             A0= A; A0(1:3, 1:3) = eye(3).*Spacing; 
%             B = A0*inv(A); %           Dicom2PhysicsTransform Matrix
            A = obj.DicomAffineMatrix; 
            C = obj.PhysicsAffineMatrix;
            B = C*inv(A);
        end
        
        %p is 3xN
        function p1 = Dicom2PhysicsCoor(obj, p)
            B   = Dicom2PhysicsTransformMatrix(obj);
            res = B*[p; ones(1, size(p, 2))]; 
            p1  = res(1:3, :); 
        end
       
%         %T is 4x4 affine transform matrix (e.g, output from image registration), this is coordinate transform w/o
%         %motifying image data
        function ApplyAffineCoorTransform(obj, T)
            A = DicomAffineMatrix(obj);
%             c = obj.coor_center-obj.coor_start;
%             Tc= AffineMatrix4x4.OffsetMatrix(c);
%             A = inv(Tc)*A*Tc;
            B = T*A;
            ResetDicomAffineMatrix(obj, B);
        end
        
        function IgnoreCS(obj)
            vh = VolHeader; vh.x_dim = obj.x_dim; vh.y_dim = obj.y_dim; vh.z_dim = obj.z_dim;
            vh.alignXYZCenter;
            obj.ResetDicomAffineMatrix(vh.DicomAffineMatrix);
        end

        function IgnorePositionOrientation(obj)
            vh = VolHeader(obj); 
            vh.ImageOrientationPatient=[1 0 0 0 1 0];
            vh.alignXYZCenter;
            obj.ResetDicomAffineMatrix(vh.DicomAffineMatrix);
        end
        
        %A is a 4x4 premultiply (y=Ax) affine matrix  that convert imagecoor (index start at 0) to dicomcoor  
        function ResetDicomAffineMatrix(obj, A)
            if isa(A, 'VolHeader')
                A = A.DicomAffineMatrix; 
            end
            [orientation, s, t] = AffineMatrix4x4.DecomposeAffineMatrix(A);
            obj.ImageOrientationPatient=orientation(:)';
            obj.x_start =t(1);  obj.y_start=t(2);    obj.z_start=t(3);
            obj.x_pixdim=s(1); obj.y_pixdim=s(2);   obj.z_pixdim=s(3);
            obj.TransformMatrix=[];
        end
        
        function ResetPixelDim(obj, pixdim)
            A = DicomAffineMatrix(obj);
            [orientation, s, t] = AffineMatrix4x4.DecomposeAffineMatrix(A);
            s1 = pixdim; t1 = t(:)./s(:).*pixdim(:);
            B = AffineMatrix4x4.ComposeAffineMatrix(orientation, s1, t1);
            ResetDicomAffineMatrix(obj, B);
        end
        
        %this is 4x4 premultiply (y=Ax) affine matrix  that convert imagecoor (index start at 0) to dicomcoor  
        function A = DicomAffineMatrix(obj)
            if numel(obj.TransformMatrix)==16
                A = reshape(obj.TransformMatrix, 4, 4)';
                return;
%             elseif numel(obj.TransformMatrix)==9
%                 R= reshape(obj.TransformMatrix, 3, 3);
            end
%             R = ImageOrientationPatientMatrix(obj);
%             Spacing= abs([obj.x_pixdim; obj.y_pixdim; obj.z_pixdim]); 
%             R = R.*Spacing; 
%             T = [obj.x_start obj.y_start obj.z_start]';
%             A = [R T; 0 0 0 1]; 
            R = obj.ImageOrientationPatientMatrix4x4;
            S = eye(4); S(1, 1) = obj.x_pixdim; S(2, 2) = obj.y_pixdim; S(3, 3) = obj.z_pixdim;
            T = zeros(4); T(:, 4) = [obj.x_start, obj.y_start, obj.z_start, 0]'; 
            A = R*S+T; 
        end
        
        %this is 4x4 affine matrix that convert imagecoor (index start at 0) to physicscoor  
        function A = PhysicsAffineMatrix(obj)
            R = eye(4);
            S = eye(4); S(1, 1) = obj.x_pixdim; S(2, 2) = obj.x_pixdim; S(3, 3) = obj.z_pixdim;
            T = zeros(4); T(:, 4) = [obj.x_start, obj.y_start, obj.z_start, 0]'; 
            A = R*S+T; 
        end
        
        function R = ImageOrientationPatientMatrix(obj)
            orientation = obj.ImageOrientationPatient(:);
            if  numel(orientation)==0
                R = eye(3);
            elseif numel(orientation)==6
                R = reshape(orientation, [3 2]); 
                R(:, 3)= cross(R(:, 1), R(:, 2)); 
            elseif numel(orientation)==9
                R = reshape(orientation, [3 3]); 
            end
        end
        
        function R = ImageOrientationPatientMatrix4x4(obj)
            R = eye(4); 
            R(1:3, 1:3) = ImageOrientationPatientMatrix(obj);
        end
        
        %Dicom and ITK is LPS system, Nifti is RAS system 
        function [X, Y, Z] = ImageCoorGrid(obj)
            [X, Y, Z]=meshgrid(1:obj.x_dim, 1:obj.y_dim, 1:obj.z_dim);
        end

        function [X, Y, Z] = DicomCoorGrid(obj)
            [Xi, Yi, Zi]=meshgrid(1:obj.x_dim, 1:obj.y_dim, 1:obj.z_dim);
            dicomCoor   =Image2DicomCoor(obj, [Xi(:)'; Yi(:)'; Zi(:)']);
            S = size(Xi);
            X = reshape(dicomCoor(1, :), S);
            Y = reshape(dicomCoor(2, :), S);
            Z = reshape(dicomCoor(3, :), S);
        end
        
        function [X, Y, Z] = PhysicsCoorGrid(obj)
            [X, Y, Z]=meshgrid(obj.xData, obj.yData, obj.zData);
        end

        %input coordinate must be in 3xN format
        function imagCoor = Dicom2ImageCoor(obj, dicomCoor)
%             R = reshape(obj.ImageOrientationPatient, [3 2]); 
%             R(:, 3)= cross(R(:, 1), R(:, 2)); 
%             Spacing= abs([obj.x_pixdim; obj.y_pixdim; obj.z_pixdim]); 
%             Start  = [obj.x_start obj.y_start obj.z_start]';
%             imagCoor= (R\(dicomCoor(:)-Start))./Spacing+[1 1 1]'; 
            A   = DicomAffineMatrix(obj);
            N   = size(dicomCoor, 2); %number of points
            res = A\[dicomCoor; ones(1, N)];
            imagCoor = res(1:3, :) + ones(3, N); %matlab index starts at 1
        end
        
        %input coordinate must be in 3xN format
        function dicomCoor = Image2DicomCoor(obj, imagCoor)
%             R = reshape(obj.ImageOrientationPatient, [3 2]); 
%             R(:, 3)= cross(R(:, 1), R(:, 2)); 
%             Spacing= abs([obj.x_pixdim; obj.y_pixdim; obj.z_pixdim]); 
%             Start  = [obj.x_start obj.y_start obj.z_start]';
%             dicomCoor  = R*(imagCoor(:)-[ 1 1 1]').*Spacing + Start; 
            A        = DicomAffineMatrix(obj);
            N        = size(imagCoor, 2); %number of points
%             imagCoor = reshape(imagCoor, 3, N); 
            res      = A*[imagCoor-ones(3, N); ones(1, N)];
            dicomCoor = res(1:3, :);
        end
        
        %used in cropImage or padImage, to change the start voxel
        function ResetStartVoxelIndex(obj, startImagCoor)
            dicomCoor = Image2DicomCoor(obj, startImagCoor(:));
            obj.x_start = dicomCoor(1); 
            obj.y_start = dicomCoor(2);
            obj.z_start = dicomCoor(3);
        end
        
         %used in cropImage or padImage, to change the start voxel
         function ResetCenterVoxelDcmCoor(obj, dcmcoor)
%             A        = DicomAffineMatrix(obj);
%             c   = [obj.x_dim/2-0.5 obj.y_dim/2-0.5 obj.z_dim/2-0.5 1]';
%             dicomCoor0 = A*c;
            dcmcoor0 = dcmcoor_center(obj);
            offset   = dcmcoor(:)-dcmcoor0(:);
            T = AffineMatrix4x4.OffsetMatrix(offset);
            ApplyAffineCoorTransform(obj, T);
        end

          %used in cropImage or padImage, to change the start voxel
          function ResetDcmCoor(obj, srcdcmcoor, dstdcmcoor)
%             A        = DicomAffineMatrix(obj);
%             c   = [obj.x_dim/2-0.5 obj.y_dim/2-0.5 obj.z_dim/2-0.5 1]';
%             dicomCoor0 = A*c;
            offset   = dstdcmcoor(:)-srcdcmcoor(:);
            T = AffineMatrix4x4.OffsetMatrix(offset);
            ApplyAffineCoorTransform(obj, T);
         end

        function mask = RtContours2Mask(vh, contours)
            mask = false([vh.y_dim, vh.x_dim, vh.z_dim]); 

            for k = 1:numel(contours)
                curve = contours{k};
                curve = double(curve)'; 
                if numel(curve)<3
                    fprintf('Skipped empty contour\n');
                else
                    imagcoors = vh.Dicom2ImageCoor(curve); 
                    
                    % binary mask of current curve
                   
                    Z = imagcoors(3, :);
                    
                    % merge with existing curve
%                     if sum(BW(:))==0 %at least one voxel
%                         BW(round(mean(Y)), round(mean(X)))=1;
%                     end
                    slice = round(mean(Z));
                    if slice>=1 && slice<=vh.z_dim
                         X = imagcoors(1, :);
                         Y = imagcoors(2, :);
                         BW= poly2mask(X, Y, vh.y_dim, vh.x_dim);
%                          mask(:,:,slice) = logical(xor(BW, mask(:,:,slice)));
                    
                          %avoid duplicated contours

                        BW1 = xor( mask(:,:,slice), BW);

                        if sum(BW1(:))>0

                           mask(:,:,slice) =BW1;  
                        end


                    end
                end
            end
        end
        
        function flag = isVolumeImageType(obj)
            flag = false; 
            if isempty(obj.imageType)
                return; 
            end
            for k=1:length(obj.DefaultVolumeImageTypes)
                imgtype = obj.DefaultVolumeImageTypes{k}; 
                if ~isempty(strfind(lower(obj.imageType), imgtype))
                    flag = true;
                    return; 
                end
            end
        end
        
        function val = dim(obj)
            val = [obj.y_dim obj.x_dim obj.z_dim]; 
        end
        
        function val = pixelDim(obj)
            val = [obj.y_pixdim obj.x_pixdim obj.z_pixdim]; 
        end
        
        function val = startPos(obj)
            val = [obj.y_start obj.x_start obj.z_start]; 
        end
        

        function val = getDim(obj)
            val = [obj.x_dim obj.y_dim obj.z_dim]; 
        end
        
        function val = getPixelDim(obj)
            val = [obj.x_pixdim obj.y_pixdim obj.z_pixdim]; 
        end
        
        function val = getStartPos(obj)
            val = [obj.x_start obj.y_start obj.z_start]; 
        end

        function setDim(obj, dim)
            obj.x_dim = dim(1); obj.y_dim = dim(2); obj.z_dim = dim(3);
        end

        function setPixelDim(obj, pixdim)
            obj.x_pixdim = pixdim(1); obj.y_pixdim = pixdim(2); obj.z_pixdim = pixdim(3);
        end

        function setStartPos(obj, startpos)
            obj.x_start = startpos(1); obj.y_start = startpos(2); obj.z_start = startpos(3);
        end

%         function R = getImageOrientationPatientMatrix(obj)
%            if numel(obj.ImageOrientationPatient)<6
%                R=[]; return; 
%            end
%            R(:, 1)= obj.ImageOrientationPatient(1:3)';
%            R(:, 2)= obj.ImageOrientationPatient(4:6)';
%            R(:, 3)= cross(R(:, 1), R(:, 2)); 
%         end
        
        %%oriented pixdim
        function pixdim = pixelSpace(obj, dir)
%             pixdim = obj.imageOrientation.(dir)*obj.([dir '_pixdim']); 
            pixdim = obj.([dir '_pixdim']);
        end
        
        
        function flag = isEqualCS(obj, dst)
            flag = isEqualOrientation(obj, dst) && isEqualDim(obj, dst);
        end

        function flag = isEqualOrientation(obj, dst)
            orientation1 = obj.ImageOrientationPatientMatrix;
            orientation2 = dst.ImageOrientationPatientMatrix;
            flag = all(abs(orientation1(:)-orientation2(:))<1e-5);
        end

        function flag = isEqualDim(obj, dst)
            flag = all(obj.dim==dst.dim) && all(abs(obj.pixelDim-dst.pixelDim)<1e-5) && all(abs(obj.startPos-dst.startPos)<1e-5);
        end
        
        function index = imageCoor2index(obj, ip)
            index = (ip(:, 3)-1)*(obj.x_dim*obj.y_dim) + (ip(:, 1)-1)*obj.y_dim + ip(:, 2);
        end
        
        function index = imageCoor2index_c(obj, ip)
            index = (ip(:, 3)-1)*(obj.x_dim*obj.y_dim) + (ip(:, 2)-1)*obj.x_dim + ip(:, 1)-1;
        end
        
        function ip = index2imageCoor(obj, index)
            index = index-1;
            index = double(index(:));
            ip(:,3) = floor(index/(obj.x_dim*obj.y_dim))+1; %z
            index = mod(index, (obj.x_dim*obj.y_dim)); 
            ip(:,1) = floor(index/obj.y_dim)+1;  %x
            ip(:,2) = mod(index, obj.y_dim)+1;   %y
        end
        
        function p = index2physicsCoor(obj, index)
            ip = index2imageCoor(obj, index);
%             p = image2physicsCoor(obj, ip);
            N = size(ip, 1);
            p = repmat([obj.x_start obj.y_start obj.z_start], [N, 1]) + (ip-1).*repmat([obj.x_pixdim obj.y_pixdim obj.z_pixdim], [N, 1]);
        end
        
        function p = image2physicsCoor(obj, ip)
            dir={'x','y','z'}; 
            for k=1:numel(ip)
                p(k) = obj.image2physics(ip(k), dir{k});
            end
        end
        
        function ip = physics2imageCoor(obj, p)
            dir={'x','y','z'}; 
            for k=1:numel(dir)
                ip(k) = obj.physics2image(p(k), dir{k});
            end
        end
        
        function p = image2physicsCoors(obj, ip)
            numpoints = size(ip, 1); 
            for k=1:numpoints
                p(k, :) = image2physicsCoor(obj, ip(k, :));
            end
        end
        
        function ip = physics2imageCoors(obj, p)
           numpoints = size(p, 1); 
            for k=1:numpoints
                ip(k, :) = physics2imageCoor(obj, p(k, :));
            end
        end
        
        function p = image2physics_dir(obj, ip, dir)
            dir = lower(dir); 
            index = ip-1; 
            if isfield(obj,(['reverse' upper(dir)])) && obj.(['reverse' upper(dir)])
                index = obj.([dir '_dim'])-ip; 
            end
            p = index*obj.([dir '_pixdim'])*obj.imageOrientation.(dir)+obj.([dir '_start']); 
        end
        
        function p = image2physics(obj, ip, dir)
            dir = lower(dir); 
            index = ip-1; 
            if isfield(obj,(['reverse' upper(dir)])) && obj.(['reverse' upper(dir)])
                index = obj.([dir '_dim'])-ip; 
            end
            p = index*obj.([dir '_pixdim'])+obj.([dir '_start']); 
        end
        
        function ip = physics2image(obj, p, dir)
            dir = lower(dir);
            ip = (p-obj.([dir '_start']))/obj.([dir '_pixdim'])+1; 
            if isfield(obj,(['reverse' upper(dir)])) && obj.(['reverse' upper(dir)])
                ip =  obj.([dir '_dim'])-ip +1; 
            end
        end
                
        function orientation =  imageOrientation(obj)
            if numel(obj.ImageOrientationPatient)==6
                orientation.x = obj.ImageOrientationPatient(1) ;
                orientation.y = obj.ImageOrientationPatient(5) ;
                orientation.z = orientation.x*orientation.y;
            else
                orientation = struct('x', 1, 'y', 1, 'z', 1); 
            end
        end
        
        function val = coor_start(obj)
            val = [obj.x_start obj.y_start obj.z_start]; 
        end

        %% tobe retired, use dcmcoor_center instead
        function val = coor_center(obj)
            val = [x_center(obj) y_center(obj) z_center(obj)]; 
        end
        
        function val = physcoor_center(obj)
            val = [x_center(obj) y_center(obj) z_center(obj)]; 
        end

        function val = dcmcoor_center(obj)
            A   = DicomAffineMatrix(obj);
            c   = [obj.x_dim/2-0.5 obj.y_dim/2-0.5 obj.z_dim/2-0.5 1]';
            val = A*c;
            val = val(1:3)'; 
        end

        function val = x_center(obj)
%             val = obj.x_start +(obj.x_dim/2-0.5)*obj.x_pixdim;
             val = obj.x_start +(obj.x_dim/2-0.5)*obj.pixelSpace('x');
        end
        
        function val = y_center(obj)
%             val = obj.y_start +(obj.y_dim/2-0.5)*obj.y_pixdim;
            val = obj.y_start +(obj.y_dim/2-0.5)*obj.pixelSpace('y');
        end
        
        function val = z_center(obj)
%             val = obj.z_start +(obj.z_dim/2-0.5)*obj.z_pixdim;
            val = obj.z_start +(obj.z_dim/2-0.5)*obj.pixelSpace('z');
        end
        
        function val = x_end(obj)
%             val = obj.x_start +(obj.x_dim)*obj.x_pixdim;
             val = obj.x_start+(obj.x_dim-1)*obj.pixelSpace('x');
        end
        
        function val = y_end(obj)
%             val = obj.y_start +(obj.y_dim)*obj.y_pixdim;
            val = obj.y_start +(obj.y_dim-1)*obj.pixelSpace('y');
        end
        
        function val = z_end(obj)
%             val = obj.z_start +(obj.z_dim)*obj.z_pixdim;
           val = obj.z_start +(obj.z_dim-1)*obj.pixelSpace('z');
        end
        
        function alignToPoint(obj,srcPos, dstPos, dirs)
            if isstruct(srcPos)
                if ~exist('dirs', 'var')
                    dirs = 'xyz'; 
                end
                for k=1:numel(dirs)
                    dir = dirs(k); 
                    obj.([dir '_start']) = obj.([dir '_start']) + dstPos.(dir) - srcPos.(dir);
    %                 obj.([dir '_start']) = obj.([dir '_start']) + obj.imageOrientation.(dir)*(dstPos.(dir) - srcPos.(dir));
                end
            else
                offset = dstPos-srcPos; 
                obj.x_start = obj.x_start + offset(1);
                obj.y_start = obj.y_start + offset(2);
                obj.z_start = obj.z_start + offset(3);
            end
        end
        
        function alignToDstCenter(obj, dst, dir)
            dir = lower(dir); 
%             K = length(dir); 
%             if K>1
%                 for k=1:K
%                     obj.alignToDstCenter(dst, dir(k));
%                 end
%                 return; 
%             end
%             
%             srcCenter = obj.([dir '_start']) +(obj.([dir '_dim'])/2-0.5)*obj.([dir '_pixdim']);
%             dstCenter = dst.([dir '_start']) +(dst.([dir '_dim'])/2-0.5)*dst.([dir '_pixdim']);
%             obj.([dir '_start']) = obj.([dir '_start']) + dstCenter - srcCenter;
            for k=1:numel(dir)
                srcCenter.(dir(k)) = obj.([dir(k) '_center']);
                dstCenter.(dir(k)) = dst.([dir(k) '_center']);
            end
            alignToPoint(obj,srcCenter, dstCenter, dir);
        end
        
        
        function alignCoorCenter(obj, dir)
            c = lower(dir); 
            N = obj.([c '_dim']); 
%             obj.([c '_start']) = -(N/2-0.5)*obj.([c '_pixdim']);
            obj.([c '_start']) = -(N/2-0.5)*obj.pixelSpace(c);
%             C=upper(dir); 
%             if isfield(obj,['reverse' C]) && obj.(['reverse' C])
%                 obj.([c '_start']) = -obj.([c '_start']);
%             end
        end
        
        function alignXYCenter(obj)
%             obj.x_start = -(obj.x_dim/2-0.5)*obj.x_pixdim; 
%             obj.y_start = -(obj.y_dim/2-0.5)*obj.y_pixdim; 
%             if isfield(obj,'reverseX') && obj.reverseX
%                 obj.x_start = (obj.x_dim-(obj.x_dim+1)/2)*obj.x_pixdim;
%             end
%             if isfield(obj,'reverseY') && obj.reverseY
%                 obj.y_start = (obj.y_dim-(obj.y_dim+1)/2)*obj.y_pixdim;
%             end
            obj.alignCoorCenter('x');
            obj.alignCoorCenter('y');
        end
        
        function alignXYZCenter(obj)
%             obj.x_start = -(obj.x_dim/2-0.5)*obj.x_pixdim; 
%             obj.y_start = -(obj.y_dim/2-0.5)*obj.y_pixdim; 
%             obj.z_start = -(obj.z_dim/2-0.5)*obj.z_pixdim; 
            obj.alignCoorCenter('x');
            obj.alignCoorCenter('y');
            obj.alignCoorCenter('z');
        end
        
        function obj = copyStruct(obj, header)
            names = fieldnames(obj);
            for k=1:length(names)
                name = names{k};
                if isfield(header, name) && isPropSetable(obj, name)
                    obj.(name) = header.(name); 
                end
            end
            if isfield(header, 'dimensions')
                if length(header.dimensions)>=1
                    obj.x_dim = header.dimensions(1); 
                end
                if length(header.dimensions)>=2
                    obj.y_dim = header.dimensions(2);
                end
                if length(header.dimensions)>=3
                    obj.z_dim = header.dimensions(3);
                end
            end
            if isfield(header, 'databaseUID');
                obj.uid = header.databaseUID;
            end
            if isfield(header, 'dataType')
                obj.data_type = header.dataType;
            end
            if strcmpi(obj.data_type, 'float_data')||strcmpi(obj.data_type, 'float')||strcmpi(obj.data_type, 'float32')
                obj.setFloat;
            end
        end

        function setDataType(obj, dataType)
            switch(lower(dataType))
                case {'float', 'single', 'float32'}
                    obj.data_type = 'float'; 
                    obj.bytes_pix = 4; 
                case {'double', 'float64'}
                    obj.data_type = 'double'; 
                    obj.bytes_pix = 8; 
                case {'int8', 'byte'}
                    obj.data_type = 'int8'; 
                    obj.bytes_pix = 1; 
                case {'short', 'int16'}
                     obj.data_type = 'int16'; 
                     obj.bytes_pix = 2;     
                case {'int32', 'int'}
                    obj.data_type = 'int32'; 
                    obj.bytes_pix = 4; 
                case {'int64', 'long'}
                    obj.data_type = 'int64'; 
                    obj.bytes_pix = 8;   
                case {'uint8', 'ubyte', 'uchar', 'char'}
                    obj.data_type = 'uint8'; 
                    obj.bytes_pix = 1; 
                case {'uint16', 'ushort'}
                     obj.data_type = 'uint16'; 
                     obj.bytes_pix = 2;     
                case {'uint32', 'uint'}
                    obj.data_type = 'uint32'; 
                    obj.bytes_pix = 4; 
                case {'uint64', 'ulong'}
                    obj.data_type = 'uint64'; 
                    obj.bytes_pix = 8;       
            end
        end
        
        function obj = setFloat(obj)
            obj.data_type = 'single'; 
            obj.bytes_pix = 4; 
        end
        
        function obj = setShort(obj)
            obj.data_type = 'int16'; 
            obj.bytes_pix = 2; 
        end     
        
        function obj = setInt(obj)
            obj.data_type = 'int32'; 
            obj.bytes_pix = 4; 
        end
        
        function obj = setChar(obj)
            obj.data_type = 'int8'; 
            obj.bytes_pix = 1; 
        end
        
        function val = xData(obj)
            %val = obj.x_start+ (0: (obj.x_dim-1))*obj.x_pixdim;
            val = obj.image2physics(1:obj.x_dim, 'x'); 
        end
        
        function val = yData(obj)
            %val = obj.y_start+ (0: (obj.y_dim-1))*obj.y_pixdim;
            val = obj.image2physics(1:obj.y_dim, 'y'); 
        end
        
        function val = zData(obj)
            %val = obj.z_start+ (0: (obj.z_dim-1))*obj.z_pixdim;
            val = obj.image2physics(1:obj.z_dim, 'z'); 
        end
        
        function val = coorData(obj, dir)
            switch lower(dir)
                case {2, 'x'}
                    val = obj.xData; 
                case {1, 'y'}
                    val = obj.yData; 
                case {3, 'z'}
                    val = obj.zData; 
            end
        end
        
        function hdr= header(obj, names)
            %names = properties(VolHeader);
            if ~exist('names', 'var')
                names = properties(obj);
            end
            
            for k=1:length(names)
                name = names{k};
                if ~strcmpi(name, 'data')
                    hdr.(name) = obj.(name); 
                end
            end          
        end
        
        function writeFile(obj, fname)
            hdr = obj.header; 
            writeStruct2File(fname, hdr); 
        end
        
        
        
        function fun = typeFun(obj)
            switch lower(obj.data_type)
            case {'uint8', 'uchar', 'char'}
                fun = @uint8;
            case {'uint16', 'short', 'ushort'}
                fun = @uint16;
            case {'float' , 'float32', 'single'}
                fun = @double;
            case {'double', 'float64'}
                fun = @double;
            otherwise
                fun = @double;
            end
        end
        
        function machine = machineFormat(obj)
            switch obj.byte_order
               case 0
                  machine = 'ieee-le';
               case 1
                  machine = 'ieee-be';
            end
        end
        
        function res = VoxelSize(self)
            res = self.x_pixdim*self.y_pixdim*self.z_pixdim; 
        end
        
        function res = VoxelSizeInCC(self)
            res = VoxelSize(self);
            if strcmpi(self.LinearMeasureUnit, 'mm')
                res = res/1000; 
            end
        end

        function showGantryAngles(obj, ppr, radius, center)
            if nargin <2
                ppr = 51; 
            end
            
            if nargin <4
                if ~isempty(obj.referenceImageIsocenter)
                    center = obj.referenceImageIsocenter;
                else
                    center = [obj.x_center, obj.y_center]; 
                end
            end
            
            if isstruct(center)
                 center = [center.x center.y]; 
            end
            
            if nargin <3
                %radius = min(obj.x_dim*obj.x_pixdim/2, obj.y_dim*obj.y_pixdim/2)-1; 
                radius = 20; 
            end
            
            deltaAngle = 360/ppr;
            angles = ((1:ppr)-1)*deltaAngle;
            %angles = mod(angles+90, 360); 

            
            x = center(1) + radius*cos((angles-90)*pi/180); 
            
            if ~obj.reverseY
                y = center(2) + radius*sin((angles-90)*pi/180); 
            else
                y = center(2) - radius*sin((angles-90)*pi/180); 
            end
            
            x = obj.physics2image(x, 'X'); 
            y = obj.physics2image(y, 'Y'); 
            plot(x, y); hold on; 
            for k=1:3:length(angles)
                text(x(k), y(k), num2str(round(angles(k))), 'color', 'g'); 
            end
        end
        
        function [firstSlice, lastSlice] = calcOverlapSlices(header1, header2, whichdim)
            if nargin <3
                whichdim = 'z'; 
            end  
            start1 = header1.([whichdim '_start']); 
            start2 = header2.([whichdim '_start']);
            dim1 = header1.([whichdim '_dim']); 
            dim2 = header2.([whichdim '_dim']);
            pixdim1 = header1.([whichdim '_pixdim']); 
            pixdim2 = header2.([whichdim '_pixdim']);
            
            firstSlice = 1+round((start2-start1)/pixdim1); 
            lastSlice  = 1+round((start2+dim2*pixdim2-start1)/pixdim1); 
            firstSlice = max(1, firstSlice); 
            lastSlice = min(dim1, lastSlice); 
        end
        
        function showAxesPosition(obj, axH, whichView, transpose, varargin)   
            switch lower(whichView)
                case {'t', 'transverse'} 
                    order = [1 2 3 4]; 
                case {'c', 'coronal'}
                    order = [5 6 1 2]; 
                case {'s', 'sagittal'}
                    order = [5 6 3 4]; 
            end
            
            if transpose
                order = [order(3:4) order(1:2)]; 
            end
            axdir = VolHeader.getImageAxesDir(obj.patientPosition);
            for k=1:4
                strs{k} = axdir{order(k)};  
            end
            VolHeader.showAxesPositionText(axH, strs, varargin{:});
        end
        
        function [tick, ticklabel] = getAxesTick(obj, dir, delta)
            if nargin <3
                delta = 5; %cm
            end
            dir = lower(dir); 
%             Delta = delta/obj.([dir '_pixdim']);
%             ip = 1:Delta:obj.([dir '_dim']);
%             xdata = obj.image2physics(ip, dir);  
%             tick0 = round(xdata/delta+0.5)*delta; 
%             %tick{k} = 1 +  (tick0{k} - startPos(k))/pixdim(k); 
%             tick = obj.physics2image(tick0, dir); 
% 
%             xdata1 = obj.image2physics_dir(ip, dir);  
%             tick1  = round(xdata1/delta+0.5)*delta; 
%             ticklabel = num2str(tick1'); 
            xdata = obj.([dir 'Data']);
            minV = round(min(xdata(:))/delta)*delta;
            maxV = round(max(xdata(:))/delta)*delta;
            tick1 = minV:delta:maxV;
            ticklabel = num2str(tick1(:)); 
            tick = obj.physics2image(tick1, dir); 
            [tick, I] = sort(tick); 
            ticklabel = ticklabel(I, :); 
        end
        
        
        function applyLinearMeasureUnitConversion(obj, convFactor)
            if convFactor==1
                return;
            end
            fn = obj.LinearMeasureFieldNames;
            for k=1:numel(fn)
                obj.(fn{k}) = obj.(fn{k})*convFactor; 
            end
        end
        
        
        function unit = getLinearMeasureUnit(obj)
            unit = obj.LinearMeasureUnit; 
        end
        
        function setLinearMeasureUnit(obj, unit)
            unit0 = obj.LinearMeasureUnit; 
            convFactor = 1; 
            if strcmpi(unit, unit0)
                return; 
            elseif strcmpi(unit, 'mm') &&  strcmpi(unit0, 'cm') 
                convFactor = 10; 
            elseif strcmpi(unit, 'cm') &&  strcmpi(unit0, 'mm') 
                convFactor = 0.1; 
            end
            if convFactor~=1
                applyLinearMeasureUnitConversion(obj, convFactor);
                obj.LinearMeasureUnit=unit; 
            end 
        end
        
        function showPolyBox(obj, poly, axH,varargin)
            if ~exist('axH', 'var')
                axH = gca; 
            end
            X = obj.physics2image(poly(1, :), 'x'); 
            Y = obj.physics2image(poly(2, :), 'y'); 
            X=[X X(1)]; 
            Y=[Y Y(1)];
            h = line(X, Y, 'parent', axH); 
            if numel(varargin)>=1
                set(h, varargin{:}); 
            end
        end
        
        function refcs = ComposeReformCS(self, varargin)
           options = OptionsMap(varargin{:});
           E1=self.ImageOrientationPatientMatrix;
           orientation = options.getOption('orientation', self.ImageOrientationPatient);               
           pixdim0 = [self.x_pixdim self.y_pixdim self.z_pixdim];
           pixdim(1) = options.getOption('x_pixdim', pixdim0(1)); 
           pixdim(2) = options.getOption('y_pixdim', pixdim0(2));
           pixdim(3) = options.getOption('z_pixdim', pixdim0(3));

           dim0 = [self.x_dim self.y_dim self.z_dim];
           L1 = dim0.* pixdim0;
           [~, E2]=AffineMatrix4x4.ImageOrientationMatrix(orientation);
           L2 = abs(E2'*E1*L1(:));
           dim1 = round(L2(:)'./pixdim(:)');
           dim(1) = options.getOption('x_dim', dim1(1)); 
           dim(2) = options.getOption('y_dim', dim1(2));
           dim(3) = options.getOption('z_dim', dim1(3));

           dcmcenter = options.getOption('dcmcenter', self.dcmcoor_center);
           LinearMeasureUnit=options.getOption('LinearMeasureUnit', self.LinearMeasureUnit);
           refcs = VolHeader.ComposeVolHeader(orientation, pixdim, dcmcenter, dim, {'LinearMeasureUnit', LinearMeasureUnit});
        end
        
        function flag = HasNanProperty(self)
            flag = isnan(self.x_dim)||isnan(self.x_pixdim)||isnan(self.x_start)||...
                   isnan(self.y_dim)||isnan(self.y_pixdim)||isnan(self.y_start)||...
                   isnan(self.z_dim)||isnan(self.z_pixdim)||isnan(self.z_start);
        end

        function ReplaceNanWithRef(self,refcs, varargin)
            options = OptionsMap(varargin{:});
            dirs = {'x_', 'y_', 'z_'};
            orientation = self.ImageOrientationPatient;
            LinearMeasureUnit=self.LinearMeasureUnit;
            refcs.setLinearMeasureUnit(LinearMeasureUnit);
            dims   = [self.x_dim, self.y_dim, self.z_dim];
            pixdims= [self.x_pixdim, self.y_pixdim, self.z_pixdim];
            for k=1:numel(dirs)
                prefix = dirs{k};
                dim = dims(k); pixdim = pixdims(k); 
                if isnan(dim)&&~isnan(pixdim) %resample
                   dims(k) = round(refcs.([prefix 'pixdim'])*refcs.([prefix 'dim'])/pixdim);
                elseif isnan(pixdim)&&~isnan(dim) %cropping
                   pixdims(k)=refcs.([prefix 'pixdim']);  
                elseif isnan(pixdim)&&isnan(dim)
                   pixdims(k)=refcs.([prefix 'pixdim']); 
                   dims(k)=refcs.([prefix 'dim']); 
                end
            end
            dcmcenter = options.getOption('dcmcenter', refcs.dcmcoor_center);
            % atlascs   = VolHeader.ComposeVolHeader(orientation, pixdims, dcmcenter, dims, {'LinearMeasureUnit', LinearMeasureUnit});
            self.x_dim = dims(1); self.y_dim = dims(2); self.z_dim = dims(3);
            A = AffineMatrix4x4.ComposeAffineMatrix(orientation, pixdims, [0 0 0]);
            self.ResetDicomAffineMatrix(A);
            self.ResetCenterVoxelDcmCoor(dcmcenter);
        end
        
        function [vh] = CreateReformVolHeader(obj, cfg)
            vh = VolHeader(obj);
            vh.setLinearMeasureUnit('mm');

            dim  = vh.getDim();            
            pixdim = vh.getPixelDim(); 
            startpos= vh.getStartPos();

            fixedorientation = StructBase.getfieldx(cfg, 'FixedOrientation'); 
            fixepixdim = StructBase.getfieldx(cfg, 'FixedVoxelSize_mm'); fixepixdim=fixepixdim(:)';
            if ~isempty(fixedorientation) ||~isempty(fixepixdim)
                vh = vh.ReformVolHeader(fixedorientation, fixepixdim);
            end

            minpixdim  = StructBase.getfieldx(cfg, 'MinVoxelSize_mm');
            if ~isempty(minpixdim) && any(minpixdim>pixdim)
                newpixdim = max(minpixdim, pixdim);
                newdim = round(dim.*pixdim./newpixdim); 
                dim    = newdim;
                pixdim = newpixdim;
                vh.setDim(dim); vh.setPixelDim(pixdim);
            end

            maxdim  = StructBase.getfieldx(cfg, 'MaxDim'); maxdim=maxdim(:)';
            if ~isempty(maxdim) && any(dim>maxdim)
                newdim = min(dim, maxdim);
                dimmaxmode = StructBase.getfieldx_default(cfg, 'MaxDimMode', 'scale');
                if strcmpi(dimmaxmode, 'crop')
                    c        = physcoor_center(obj);
                    newpixdim = pixdim; 
                    newstart = c-(newdim/2-0.5)./newpixdim;
                else
                    scale  = dim./newdim; 
                    index  = StructBase.getfieldx_default(cfg, 'aspectreserveindex', [1 2]); 
                    if ~isempty(index)
                        scale(index) = max(scale(index));
                    end
                    newdim    = round(dim./scale);
                    newpixdim = dim.*pixdim./newdim;  
                    newstart  = startpos; 
                end
                dim    = newdim;
                pixdim = newpixdim;
                startpos=newstart;
                vh.setDim(dim); vh.setPixelDim(pixdim); vh.setStartPos(startpos);
            end
            vh.setLinearMeasureUnit(obj.LinearMeasureUnit);
        end

        function vh = ReformVolHeader(obj, orientation, pixdim, varargin)
            if ~exist('orientation','var')|| isempty(orientation)
                orientation=obj.ImageOrientationPatient;
            end
            if ~exist('pixdim','var')|| isempty(pixdim)
                pixdim=[obj.x_pixdim, obj.y_pixdim, obj.z_pixdim];
            end
            %if ~exist('dim2', 'var') || isempty(dim2)
                L1 = [obj.x_dim, obj.y_dim, obj.z_dim].* [obj.x_pixdim, obj.y_pixdim, obj.z_pixdim];
                E1=obj.ImageOrientationPatientMatrix;
                [~, E2]=AffineMatrix4x4.ImageOrientationMatrix(orientation);
                L2 = abs(E2'*E1*L1(:));
                dim = round(L2(:)'./pixdim(:)');
            %end
            options = OptionsMap(varargin{:});
            dim(1)=options.getoptioni('out.x_dim', dim(1));
            dim(2)=options.getoptioni('out.y_dim', dim(2));
            dim(3)=options.getoptioni('out.z_dim', dim(3));
            dcmcenter = obj.dcmcoor_center;
            vh = VolHeader.ComposeVolHeader(orientation, pixdim, dcmcenter, dim, {'LinearMeasureUnit', obj.LinearMeasureUnit});
        end
        

        %LPS are positive directions, in image coor
        function se = strel_noniso(self, margin)
            if self.z_dim>1
                se = strel3d_noniso(self, margin);
            elseif self.y_dim>1
                se = strel2d_noniso(self, margin);
            end
        end
        
        function se = strel3d_noniso(self, margin)
            margin = margin(:)';
            if numel(margin)==1
                margins = margin*ones(1, 6); 
            elseif numel(margin)==2
                margins = [margin(1) margin(1) margin(2) margin(2) 0 0];
            elseif numel(margin)==3
                margins = [margin(1) margin(1) margin(2) margin(2)  margin(3) margin(3)];
            elseif numel(margin)==4
                margins = [margin 0 0];
            elseif numel(margin)==6
                margins = [margin];
            end
            
            %margins(margins==0)=Inf;
           
            left  = round(margins(1)/self.x_pixdim); 
            right = round(margins(2)/self.x_pixdim); 
            
            top = round(margins(3)/self.y_pixdim);
            bottom = round(margins(4)/self.y_pixdim);
            
            inferior = round(margins(5)/self.z_pixdim);
            superior = round(margins(6)/self.z_pixdim);
            
            %left, right, top, bottom, superior, inferior;
            M = max(top, bottom); N = max(left, right); K = max(superior, inferior);
            x = -N:N; y = -M:M; z = -K:K;
            [X, Y, Z] = meshgrid(x, y, z);
%             if numel(margin)<=3
%                 switch numel(margin)
%                     case {1, 3}
%                         I = (X/left).^2+(Y/top).^2+(Z/superior).^2<=1;
%                     case 2
%                         I = (X/left).^2+(Y/top).^2<=1;
%                 end
%             else
%                 I = zeros(2*M+1, 2*N+1, 2*K+1);
%                 I(y>=-top&y<=bottom, x>=-left &x<=right, z>=-superior&z<=inferior)=1; 
%             end
            %position direction is LPS
            I = zeros(2*M+1, 2*N+1, 2*K+1);
            eps = 1e-5;
            if left==0; left=eps; end
            if right==0; right=eps; end
            if top==0; top=eps; end
            if bottom==0; bottom=eps; end
            if superior==0; superior=eps; end
            if inferior==0; inferior=eps; end
            %I = (X(X>=0)/left).^2 + (X(X<0)/right).^2 + (Y(Y>=0)/bottom).^2 + (Y(Y<0)/top).^2 +  (Z(Z>=0)/superior).^2 + (Z(Z<0)/inferior).^2;
            if numel(margin)==2 ||numel(margin)==4
                K = X>=0 & Y>=0;
                I(K) = (X(K)/left).^2+ (Y(K)/bottom).^2 <=1;
                
                K = X<0 & Y>=0;
                I(K) = (X(K)/right).^2+ (Y(K)/bottom).^2 <=1;
    
                K = X>=0 & Y<0;
                I(K) = (X(K)/left).^2+ (Y(K)/top).^2 <=1;
                
                K = X<0 & Y<0 ;
                I(K) = (X(K)/right).^2+ (Y(K)/top).^2 <=1;

            else
                K = X>=0 & Y>=0 & Z>=0;
                I(K) = (X(K)/left).^2+ (Y(K)/bottom).^2 +(Z(K)/superior).^2<=1;
                
                K = X<0 & Y>=0 & Z>=0;
                I(K) = (X(K)/right).^2+ (Y(K)/bottom).^2 +(Z(K)/superior).^2<=1;
    
                K = X>=0 & Y<0 & Z>=0;
                I(K) = (X(K)/left).^2+ (Y(K)/top).^2 +(Z(K)/superior).^2<=1;
                
                K = X<0 & Y<0 & Z>=0;
                I(K) = (X(K)/right).^2+ (Y(K)/top).^2 +(Z(K)/superior).^2<=1;
    
                K = X>=0 & Y>=0 & Z<0;
                I(K) = (X(K)/left).^2+ (Y(K)/bottom).^2 +(Z(K)/inferior).^2<=1;
                
                K = X<0 & Y>=0 & Z<0;
                I(K) = (X(K)/right).^2+ (Y(K)/bottom).^2 +(Z(K)/inferior).^2<=1;
    
                K = X>=0 & Y<0 & Z<0;
                I(K) = (X(K)/left).^2+ (Y(K)/top).^2 +(Z(K)/inferior).^2<=1;
                
                K = X<0 & Y<0 & Z<0;
                I(K) = (X(K)/right).^2+ (Y(K)/top).^2 +(Z(K)/inferior).^2<=1;
            end

            se= strel(I);
        end
        
        function se = strel2d_noniso(self, margin)
            margin = margin(:)';
            if numel(margin)==1
                margins = margin*ones(1, 4); 
            elseif numel(margin)==2
                margins = [margin(1) margin(1) margin(2) margin(2)];
            elseif numel(margin)==4
                margins = [margin];
            end
            
            %margins(margins==0)=Inf;
           

            left  = round(margins(1)/self.x_pixdim); 
            right = round(margins(2)/self.x_pixdim); 
            
            top = round(margins(3)/self.y_pixdim);
            bottom = round(margins(4)/self.y_pixdim);
 
            %left, right, top, bottom, superior, inferior;
            M = max(top, bottom); N = max(left, right); 
            x = -N:N; y = -M:M; 
            [X, Y] = meshgrid(x, y);

            I = zeros(2*M+1, 2*N+1);
            % if left==0; left=Inf; end
            % if right==0; right=Inf; end
            % if top==0; top=Inf; end
            % if bottom==0; bottom=Inf; end
            eps = 1e-5;
             if left==0; left=eps; end
             if right==0; right=eps; end
             if top==0; top=eps; end
             if bottom==0; bottom=eps; end
            if numel(margin)==2 ||numel(margin)==4
                K = X>=0 & Y>=0;
                I(K) = (X(K)/left).^2+ (Y(K)/bottom).^2 <=1;
                
                K = X<0 & Y>=0;
                I(K) = (X(K)/right).^2+ (Y(K)/bottom).^2 <=1;
    
                K = X>=0 & Y<0;
                I(K) = (X(K)/left).^2+ (Y(K)/top).^2 <=1;
                
                K = X<0 & Y<0 ;
                I(K) = (X(K)/right).^2+ (Y(K)/top).^2 <=1;
            end

            se= strel(I);
        end

    end
    
    methods (Static)
        function vh = ComposeVolHeader(orientation, pixdim, dcmcenter, dim, varargin)
            options = OptionsMap(varargin{:}); 
            vh = VolHeader; 
            vh.LinearMeasureUnit = options.getOption('LinearMeasureUnit', 'cm');
            vh.x_dim = dim(1); vh.y_dim = dim(2); vh.z_dim = dim(3);
            A = AffineMatrix4x4.ComposeAffineMatrix(orientation, pixdim, [0 0 0]);
            vh.ResetDicomAffineMatrix(A);
            vh.ResetCenterVoxelDcmCoor(dcmcenter);
        end

        function header = ToMhdHeader(fname, varargin)
            options = OptionsMap(varargin{:});
            obj = VolHeader(fname);
            [PATHSTR,NAME, EXT]    = fileparts(fname);
            header = struct( 'NDims',  3, ...
                            'BinaryData', 'True', ...
                            'BinaryDataByteOrderMSB', 'False', ...
                            'CompressedData', 'False', ...
                            'TransformMatrix', [1 0 0 0 1 0 0 0 1], ...
                            'Offset', [0 0 0], ...
                            'CenterOfRotation', [0 0 0], ...
                            'AnatomicalOrientation', 'RAI', ...
                            'ElementSpacing', [], ...
                            'DimSize', [], ...
                            'ElementType', 'MET_USHORT', ...
                            'ElementDataFile', [NAME '.img']);

            switch (upper(obj.data_type))
                case {'SHORT'  'INT16'}
                    header.ElementType = 'MET_SHORT'; 
                case {'USHORT'  'UINT16'}
                    header.ElementType = 'MET_USHORT';   
                case {'INT'  'INT32'}
                    header.ElementType = 'MET_INT';     
                case {'FLOAT'  'FLOAT32'}
                    header.ElementType = 'MET_FLOAT';    
                case {'DOUBLE'  'FLOAT64'}
                    header.ElementType = 'MET_DOUBLE';     
            end            

            obj.setLinearMeasureUnit('mm'); 

            flipY = options.getOption('FlipY', 0); 
            if flipY
                header.TransformMatrix = [1 0 0 0 -1 0 0 0 1];
            end

            prefix = 'xyz'; 
            for k=1:3
                header.DimSize(k)           =   obj.([prefix(k) '_dim']);
                header.ElementSpacing(k)    =   obj.([prefix(k) '_pixdim']);
                header.Offset(k)            =   obj.([prefix(k) '_start']);
            end
            writeStruct2File([PATHSTR,filesep NAME '.mhd'], header);
        end


        function res = DefaultVolumeImageTypes 
            res = {'ct', 'dose', 'density'};  
        end
        
        function res = DefaultImageHeaderExts 
            res = {'.header'};
        end
        
        function res = imageExts
            res ={'.img', '.sin', '.bin', '.raw', '.img.gz', '.bin.gz', '.raw.gz'}; 
        end
        
        function fn = LinearMeasureFieldNames
            fn = {'x_pixdim', 'y_pixdim', 'z_pixdim', 'x_start',  'y_start',  'z_start'}; 
        end
        
        function [orientation, planeMapping] = getSliceOrientation(self)
             if isempty(self.ImageOrientationPatient)
                 self.ImageOrientationPatient=[1 0 0 0 1 0];
             end
             [orientation, planeMapping] = VolHeader.CalcSliceOrientation(self.ImageOrientationPatient(1:6));
        end

        function dir = getImageAxesDir(patientPosition)
            if ~exist('patientPosition', 'var') || isempty(patientPosition)
                patientPosition='HFS';
            end
            switch patientPosition
                case 'HFS'
                    dir = {'R', 'L', 'A', 'P', 'S', 'I'};
                case 'FFS'
                    dir = {'L', 'R', 'A', 'P', 'I', 'S'};
                case 'HFP'
                    dir = {'L', 'R', 'P', 'A', 'S', 'I'};
                case 'FFP'
                    dir = {'R', 'L', 'P', 'A', 'I', 'S'};
            end
        end
        
        function showAxesPositionText(axH, strs, varargin)
            textPos = VolHeader.getAxesDirTextPosition(axH);
            %TAG = gettag; 
            figH = get(axH, 'parent');
            set(figH,'CurrentAxes',axH); 
            for k=1:length(strs)
                pos =  textPos{k}; str = strs{k}; 
                text(pos(1), pos(2), str, varargin{:}); 
            end
        end

        function textPos = getAxesDirTextPosition(axH)
            xlim = get(axH, 'XLim'); XRange = xlim(2)- xlim(1); Xmid = (xlim(2)+ xlim(1))/2; 
            ylim = get(axH, 'YLim'); YRange = ylim(2)- ylim(1); Ymid = (ylim(2)+ ylim(1))/2; 
            Left(1) = xlim(1)+0.1*XRange;     Right(1)  = xlim(1)+0.9*XRange; 
            Left(2) = Ymid;     Right(2) = Ymid; 
            Top(2)  = ylim(1)+0.1*YRange; Bottom(2) = ylim(1)+0.9*YRange; 
            Top(1) = Xmid; Bottom(1) = Xmid; 
            textPos = {Left, Right, Top, Bottom}; 
        end
        
        function flag = isMemberString(EXT, imageExts)
            flag = false; 
            for k=1:length(imageExts)
                if strcmpi(EXT, imageExts{k})
                    flag=true; 
                    break; 
                end
            end
        end
    end
end



