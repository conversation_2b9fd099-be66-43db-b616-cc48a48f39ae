classdef VolHeaderImage <VolHeader 
    %01/28/2015 add .mhd file I/O
    %Copyright Weiguo Lu  2014
    %

    properties (Constant)
        
    end
    
    properties
        data
    end
    

    methods
        function obj = VolHeaderImage(varargin)
            obj = obj@VolHeader(varargin{:}) ;
            if nargin >=1
                fname = varargin{1}; 
            else
                return
            end 
            
            if nargin <2
                dataFolder = [];
            else
                dataFolder = varargin{2}; 
            end
            
            if isstruct(fname) || isobject(fname)
                if isempty(dataFolder)
                    return;                  
                elseif ~isempty(dataFolder) && ~ischar(dataFolder)
                    obj.data = dataFolder; 
                    return; 
                else
                    obj.readData([dataFolder obj.uid]);
                    return; 
                end
            elseif ischar(fname)
                if ~isempty(regexpi(fname, ['(.nii$)|(.nii.gz$)']))
                    obj.readNiftiImage(fname); 
                    return;
                elseif ~isempty(regexpi(fname, ['.mhd']))
                    obj.readMhdFile(fname)
                elseif ~isempty(regexpi(fname, ['.mha']))
                    obj.readMhaFile(fname)
                elseif ~isempty(regexpi(fname, ['.nrrd']))
                    obj.readNrrdImage(fname)
                elseif ~isempty(regexpi(fname, ['.hdr']))
                    obj.readNiiHdrImage(fname)
                else
                    dataFileName = VolHeaderImage.getImageDataFile(fname, dataFolder); 
                    obj.readData(dataFileName);
                end
            end
        end
        
        function ZeroData(obj, targetType)
            if isempty(obj.data)
                obj.data = zeros([obj.y_dim, obj.x_dim, obj.z_dim], obj.data_type);
            else
                obj.data(:)=0; 
            end
            if nargin<2
                return
            end
            if isa(targetType, 'function_handle')
                obj.data = targetType(obj.data); % Use function handle directly
            elseif ischar(targetType) || isstring(targetType)
                obj.data = feval(targetType, obj.data); % Use feval for type conversion
            end
        end
        
        function SaveImage(obj, fname, ext, varargin)
            if ~exist('ext', 'var')
                ext = '.header';
            end
            switch lower(ext)
                case {'.header', ''}
                    obj.writeFile(fname);
                case {'.img.gz'}
                    obj.writeFile(fname, ext);
                case {'.mhd', '.mhd.gz'}
                    obj.writeMhdFile(fname, ext);
                case {'.nii'}
                    obj.writeNiftiImage(fname);
                case {'.nii.gz'}
                    obj.writeNiftiImage(fname, [],'Compressed', true );   
                    
            end
        end
        
        function writeFile(obj, fname, ext, varargin)
            if ~exist('ext', 'var') || isempty(ext)
                ext = '.img'; 
            end
            writeHeader(obj, [fname '.header'], varargin{:});
            writeRawData(obj, [fname ext]);
        end
        
        function writeHeader(obj, fname, varargin)
            header=obj.header; 
            header.byte_order = 0; 
            writeStruct2File(fname, header, varargin{:});
        end
        
        function count = writeRawData(obj, imgfname)
            imgfname0 = imgfname; 
            [~, name, ext] = fileparts(imgfname); 
            switch lower(ext)
                case {'.gz'}
                    imgfname = ['c:\temp\' name];
            end
            
            count = 0; 
            if ~isempty(obj.data)
                fid = fopen(imgfname, 'wb');
                count = fwrite(fid, permute(obj.data, [2 1 3]), obj.data_type);
                fclose(fid);
            end
            
            switch lower(ext)
                case {'.gz'}
                    gzip(imgfname);
                    movefile([imgfname ext], imgfname0); 
                    try
                        delete(imgfname); 
                    catch
                    end
            end
        end
        
        function header = readMhaFile(self,fname)
            header = readMhaHeader(self,fname);
            self.readMhaData(header);
        end
        
        function header = readMhaHeader(self,fname)
            header =  mha_read_header(fname); 
            self.LinearMeasureUnit='mm'; 
            self.ImageOrientationPatient=header.TransformMatrix;
            self.referenceImageIsocenter=header.CenterOfRotation;
            prefix = 'xyz'; 
            for k=1:3
                try
                self.([prefix(k) '_dim'])     = header.Dimensions(k); 
                self.([prefix(k) '_pixdim'])  = header.PixelDimensions(k);
                self.([prefix(k) '_start'])   = header.Offset(k);
                catch
                end
            end
        end
        
        function readMhaData(self,header)
            if ischar(header)
                header =  mha_read_header(header); 
            end
            switch (upper(header.DataType))
                case {'SHORT', 'USHORT'}
                    self.setShort(); 
                case 'FLOAT'
                    self.setFloat();
            end
            rawdata = mha_read_volume(header);
            rawdata = permute(rawdata, [2 1 3]); 
            self.setData(rawdata); 
        end
        
        function nii = readNiiHdrImage(self, fname)
            nii = load_nii(fname);
            self.setData(permute(nii.img, [2, 1, 3]));
            hdr = nii.hdr; 
            self.x_pixdim = hdr.dime.pixdim(3); 
            self.y_pixdim = hdr.dime.pixdim(2); 
            self.z_pixdim = hdr.dime.pixdim(4); 
            self.x_start = hdr.hist.originator(2); 
            self.y_start = hdr.hist.originator(1); 
            self.z_start = hdr.hist.originator(3); 
            self.LinearMeasureUnit='mm';
        end
        
        function hdr = readNiftiImage(self, fname)
            hdr = niftiinfo(fname);
            vol = niftiread(fname);
            
            if isfield(hdr, 'MultiplicativeScaling') && hdr.MultiplicativeScaling~=0 && hdr.MultiplicativeScaling~=1
                vol = double(vol) * hdr.MultiplicativeScaling;
                hdr.Datatype='single';
            end
            
            if isfield(hdr, 'AdditiveOffset') && hdr.AdditiveOffset~=0
                vol = double(vol) + hdr.AdditiveOffset;
            end
           
            vh = VolHeader.Nifti2VolHeader(hdr); 
            self.copyStruct(vh); 
            
%             if qfac==-1
%                 vol = flipdim(vol, 2); 
%             end
            %todo: 4D data, use its sum
            if size(vol, 4)>1
                %vol = sum(vol, 4);
                %vol = vol(:, :, :, end);
                vol = permute(vol, [2, 1, 3, 4]);
            else
                vol = permute(vol, [2, 1, 3]);
            end

            self.setData(vol);            
%             self.setShort(); 
%             self.alignXYZCenter;

            qfac = hdr.Qfactor; 
            if qfac==-1
                self.ReformImageOrientation('-QFAC');
            end
        end
        
        function writeNiftiImage(self, fname, hdr, varargin)
            options = OptionsMap(varargin{:});
            savefloatasint16 = options.getoptioni('SaveFloatAsInt16', 1);
            compressed = options.getoptioni('Compressed', true);
            %datatype = self.data_type;
            %origLinearMeasureUnit = self.LinearMeasureUnit;
            if ~exist('hdr', 'var') || isempty(hdr)
                 vh = VolHeaderImage(self);
                 qfac = options.getoptioni_numeric('qfac', 1);
                 MultiplicativeScaling = 1; 
                 if strcmpi(self.data_type, 'short')
                     vh.data_type = 'int16';
                 elseif savefloatasint16 && (strcmpi(self.data_type, 'float') || strcmpi(self.data_type, 'float32'))
                     %vh.data_type = 'single';
                     MultiplicativeScaling = max(vh.data(:)) / 32767;
                     vh.data = vh.data/MultiplicativeScaling;
                     vh.data_type = 'int16';
                 end            
                 
                 
                 
                 vh.setLinearMeasureUnit('mm');
                 if qfac==-1
                     if isa(self, 'ROIMaskImage')
                        interpmethod = 'nearest';
                     else
                        interpmethod = 'linear';
                     end
                     vh.ReformImageOrientation('-QFAC', {'interpmethod', interpmethod});
                 end
                 
                 pixdim  = [vh.x_pixdim vh.y_pixdim vh.z_pixdim];
                 %imgdata = permute(int16(self.data), [2 1 3]); 
                 imgdata = permute(vh.data, [2 1 3]); 
%                  if strcmpi(self.data_type, 'float') || strcmpi(self.data_type, 'float32')
%                      imgdata = cast(imgdata, 'single');
%                  else
                     imgdata = cast(imgdata, vh.data_type);
%                  end
                 %the nii file is saved in RAS orientation, flip to LPS
                 %orientation
                 T = vh.DicomAffineMatrix;
                 T(1:2, :) = -T(1:2, :);
                 T = T';
                 imgsize = size(imgdata); 
                 if numel(imgsize)==2
                     pixdim(3)=[];
                     T(:, 3)=[0 0 1 0]';
                 end

                 trans = struct('T', T, 'Dimensionality', 3); 
                 defaultversion = 1; 
                 if max(imgsize)>32767
                     defaultversion=2; 
                 end
                 version = options.getoptioni_numeric('nifti.version',  defaultversion); 
                 niftiversion = ['NIfTI' num2str(version)]; 

                 hdr = struct(...
                     'Version', niftiversion,...
                     'Description', '', ...
                     'Datatype',      vh.data_type, ...
                     'BitsPerPixel',  vh.bytes_pix*8, ...
                     'ImageSize', imgsize, ...
                     'PixelDimensions', pixdim,...  
                     'SpaceUnits', 'Millimeter',...
                     'TimeUnits', 'None',...
                     'Qfactor', qfac,...  
                     'AdditiveOffset', 0, ...
                     'MultiplicativeScaling', MultiplicativeScaling, ...
                     'TimeOffset',  0, ...
                     'SliceCode',  'Unknown', ...
                     'FrequencyDimension',  0, ...
                     'PhaseDimension',  0, ...
                     'SpatialDimension',  3, ...
                     'DisplayIntensityRange',  [0 0], ...
                     'TransformName', 'Sform', ...
                     'Transform', trans , ...
                     'AuxiliaryFile', '');
            else
                img = VolHeaderImage(self); 
                img.setLinearMeasureUnit('mm');
                refcs = VolHeader.Nifti2VolHeader(hdr);
                img.ReformCS(refcs); 
                imgdata = permute(cast(img.data, hdr.Datatype), [2 1 3]); 
            end
            
            %The following code  is to fix the bug in niftiwrite that extracts ext out from fname
%             [path, name origext] = fileparts(fname);
%             fname1 = [path, filesep, name];
%             niftiwrite(imgdata, fname1, hdr, 'Compressed', compressed); 
%             if ~isempty(origext)
%                 niiext = '.nii';
%                 if compressed
%                     niiext = '.nii.gz';
%                 end
%                 movefile([fname1 niiext], [fname1 origext niiext], 'f');
%             end
            
            %fname1 = 'c:\temp\niftitemp'; 
            fname1 = tempname; 
            niftiwrite(imgdata, fname1, hdr, 'Compressed', compressed); 
            niiext = '.nii';
            if compressed
               niiext = '.nii.gz';
            end
            movefile([fname1 niiext], [fname niiext], 'f');
        end
        
        function SafeWriteNiftiImage(img, fname)
            [path]=fileparts(fname);
            if ~exist(path, 'dir')
                DosUtil.mksubdir(path);
            end
            img.writeNiftiImage(fname);
        end

        %%.mhd file I/O
        function header = readMhdHeader(self, fname)
            if isstruct(fname)
                header = fname;
            else
                header = parseTextFile(fname);
            end

           self.FromMhdHeader(header);
%             element_types=struct('double','MET_DOUBLE','int8','MET_CHAR','uint8','MET_UCHAR','int16','MET_SHORT','uint16','MET_USHORT','int32','MET_INT','uint32','MET_UINT');
            
            try
                switch (upper(header.ElementType))
                    case 'MET_USHORT'
                        self.setShort(); 
                    case 'MET_SHORT'
                        self.setShort();     
                    case 'MET_INT' 
                         self.setInt();      
                    case 'MET_UINT' 
                         self.setInt(); 
                    case 'MET_LONG_LONG' 
                         self.setDataType('uint64');      
                    case 'MET_FLOAT'
                        self.setFloat();
                end
            catch
                switch (upper(header.DataType))
                    case 'SHORT'
                        self.setShort(); 
                    case 'FLOAT'
                        self.setFloat();
                end
            end
        end
        
        function readMhdFile(self, fname)
            header = self.readMhdHeader(fname);
            [PATHSTR,NAME,EXT] = fileparts(fname);
            if isempty(PATHSTR) PATHSTR = '.'; end
            datafile = [PATHSTR filesep header.ElementDataFile]; 
            self.readData(datafile); 
        end
        
        
        
        function header = writeMhdHeader(self, fname, varargin)
            options = OptionsMap(varargin{:});
%             self = VolHeaderImage(obj); 
            
            header = MhdHeader(self);
          
%             flipY = options.getOption('FlipY', 0); 
%             if flipY
%                 self.data = flipdim(self.data, 1);
%                 header.TransformMatrix = [1 0 0 0 -1 0 0 0 1];
%             end
            
            fn = fieldnames(header);
            for k=1:numel(fn)
                name = fn{k}; 
                if isnumeric(header.(name))
                    header.(name) = num2str(header.(name)); 
                end
            end
            
            [PATHSTR,NAME, EXT]    = fileparts(fname);
            rawext = options.getoptioni('rawdatafileext', '.raw'); 
            header.ElementDataFile = [NAME rawext]; 
            
            %writeStruct2File(fname , header, varargin{:});
            writeStruct2File(fname , header, 1);
        end
        
        function writeMhdFile(self, fname, varargin)
            header = writeMhdHeader(self, fname, varargin{:});
            [PATHSTR,NAME, EXT]    = fileparts(fname);
            writeRawData(self, [PATHSTR filesep header.ElementDataFile]);            
        end
        
        function readVtiFile(self, fname)
            tempfile = 'c:\temp\tempimagefromvti';
            delete([tempfile '.*']); 
%             exestr = 'C:\Source\matlab\repo-lu\Source\Common\model\vti2mhd.exe '; 
            exestr = [which('vti2mhd.exe') ' '];
            cmdline = [exestr ' "' fname '" ' tempfile];
            system(cmdline); 
            self.readMhdFile([tempfile '.mhd']);
        end
        
        function readNrrdImage(self, fname)
            [X, meta] = imio.nrrdread(fname); 
            
            self.setData(X); 
            A = regexp(meta.spaceorigin,['\d+\.?\d*'],'match'); A = cellfun(@(x)(str2double(x)), A); 
            self.x_start = A(1); self.y_start = A(2); self.z_start = A(3); 
            A = regexp(meta.spacedirections,['\d+\.?\d*'],'match'); A = cellfun(@(x)(str2double(x)), A); 
            self.x_pixdim = A(1); self.y_pixdim = A(5); self.z_pixdim = A(9); 
            self.data_type = meta.type; 
            self.LinearMeasureUnit='mm'; 
        end
        
        function readDicomRTDose(obj, varargin)
            if ischar(varargin{1})
                fname = varargin{1};
                info=dicominfo(fname);
                dose=dicomread(fname);
            else
                info=varargin{1};
                dose=dicomread(info.Filename); 
            end
            
            obj.data=double(squeeze(dose))*info.DoseGridScaling;
            obj.x_dim=size(obj.data, 2); 
            obj.y_dim=size(obj.data, 1);
            obj.z_dim=size(obj.data, 3);
            obj.x_pixdim = info.PixelSpacing(1)/10; 
            obj.y_pixdim = info.PixelSpacing(2)/10; 
            if ~isempty(info.SliceThickness)
                obj.z_pixdim = info.SliceThickness/10;
            else
                obj.z_pixdim=obj.x_pixdim;
            end
            obj.reverseY=false; 
            obj.setFloat;
        end
        

        function read3dDose(self, fname)
            [data, header, err] = readDOSEXYZ_3ddose(fname, 0);
            self.copy(header); 
            self.setData(data); 
        end
        
        function obj=setData(obj, data)
            obj.data = data; 
            obj.x_dim = size(data, 2); 
            obj.y_dim = size(data, 1); 
            obj.z_dim = size(data, 3); 
        end
        
        function setUniformData(obj, val)
            obj.data(:)=val; 
        end
        
        function dst = getSliceImage(obj, slice, dir)
            if ~exist('dir', 'var')
                dir = 'z'; 
            end
            if isa(obj, 'ROIMaskImage')
                dst = ROIMaskImage(obj.header); 
            else
                dst = VolHeaderImage(obj.header); 
            end
            switch lower(dir)
                case {'z', 't', 'axial', 'transverse'}
                    if ~exist('slice', 'var') ||isempty(slice)
                        slice = round(obj.z_dim/2); 
                    end
                    dst.setData(obj.data(:, :, slice));
                    dst.z_start = obj.z_start+(slice-1)*obj.z_pixdim; 
                case {'y', 'c', 'coronal'}
                    if ~exist('slice', 'var') ||isempty(slice)
                        slice = round(obj.y_dim/2); 
                    end
                    dst.setData(squeeze(obj.data(slice, :, :))');
                    dst.y_dim = obj.z_dim;
                    dst.y_pixdim = obj.z_pixdim;
                    dst.y_start = obj.z_start;
                 case {'x', 's', 'sagittal'}
                    if ~exist('slice', 'var') ||isempty(slice)
                        slice = round(obj.x_dim/2); 
                    end
                    dst.setData(squeeze(obj.data(:,slice, :))); 
                    dst.x_dim = obj.z_dim;
                    dst.x_pixdim = obj.z_pixdim;
                    dst.x_start = obj.z_start;
            end
        end
        
        function dst = getMIPImage(obj, dir)
            if ~exist('dir', 'var')
                dir = 'z'; 
            end
            
            dst = VolHeaderImage(obj.header); 
            switch lower(dir)
                case {'z', 't', 'axial'}
                    dst.setData(max(obj.data,[], 3));
                case {'y', 'c', 'coronal'}
                    dst.setData(squeeze(max(obj.data,[], 1))');
                    dst.y_dim = obj.z_dim;
                    dst.y_pixdim = obj.z_pixdim;
                    dst.y_start = obj.z_start;
                case {'x', 's', 'sagittal'}
                    dst.setData(squeeze(max(obj.data,[], 2))); 
                    dst.x_dim = obj.z_dim;
                    dst.x_pixdim = obj.z_pixdim;
                    dst.x_start = obj.z_start;
            end
        end
        
        function dst = getProjectionImage(obj, dir)
            if ~exist('dir', 'var')
                dir = 'z'; 
            end
            
            dst = VolHeaderImage(obj.header); 
            switch lower(dir)
                case {'z', 't', 'axial'}
                    dst.setData(sum(obj.data, 3));
                case {'y', 'c', 'coronal'}
                    dst.setData(squeeze(sum(obj.data, 1))');
                    dst.y_dim = obj.z_dim;
                    dst.y_pixdim = obj.z_pixdim;
                    dst.y_start = obj.z_start;
                case {'x', 's', 'sagittal'}
                    dst.setData(squeeze(sum(obj.data, 2))); 
                    dst.x_dim = obj.z_dim;
                    dst.x_pixdim = obj.z_pixdim;
                    dst.x_start = obj.z_start;
            end
        end
        
        %to be retired
        function resize(obj, varargin)
            options = OptionsMap(varargin{:});
            pixdim = options.getoptioni_numeric('resizepixdim');
            
            if isempty(pixdim)
                resizedim = options.getoptioni_numeric('resizedim'); 
                if numel(resizedim)<1
                    resizedim(1) =obj.x_dim; 
                end
                if numel(resizedim)<2
                    resizedim(2) =obj.y_dim; 
                end
                if numel(resizedim)<3
                    resizedim(3) =obj.z_dim; 
                end

                resizefactor = options.getoptioni_numeric('resizefactor', resizedim./[obj.x_dim, obj.y_dim, obj.z_dim]); 
                if numel(resizefactor)==2
                    resizefactor = [resizefactor 1]; 
                elseif numel(resizefactor)==1
                    resizefactor = [resizefactor resizefactor 1]; 
                end

                if all(resizefactor==1)
                    return;
                end
                pixdim = [obj.x_pixdim obj.y_pixdim  obj.z_pixdim]./resizefactor;            
            end
            
            if numel(pixdim)<2
                pixdim(2) = pixeldim(1);
            end
            
            if numel(pixdim)<3
                pixdim(3) = obj.z_pixdim;
            end
             
            interpmethod = options.getoptioni('interpmethod', 'linear');
            obj.resample(pixdim, interpmethod); 
        end
        
        function Resize(obj, dstsize, varargin)
            dims = size(obj.data);
            if all(dims==dstsize)
                return;
            end
            
            dstcs = VolHeader(obj);
            dstcs.x_dim = dstsize(2); dstcs.x_pixdim = obj.x_pixdim*dims(2)/dstsize(2); 
            dstcs.y_dim = dstsize(1); dstcs.y_pixdim = obj.y_pixdim*dims(1)/dstsize(1); 
            dstcs.z_dim = dstsize(3); dstcs.z_pixdim = obj.z_pixdim*dims(3)/dstsize(3); 

            obj.ReformCS(dstcs, varargin{:});
        end

        function DownsampleImage(obj, factors)
            if obj.z_dim>1
                newdata = obj.data(1:factors(2):end, 1:factors(1):end, 1:factors(3):end);
                obj.setData(newdata);
                obj.x_start = obj.x_start +0.5*(factors(1)-1)*obj.x_pixdim;
                obj.y_start = obj.y_start +0.5*(factors(2)-1)*obj.y_pixdim;
                obj.z_start = obj.z_start +0.5*(factors(3)-1)*obj.z_pixdim;
                  
                obj.x_pixdim = obj.x_pixdim*factors(1);
                obj.y_pixdim = obj.y_pixdim*factors(2);
                obj.z_pixdim = obj.z_pixdim*factors(3);
            else
                newdata = obj.data(1:factors(2):end, 1:factors(1):end);
                obj.setData(newdata);
                obj.x_start = obj.x_start +0.5*(factors(1)-1)*obj.x_pixdim;
                obj.y_start = obj.y_start +0.5*(factors(2)-1)*obj.y_pixdim;
                
                obj.x_pixdim = obj.x_pixdim*factors(1);
                obj.y_pixdim = obj.y_pixdim*factors(2);
            end
        end
        
        function downsample(obj, factor)
            pixdim(1) = obj.x_pixdim*factor(1); 
            pixdim(2) = obj.y_pixdim*factor(2);
            pixdim(3) = obj.z_pixdim*factor(3);
            kernel = ones(round(factor)); kernel = kernel/numel(kernel);
            obj.data = convn(obj.data, kernel, 'same'); 
    
            dstHeader = VolHeader(obj.header);
            dstHeader.x_dim = round(dstHeader.x_dim/factor(1)); 
            dstHeader.x_pixdim = pixdim(1); 
            dstHeader.y_dim = round(dstHeader.y_dim/factor(2));  
            dstHeader.y_pixdim = pixdim(2); 
            dstHeader.z_dim = round(dstHeader.z_dim/factor(3));   
            dstHeader.z_pixdim = pixdim(3);  
            obj.reform(dstHeader); 
        end
        
        function resample(obj, pixdim, varargin)
            N = length(pixdim);
            if N<2
                pixdim(2) = obj.y_pixdim; 
                pixdim(3) = obj.z_pixdim; 
            elseif N<3
                pixdim(3) = obj.z_pixdim; 
            end
            
            if obj.x_pixdim==pixdim(1) && obj.y_pixdim==pixdim(2) && obj.z_pixdim==pixdim(3)
                return; 
            end
            dstHeader = VolHeader(obj.header);
            dstHeader.x_dim = round(dstHeader.x_dim*obj.x_pixdim/pixdim(1)); 
            %dstHeader.x_dim = ceil(dstHeader.x_dim/4)*4; %make it multiple of 4
            dstHeader.x_pixdim = pixdim(1); 
            dstHeader.alignToDstCenter(obj, 'x'); 
            
            dstHeader.y_dim = round(dstHeader.y_dim*obj.y_pixdim/pixdim(2)); 
            %dstHeader.y_dim = ceil(dstHeader.y_dim/4)*4; %make it multiple of 4
            dstHeader.y_pixdim = pixdim(2); 
            dstHeader.alignToDstCenter(obj, 'y'); 
            
            if obj.z_dim>1 %handle 2D case
            dstHeader.z_dim = round(dstHeader.z_dim*obj.z_pixdim/pixdim(3));  
            dstHeader.z_pixdim = pixdim(3);  
            dstHeader.alignToDstCenter(obj, 'z');
            end
            
            obj.reform(dstHeader,  varargin{:});
        end
        
        function reform2d(obj, dstHeader2d, varargin)
            numslices = obj.z_dim;        
            data     = zeros(dstHeader2d.y_dim, dstHeader2d.x_dim, numslices);
            dstImage = wtk.util.hackclone(dstHeader2d); 
            dstImage.setData(data); 
            dstImage.z_start = obj.z_start; 
            dstImage.z_pixdim = obj.z_pixdim; 
            obj.reform(dstImage, varargin{:}); 
        end
        
        function reform(obj, dstHeader, interpMethod, toMerge)
            if isEqualDim(obj, dstHeader)
                return;
            end

            if isfield(dstHeader, 'LinearMeasureUnit')
                obj.setLinearMeasureUnit(dstHeader.LinearMeasureUnit); 
            end

            if ~exist('toMerge', 'var')
                toMerge = 0; 
            end

            if ~exist('interpMethod', 'var')
                interpMethod = obj.DefaultInterpMethod(); 
            end

%             if isfield(obj, 'translation') 
%                 obj.x_start = obj.x_start+obj.translation(1);
%                 obj.y_start = obj.y_start+obj.translation(2);
%                 obj.z_start = obj.z_start+obj.translation(3);
%             end

            x = obj.xData;                 y = obj.yData;                 z = obj.zData; 
            xi = dstHeader.xData;          yi = dstHeader.yData;          zi = dstHeader.zData; 
            I = find(xi>=min(x) & xi<=max(x)); 
            J = find(yi>=min(y) & yi<=max(y)); 
            K = find(zi>=min(z) & zi<=max(z)); 
            xi = xi(I); yi = yi(J); zi=zi(K); 
            if obj.z_dim >1
            [X, Y, Z] = meshgrid(x, y, z); 
            [Xi, Yi, Zi] = meshgrid(xi, yi, zi);
            else
                [X, Y] = meshgrid(x, y); 
                [Xi, Yi] = meshgrid(xi, yi);
            end

%             if isfield(obj, 'rotation') && abs(obj.rotation(3))>=0.5
%                 angle = obj.rotation(3)*pi/180;              
%                 if obj.reverseY
%                     angle = -angle; 
%                 end
%                 cosAngle = cos(angle); 
%                 sinAngle = sin(angle); 
%                 xc = dstHeader.x_center;
%                 yc = dstHeader.y_center;
%                 Xi = xc + (Xi-xc)*cosAngle + (Yi-yc)*sinAngle; 
%                 Yi = yc - (Xi-xc)*sinAngle + (Yi-yc)*cosAngle;
%             end

            if ~toMerge
                dstData = zeros(dstHeader.y_dim, dstHeader.x_dim, dstHeader.z_dim); 
            else
                dstData = double(dstHeader.data);
            end

            if obj.z_dim >1
                if ~isempty(J) &&  ~isempty(I) &&  ~isempty(K)
                dstData(J, I, K) = interp3(X, Y, Z, double(obj.data), Xi, Yi, Zi, interpMethod, 0); 
                end
            else
                if ~isempty(J) &&  ~isempty(I) 
                    if numel(J)==1
                        dstData(J, I) = interp1(X, double(obj.data), Xi, interpMethod, 0); 
                    elseif numel(I)==1
                        dstData(J, I) = interp1(Y, double(obj.data), Yi, interpMethod, 0); 
                    else
                        dstData(J, I) = interp2(X, Y, double(obj.data), Xi, Yi, interpMethod, 0); 
                    end
                end
            end

            obj.setData(dstData); 
%             obj.copyStruct(dstHeader.header(fieldnames(VolHeader))); 
            dirs = 'xyz';
            for k=1:3
                dir = dirs(k);
                obj.([dir '_start']) = dstHeader.([dir '_start']);
                obj.([dir '_pixdim']) = dstHeader.([dir '_pixdim']);
            end
        end
        
        function mergeTo(obj, dstImage, translation, varargin)
%             if exist('translation', 'var')
%                 obj.translation = translation; 
%             end
            obj.x_start = obj.x_start+translation(1);
            obj.y_start = obj.y_start+translation(2);
            obj.z_start = obj.z_start+translation(3);
            
            options = OptionsMap(varargin{:});
            
            interpmethod = options.getoptioni('interpolation', obj.DefaultInterpMethod());
            
            obj.reform(dstImage, interpmethod, 1); 
        end
        
        function alignTo(obj, dstImage, srcCenter, dstCenter, varargin)
            if ~exist('dstCenter', 'var') || isempty(dstCenter)
                dstCenter=dstImage.coor_center; 
            end
            if ~exist('srcCenter', 'var') || isempty(srcCenter)
                srcCenter=obj.coor_center; 
            end
            translation = dstCenter -srcCenter ; 
            obj.x_start = obj.x_start+translation(1);
            obj.y_start = obj.y_start+translation(2);
            obj.z_start = obj.z_start+translation(3);
            options = OptionsMap(varargin{:});
            interpMethod = options.getOptioni('interpolationmethod', obj.DefaultInterpMethod()); 
            obj.reform(dstImage, interpMethod, 0); 
        end
        
        function imgs = SplitVolume_z(obj, maxslices, varargin)
            options = OptionsMap(varargin{:});
            overlap = options.getoptioni_numeric('Overlap', 0);
            zdim = obj.z_dim; 
            numsplits = ceil(zdim/maxslices);
            if numsplits==1
                imgs = VolHeaderImage(obj); 
                return
            end
            
            if ~overlap
               dimz   = ceil(zdim/numsplits);
               slice0=1; 
               for k=1:numsplits
                    Dim{k} = [obj.x_dim, obj.y_dim dimz];
                    S{k}   = [1 1 slice0];
                    slice0 = slice0+dimz; 
               end
            else
               %dimz   = ceil(zdim/numsplits);
               slice0 = 1; 
               offset = round((maxslices*numsplits-zdim)/(numsplits-1));
               for k=1:numsplits
                    Dim{k} = [obj.x_dim, obj.y_dim maxslices];
                    S{k}   = [1 1 slice0];
                    slice0 = slice0+maxslices-offset; 
               end
            end
            for k=1:numsplits
                imgs(k) = VolHeaderImage(obj);
                imgs(k).CropBoundingBox(S{k}, Dim{k});
            end
        end
        
        function ReformCS(self, refcs, varargin)
            options   = OptionsMap(varargin{:});
            if isempty(refcs)
              cfg = options.getOption('ReformCSCfg');
              if ~isempty(cfg)
                refcs = self.CreateReformVolHeader(cfg);
              else
                refcs = ComposeReformCS(self, options);
              end
            end

            self.setLinearMeasureUnit(refcs.LinearMeasureUnit);
            
            if self.isEqualCS(refcs)
                return; 
            end
            
            
              A = refcs.DicomAffineMatrix();
              B = self.DicomAffineMatrix();
              C = inv(B)*A; C = C'; 
%             C(:, 4) = [0 0 0 1]'; %enforce 

        %     dstimage = VolHeaderImage.TransformImage(movingimage, C, fixedimage); 
            [Xa, Ya, Za] = meshgrid(1:refcs.x_dim, 1:refcs.y_dim, 1:refcs.z_dim); 
            
            N = numel(Xa);
            
%             res = [Xa(:) Ya(:) Za(:) ones(N, 1)]*C; 
            res = [Xa(:)-1 Ya(:)-1 Za(:)-1 ones(N, 1)]*C; 
            res = res+1; %start with index 1
               
            
            
            interpmethod = options.getoptioni('interpmethod', self.DefaultInterpMethod()); 
            
            extrapval = options.getoptioni('extrapval', 0); 
            if self.z_dim>1
                % Data = interp3(double(self.data), res(:, 1), res(:, 2), res(:,3), interpmethod, extrapval); 
                if isa(self, 'ROIMaskImage')
                   %Data = VolHeaderImage.iminterpn(self.data, res(:, 2), res(:, 1), res(:,3), interpmethod, extrapval);
                   X=round(res(:,1)); Y=round(res(:,2)); Z=round(res(:,3));
                   I=X>=1&X<=self.x_dim&Y>=1&Y<=self.y_dim&Z>=1&Z<=self.z_dim;
                   index = self.imageCoor2index([X(I) Y(I) Z(I)]);
                   Data(I)= self.data(index);
                   Data(~I)=0;
                else
                   Data = interp3(double(self.data), res(:, 1), res(:, 2), res(:,3), interpmethod, extrapval); 
                end
            elseif self.y_dim>1
                Data = interp2(double(self.data), res(:, 1), res(:, 2), interpmethod, extrapval); 
            end
            self.setData(reshape(Data, [refcs.y_dim, refcs.x_dim, refcs.z_dim]));
            self.x_pixdim = refcs.x_pixdim;  self.y_pixdim = refcs.y_pixdim;   self.z_pixdim = refcs.z_pixdim; 
            self.x_start  = refcs.x_start;  self.y_start = refcs.y_start;   self.z_start = refcs.z_start; 
            self.ImageOrientationPatient = refcs.ImageOrientationPatient;
            self.TransformMatrix = refcs.TransformMatrix; 
        end
        
        %C is the left multiply transform matrix defined in image
        %coordinate system
%         function ApplyAffineTransform(self, C, varargin)
%             options = OptionsMap(varargin{:});
%             
%             [Xa, Ya, Za] = meshgrid(1:self.x_dim, 1:self.y_dim, 1:self.z_dim); 
%             
%             N = numel(Xa);
%             
%             res = [Xa(:) Ya(:) Za(:) ones(N, 1)]*C; 
% 
%             interpmethod = options.getoptioni('interpmethod', 'linear'); 
%             
%             extrapval = options.getoptioni('extrapval', 0); 
%             
%             Data = interp3(self.data, res(:, 1), res(:, 2), res(:,3), interpmethod, extrapval); 
%             
%             self.setData(reshape(Data, [self.y_dim, self.x_dim, self.z_dim]));
%         end
        
        %the fixed CS has R=eye(3); 
        function flag = ReformImageOrientation(self, orientation, varargin)
            if nargin<2
                orientation = [1 0 0 0 1 0 0 0 1];
            end
            if ischar(orientation)
                switch upper(orientation)
                    case {'LPS'}
                        orientation = [1 0 0 0 1 0 0 0 1];
                    case {'RAS'}
                        orientation = [-1 0 0 0 -1 0 0 0 1];
                    case {'-QFAC'}
                        orientation = reshape(self.ImageOrientationPatient, 3, []); 
                        if size(orientation, 2)==2
                            orientation(:, 3) = cross(orientation(:, 1), orientation(:, 2));
                        end
                        orientation(:, 2) = -orientation(:, 2);
                        orientation = orientation(:)';
                    
                    case {'HFS', 'LPS-HFS'}
                        orientation = [1 0 0 0 1 0 0 0 1];
                    case {'FFS', 'LPS-FFS'}
                        orientation = [-1 0 0 0 1 0 0 0 -1];
                    case {'FFP', 'LPS-FFP'}
                        orientation = [1 0 0 0 -1 0 0 0 -1];  
                    case {'HFP', 'LPS-HFP'}
                        orientation = [-1 0 0 0 -1 0 0 0 1];    
                    otherwise
                        disp(['warning: ReformImageOrientation() orientation ' orientation ' not defined'])
                        flag =0; return;
                end
            end
            
%             orientation0 = self.ImageOrientationPatient;
%             if numel(orientation0)==6
%                 orientation0 = reshape(orientation0, 3, 2); 
%                 orientation0(:, 3) = cross(orientation0(:, 1), orientation0(:, 2)); 
%                 orientation0=orientation0(:)'; 
%             end
%             
%             if numel(orientation0) == numel(orientation) && max(abs(orientation0-orientation))<1e-5
%                 flag = 0; 
%                 return;
%             end
            orientation0 = self.ImageOrientationPatientMatrix;
            if all(abs(orientation(:)-orientation0(:))<1e-5)
                flag = 0; 
                return;
            end
            if 1
            R = reshape(orientation(:), 3, 3); 
            imagCoor = ([self.x_dim  self.y_dim self.z_dim]')/2+0.5;
            center   = self.Image2DicomCoor(imagCoor);
            %start    = center - R*imagCoor.*[self.x_pixdim  self.y_pixdim self.z_pixdim]';
            start    = center - R*(imagCoor-1).*[self.x_pixdim  self.y_pixdim self.z_pixdim]';
            refcs    = VolHeader(self); 
            refcs.x_start = start(1); refcs.y_start = start(2); refcs.z_start = start(3); 
            refcs.TransformMatrix=[]; 
            refcs.ImageOrientationPatient = orientation(:)'; 
            self.ReformCS(refcs, varargin{:});
            else %todo: make following code work
                R      = reshape(orientation(:), 3, 3); 
                pixdim = [self.x_pixdim, self.y_pixdim, self.z_pixdim]';
                dim    = [self.x_dim, self.y_dim, self.z_dim]';
                physicsdim = pixdim.*dim; 
                
                %pixdim = R*pixdim;
                physicsdim=R*physicsdim;
                center = self.dcmcoor_center; 
                start  = center-physicsdim/2;
                
                pixdim = abs(pixdim); physicsdim=abs(physicsdim);
                dim = round(physicsdim./pixdim);
                
                refcs  = VolHeader(self); refcs.TransformMatrix=[]; 
                refcs.ImageOrientationPatient = orientation(:)'; 
                refcs.x_start = start(1); refcs.y_start = start(2); refcs.z_start = start(3); 
                refcs.x_pixdim = pixdim(1); refcs.y_pixdim =  pixdim(2); refcs.z_pixdim =  pixdim(3);
                refcs.x_dim = dim(1); refcs.y_dim =  dim(2); refcs.z_dim = dim(3);
                self.ReformCS(refcs, varargin{:});
            end
            flag = 1; 
        end
        
        
        function readData(obj, fname)
            if ~exist(fname, 'file')
                for k=1:length(obj.imageExts)
                    ext = obj.imageExts{k}; 
                    if exist([fname ext], 'file')
                        fname = [fname ext];
                        break; 
                    end
                end
            end
            
            readRawData(obj, fname);
        end
        
        function readRawData(obj, fname)
            [~, name, ext] = fileparts(fname); 
            tempfolder = tempname;
            switch lower(ext)
                case {'.gz'}
                    gunzip(fname, tempfolder); 
                    fname = [tempfolder filesep name];
            end
            
            fid = fopen(fname, 'rb', obj.machineFormat); 
            A = fread(fid, obj.data_type);
            fclose(fid);
            A = reshape(A, [obj.x_dim, obj.y_dim, obj.z_dim]);
            obj.setData(permute(A, [2 1 3])); 
            
            try
                if exist(tempfolder, 'folder')
                    rmdir(tempfolder, 's');
                end
            catch
            end
        end
        
        
        function show(obj, win)
            if ~exist('win', 'var')
                win=[0, max(obj.data(:))];
            end
            if isvector(obj.data)
                plot(obj.xData, obj.data); grid on; 
            elseif size(obj.data, 3)==1
                imshow(obj.data, win);
            end
        end
        
        function viewer =imview(obj, varargin)
%             imsegViewer({'imag1', obj}, varargin{:}); 
%             config=HierarchyCalculator.parseOptions(varargin);
%             config.imag1=obj; 
%             imsegViewer(config); 
            % options = OptionsMap(varargin{:});
            % fns = {'windowLow', 'windowHigh', 'overlapWeight', 'overlapScale', 'bw', 'slice1', 'slice2', 'slice3', 'regionbox'}; 
            % fns1= {'xLim1', 'yLim2','xLim2', 'yLim1','xLim3', 'yLim3' };
            % fns = cat(2, fns, fns1);
            % states=[];
            % for k=1:numel(fns)
            %     name = fns{k};
            %     if options.isOption(name)
            %         states.(name) = options.getOption(name);
            %         options.removeOption(name);
            %     end
            % end
            % viewer = imsegViewer({'imag1', obj},{'states', states},varargin{:}); 
            
            options = OptionsMap({'imag1', obj},varargin{:});
            boundingbox = options.getOption('BoundingBox');
            if numel(boundingbox)==6
                xlim = boundingbox(1)+[0 boundingbox(4)];
                ylim = boundingbox(2)+[0 boundingbox(5)];
                zlim = boundingbox(3)+[0 boundingbox(6)];
                %ylim = obj.y_dim-ylim; ylim = ylim([2 1]);
                options.setUndefinedOptions({'xLim1', xlim}, {'yLim1', ylim},{'xLim2', zlim},{'yLim2', xlim},{'xLim3', zlim},{'yLim3', ylim});
            end
            viewer = imsegViewer(options);
        end
        
        function viewer =imview_labelmask(self, labelmask, varargin)
            I = round(self.y_dim/2); 
            J = round(self.x_dim/2);
            K = round(self.z_dim/2);
            try
                c = labelmask.CenterOfMass(1);
                if ~any(isnan(c))
                    ic= round(self.Dicom2ImageCoor(c(:)));
                    I = ic(2); J = ic(1); K = ic(3); 
                end
            catch
            end
            options    = OptionsMap(varargin{:});
            labels = unique(labelmask.data(:));
            labels(labels==0)=[];
            labels=labels(:)';
            if numel(labels)==1
                labels = [labels labels];
            end
            overlapScale = options.getoptioni('overlapScale', labels);
            % %states = OptionsMap; 
            % %states.setOptions({'slice1',K},{'slice2',I},{'slice3',J},  {'bw', 1}, {'overlapScale',  overlapScale})
            
            
            % states=struct('slice1',K,'slice2',I,'slice3',J);
            % 
            % %fns = fieldnames(states);
            % fns = {'windowLow', 'windowHigh', 'overlapWeight', 'overlapScale', 'bw', 'slice1', 'slice2', 'slice3', 'regionbox'}; 
            % for k=1:numel(fns)
            %     name = fns{k};
            %     if options.isOption(name)
            %         states.(name) = options.getOption(name);
            %     end
            % end
            % 
            % ascontour = options.getoptioni('LabelMaskAsContour', 0);
            % if ascontour
            %     viewer =self.imview({'roi1', labelmask}, {'states', states}, {'imag2',labelmask}, varargin{:});
            % else
            %     states.('overlapScale')=  overlapScale;
            %     states.('bw')=1;
            %     viewer =self.imview({'states', states}, {'imag2',labelmask}, varargin{:});
            % end

            options.setOptions({'imag2',labelmask});
            options.setUndefinedOptions({'slice1',K},{'slice2',I},{'slice3',J});
            ascontour = options.getoptioni('LabelMaskAsContour', 0);
            if ascontour
                options.setOptions({'roi1', labelmask});
            else
                options.setOptions({'overlapScale',overlapScale}, {'bw', 1});
            end

            viewer =self.imview(options);
            %self.imview( {'imag2',dose}, states, varargin{:});
        end
        
        function viewer =imview_dose(self, dose, varargin)
            options    = OptionsMap(varargin{:});
            
            I = round(self.y_dim/2); 
            J = round(self.x_dim/2);
            K = round(self.z_dim/2);
            
            viewercenter = options.getOption('ViewerCenter', 'COM-dose');
            switch viewercenter
                case {'COM-dose'}
                    try
                        c = dose.CenterOfMass(1);
                        if ~any(isnan(c))
                            ic= round(self.Dicom2ImageCoor(c(:)));
                            I = ic(2); J = ic(1); K = ic(3); 
                        end
                    catch
                    end
                
                otherwise %ImageCenter
                    
            end
            
            refdoseval = options.getoptioni('RefDoseVal', 0.9*max(dose.data(:)));
            overlapScale = options.getoptioni('overlapScale', refdoseval*[1 :-0.1:0.1]);
            % %states = OptionsMap; 
            % %states.setOptions({'slice1',K},{'slice2',I},{'slice3',J},  {'bw', 1}, {'overlapScale',  overlapScale})
            % states=struct('slice1',K,'slice2',I,'slice3',J,  'bw', 1, 'overlapScale',  overlapScale);
            % %fns = fieldnames(states);
            % fns = {'windowLow', 'windowHigh', 'overlapWeight', 'overlapScale', 'bw', 'slice1', 'slice2', 'slice3', 'regionbox'}; 
            % fns1= {'xLim1', 'yLim2','xLim2', 'yLim1','xLim3', 'yLim3' };
            % fns = cat(2, fns, fns1);
            % for k=1:numel(fns)
            %     name = fns{k};
            %     if options.isOption(name)
            %         states.(name) = options.getOption(name);
            %     end
            % end
            % viewer =self.imview({'states', states}, {'imag2',dose}, varargin{:});
            % %self.imview( {'imag2',dose}, states, varargin{:});
            
            
            %options.setOptions({'imag2',dose}, {'slice1',K}, {'slice2',I},{'slice3',J},  {'bw', 1}, {'overlapScale',  overlapScale});
            options.setUndefinedOptions({'slice1',K}, {'slice2',I},{'slice3',J},  {'bw', 1}, {'overlapScale',  overlapScale});
            viewer =self.imview({'imag2',dose},  options);
        end

        function showSlice(obj, win, dir, slice, delta, transpose, units)
            if nargin <5
                delta = 5; 
            end
            if nargin <4
                slice = []; 
            end
            if nargin <3
                dir = 't'; 
            end
            
            if nargin <6
                transpose = false; 
            end
            
            if nargin <7
                units = {obj.LinearMeasureUnit obj.LinearMeasureUnit}; 
            end
            
            c = round(size(obj.data)/2); 
            switch(lower(dir))
                case {'transverse',  't'}
                    if isempty(slice)
                        slice = c(3); 
                    end
                    img = obj.data(:, :, slice);
%                     coor = [1 2 3]; 
                    LABEL = {'Y', 'X', 'Z'}; 
                case {'sagittal',  's'}
                    if isempty(slice)
                        slice = c(2); 
                    end
                    img = squeeze(obj.data(:, slice, :));
%                     coor = [1 3 2]; 
                    LABEL = {'Y', 'Z', 'X'}; 
                case {'coronal',  'c'}
                    if isempty(slice)
                        slice = c(1); 
                    end
                    img = squeeze(obj.data(slice,:, :));
%                     coor = [2 3 1]; 
                    LABEL = {'X', 'Z', 'Y'}; 
            end
            
%             dim0 = obj.dim; 
%             startPos0 = obj.startPos; 
%             pixdim0 = obj.pixelDim;
            
%             dim = dim0(coor); 
%             startPos = startPos0(coor); 
%             pixdim = pixdim0(coor); 
            %dataAspectRatio = [1./pixdim(coor)];
%             dataAspectRatio = pixdim;
            
            for n=1:3
                dataAspectRatio(n) = obj.([lower(LABEL{n}) '_pixdim']); 
            end
            
            for k=1:2
                %Delta = delta/pixdim(k);
                %ip = (1:Delta:dim(k)) -1;
                %tick0{k}= round((startPos(k) + ip*pixdim(k))/delta)*delta;
%                 Delta = delta/pixdim(k);
%                 ip = (1:Delta:dim(k));
%                 xdata = obj.image2physics(ip, LABEL{k}); 
%                 tick0{k} = round(xdata/delta)*delta; 
%                 ticklabel{k} = num2str(tick0{k}'); 
%                 %tick{k} = 1 +  (tick0{k} - startPos(k))/pixdim(k); 
%                 tick{k} = obj.physics2image(tick0{k}, LABEL{k}); 
                  [tick{k}, ticklabel{k}] = obj.getAxesTick(LABEL{k}, delta);
            end
            
            dirs = [1 2 3]; 
            
            if transpose
                img = img'; 
                dirs = [2 1 3]; 
            end
            
            h = imshow(img, win);
            set(gca, 'XTick',   tick{dirs(2)}, 'XTickLabel', ticklabel{dirs(2)});  
            set(gca, 'YTick',   tick{dirs(1)}, 'YTickLabel', ticklabel{dirs(1)});   
            xlabel([LABEL{dirs(2)} ' (' units{dirs(2)} ')'] ); 
            ylabel([LABEL{dirs(1)} ' (' units{dirs(1)} ')'] ); 
            set(gca, 'DataAspectRatio', dataAspectRatio(dirs)); 
            
            obj.showAxesPosition(gca, dir, transpose, 'color', 'green');
        end
        
        function val = calc1Dsum(obj, whichdim)
            dims = [1 2 3]; 
            otherDims = dims(~ismember(dims, whichdim)); 
            val = squeeze(sum(sum(obj.data, otherDims(1)), otherDims(2))); 
        end
        
        function [slices, val] = getSignificantSlices(obj, whichdim, threshold)
            val = obj.calc1Dsum(whichdim); 
            maxVal = max(val); 
            slices = find(val>=maxVal*threshold); 
        end
    
        function val = getCenterVoxelValue(obj, r)
            xc = obj.x_center;
            yc = obj.y_center;
            zc = obj.z_center;
            pos = [xc, yc, zc]; 
            if ~exist('r', 'var')
                r=0; 
            end
            val = obj.getValueAtPos(pos, r);
        end
        
        function val = getValueAtPos(obj, pos, r)
            if ~exist('r', 'var')
                r=0; 
            end
            if length(r)==1
                r = r*ones(3, 1); 
            end
            r = round(r);
            i = round(obj.physics2image(pos(2),'y'))+[-r(2):r(2)]; 
            j = round(obj.physics2image(pos(1),'x'))+[-r(1):r(1)]; 
            if length(pos)<=2
                k = 1; 
            else
                k = round(obj.physics2image(pos(3),'z'))+[-r(3):r(3)]; 
            end
            i = max(i, 1); i=min(i, obj.y_dim); 
            j = max(j, 1); j=min(j, obj.x_dim); 
            k = max(k, 1); k=min(k, obj.z_dim); 
            val = obj.data(i, j, k);
            val = mean(double(val(:)));
        end
        
        
        function val = getValueAtDicomCoor(obj, dicomCoor, r)
            imageCoor = obj.Dicom2ImageCoor(dicomCoor); 
            if ~exist('r', 'var')
                r=0; 
            end
            if r==0
                val       = interp3(obj.data, imageCoor(1), imageCoor(2), imageCoor(3)); 
            else
                R=round(r./[obj.x_pixdim, obj.y_pixdim, obj.z_pixdim]); 
                [I, J, K] = ndgrid(imageCoor(1)+(-R(1):R(1)), imageCoor(2)+(-R(2):R(2)), imageCoor(3)+(-R(3):R(3))); 
                val = interp3(obj.data, I, J, K);
                val(isnan(val))=[]; 
                val = mean(val(:)); 
            end
        end
        
        
        function prof = getDirProfile(obj, dir, center, t)
            for k=1:numel(dir)
                pos(k, :) = center(k)+t*dir(k); 
            end
            prof = ProfileData; 
            prof.dist = t; 
            for m=1:numel(t)
                prof.data(m) = obj.getValueAtPos(pos(:, m));
            end
        end
        
        function prof = getProfileData(obj, dir, varargin)
            options = OptionsMap(varargin{:}); 
            pos = options.getoptioni('center', coor_center(obj));
            r = options.getoptioni('radius', [0 0 0]);
            prof = ProfileData(obj.getProfile(pos, dir, r));
        end
        
        function prof = getProfile(obj, pos, dir, r)
            if ~exist('r', 'var')
                r=[0 0 0]; 
            end
            if ~exist('dir', 'var')
                dir = 'x';
            end
            if ~exist('pos', 'var') || isempty(pos)
                pos = [0 0 0]; 
            end
            
            if strcmpi(pos, 'cumulative')
                prof = getCumulativeProfile(obj, dir);
                return; 
            end
            
            dir = lower(dir); 
            prof.dist = obj.([dir 'Data']);
            N = length(prof.dist); 
            %pos = repmat(pos, [N, 1]); 
            switch lower(dir)
                case 'x'
                    n=1; 
                case 'y'
                    n=2; 
                case 'z'
                    n=3; 
            end
            
%             if length(r)>1
%                 r(n) = 0; 
%             end
            pos1 = pos; 
            for k=1:N
                pos1(n) = prof.dist(k);
                prof.data(k) = obj.getValueAtPos(pos1, r);
            end
        end
        
        function prof = getCumulativeProfile(obj, dir, varargin)
            dir = lower(dir); 
            prof.dist = obj.([dir 'Data']);
            [~, whichdim] = ismember(dir, {'y', 'x', 'z'});
            dims = [1 2 3];
            otherDims = dims(~ismember(dims, whichdim)); 
            prof.data = squeeze(sum(sum(obj.data, otherDims(1)), otherDims(2))); 
            options = OptionsMap(varargin{:}); 
            average = options.getOption('average', 1);
            if average
                dim = obj.dim(); 
                numVoxels = dim(otherDims(1))*dim(otherDims(2));
                prof.data = prof.data / numVoxels; 
            end
        end
        
        function FlipAxis(vh, dir)
            dir = lower(dir); 
            
            switch dir
                case 'x'
                    vh.x_start = vh.x_start + (vh.x_dim-1)*vh.x_pixdim; 
                    vh.x_pixdim = -vh.x_pixdim;
                    vh.data=flipdim(vh.data, 2); 
                case 'y'
                    vh.y_start = vh.y_start + (vh.y_dim-1)*vh.y_pixdim; 
                    vh.y_pixdim = -vh.y_pixdim;
                    vh.data=flipdim(vh.data, 1); 
                case 'z'
                    vh.z_start = vh.z_start + (vh.z_dim-1)*vh.z_pixdim; 
                    vh.z_pixdim = -vh.z_pixdim;
                    vh.data=flipdim(vh.data, 3); 
            end
        end
        
        function result=flipImageOrientation(obj, orientation)
            if ~exist('orientation', 'var')
                orientation = obj.imageOrientation;
            end
            [obj.data, result]=VolHeaderImage.flipVolumeDataOrientation(obj.data, orientation);
        end
        
        function flipImageData(obj, dir)
            dir=upper(dir);
            switch dir
                case 'X'
                    obj.data=flipdim(obj.data, 2); 
                case 'Y'
                    obj.data=flipdim(obj.data, 1);  
                case 'Z'
                    obj.data=flipdim(obj.data, 3);  
            end
            obj.(['reverse' dir])=~obj.(['reverse' dir]);
        end
        
        function alignImageData(obj)
            dirs = {'X', 'Y', 'Z'};
            for k=1:numel(dirs)
                dir = dirs{k}; 
                if obj.(['reverse' dir])
                    flipImageData(obj, dir);
                end
            end
        end
        
        
        function fromProfilesProduct(obj, xprof, yprof,refVal,pos)
            if ~exist('pos','var'), pos=[0 0]; end
            xp=interp1(xprof.dist,xprof.data,obj.xData,'linear','extrap');
            yp=interp1(yprof.dist,yprof.data,obj.yData,'linear','extrap');
            [XP,YP]=meshgrid(xp,yp);
            obj.data=XP.*YP;
            if exist('refVal','var')
                val=obj.getValueAtPos(pos,0);
                obj.data=obj.data/val*refVal;
            end
        end
        
        function prof = getSymmetryDiagonalProfile(obj, r, toNorm)
            %r = 0:profImage.x_pixdim:max(R(:)); 
            x = r*sqrt(2)/2; y=x; 
            for k=1:numel(x)
                prof.dist(k) = r(k); 
                sumValue =  obj.getValueAtPos([x(k), y(k)], 1) +...
                            obj.getValueAtPos([x(k), -y(k)], 1)+...
                            obj.getValueAtPos([-x(k), y(k)], 1)+...
                            obj.getValueAtPos([-x(k), -y(k)], 1); 
                prof.data(k) = sumValue/4; 
            end
            if ~exist('toNorm', 'var')
                toNorm = 0; 
            end
            
            if toNorm
                centerValue = obj.getCenterVoxelValue(3);
                prof.data = prof.data/centerValue; 
            end
        end
         
        function addnoise(obj, refVal, varargin)
            obj.data = refVal*ImageUitls.imnoise_ex(obj.data/refVal, varargin{:}); 
        end
        
        function linearMapData(obj, srcVal, dstVal)
            if ~exist('srcVal', 'var')
                srcVal = [min(obj.data(:)), max(obj.data(:))]; 
            end
            if ~exist('dstVal', 'var')
                dstVal = [0 1]; 
            end
            k = (dstVal(2)-dstVal(1))/(srcVal(2)-srcVal(1));
            
            
            obj.data = k*(obj.data-srcVal(1)) + dstVal(1); 
            
            obj.data(obj.data<dstVal(1))=dstVal(1);
            obj.data(obj.data>dstVal(2))=dstVal(2);
        end
        
        function [meanval, stdval] = zScoreNormalizeData(obj)
            [obj.data, meanval, stdval]  = obj.ZScoreNormalization(obj.data);
        end
        
        function [medianval] = zScoreLinearMapData(obj, varargin)
            [obj.data, medianval]  = obj.ZScoreLinearMap(obj.data, varargin{:});
        end

        function intensityMapData(obj, info)
            obj.data = obj.IntensityMap(obj.data, info);
        end


        function [maskimg] = MaskAugmentImage(obj, cfg)
            maskimg = StructBase.getfieldx(cfg,'MaskImage');
            fov_extension=StructBase.getfieldx(cfg,'FOVExtension'); 
            if ~isempty(maskimg)
               maskimg = VolHeaderImage(maskimg);
            elseif ~isempty(fov_extension)
                if ~isstruct(fov_extension)
                    dstsize = fov_extension;
                    offcenterratio = 0; 
                else
                    dstsize = StructBase.getfieldx(fov_extension, 'DstSize_mm');
                    offcenterratio = StructBase.getfieldx_default(fov_extension, 'offcenterratio', 0); %-0.5: front, 0: center, 0.5 back
                end
                if ischar(dstsize)
                    dstsize = str2num(dstsize);
                end
                
                overlapmargin = StructBase.getfieldx_default(fov_extension, 'Overlap_mm', [0 0 0]); 

                if strcmpi(obj.LinearMeasureUnit, 'cm')
                    dstsize=dstsize/10;
                    overlapmargin=overlapmargin/10;
                end
                startpos = [obj.x_start, obj.y_start, obj.z_start];
                dim = [obj.x_dim, obj.y_dim, obj.z_dim];
                pixdim   = [obj.x_pixdim, obj.y_pixdim, obj.z_pixdim];
                siz = dim.*pixdim;
                for k=1:3
                    if dstsize(k)>siz(k)
                        dim(k) = round(dstsize(k)/pixdim(k));
                        offset = (dstsize(k)-siz(k))*(-0.5+offcenterratio);
                        startpos(k) = startpos(k)+offset;
                    end
                end
                vh = VolHeader(VolHeader(obj)); 
                vh.x_dim=dim(1); vh.y_dim=dim(2);vh.z_dim=dim(3);
                vh.x_start=startpos(1); vh.y_start=startpos(2);vh.z_start=startpos(3);
                maskimg = VolHeaderImage(vh); maskimg.ZeroData; 
                [X, Y, Z]=maskimg.PhysicsCoorGrid();
                I =  X>=obj.x_start+overlapmargin(1) &X<obj.x_start+obj.x_dim*obj.x_pixdim-overlapmargin(1)...
                    &Y>=obj.y_start+overlapmargin(2) &Y<obj.y_start+obj.y_dim*obj.y_pixdim-overlapmargin(2)...
                    &Z>=obj.z_start+overlapmargin(3) &Z<obj.z_start+obj.z_dim*obj.z_pixdim-overlapmargin(3);
                maskimg.data(I)=1; 
            else
                %--mask_diameter_mm 200 600 600 --mask_diameter_delta_mm 50 0 0 --mask_shape Box --mask_include_mode 0
                mask_shape = StructBase.getfieldx_default(cfg, 'mask_shape', 'Box');
                mask_diameter = StructBase.getfieldx_default(cfg, 'mask_diameter_mm', []);
                mask_offset = StructBase.getfieldx_default(cfg, 'mask_offset_mm', [0 0 0]);
                maskimg = VolHeaderImage(obj); maskimg.ZeroData(); 

                if strcmpi(obj.LinearMeasureUnit, 'cm')
                    mask_diameter = mask_diameter/10; 
                    mask_offset=mask_offset/10; 
                end
                 [X, Y, Z]=maskimg.PhysicsCoorGrid();
                 X = X-mask_offset(1); Y = Y-mask_offset(2); Z = Z-mask_offset(3);
                 mask_radius = mask_diameter/2; 
                switch lower(mask_shape)
                    case 'box'
                        maskdata = X>=-mask_radius (1) & X< mask_radius(1) & Y>=-mask_radius (2) & Y< mask_radius(2) & Z>=-mask_radius (3) &Z< mask_radius(3);
                    case 'cylinder'
                        maskdata = (X/mask_radius (1)).^2+(Y/mask_radius (2)).^2 <=1 & Z>=-mask_radius (3) & Z< mask_radius(3);
                    case 'cylinder2d'
                        maskdata = (X/mask_radius (1)).^2+(Y/mask_radius (2)).^2 <=1;
                end
                maskimg.data = maskdata; 
            end
            
            mask_include_mode=StructBase.getfieldx_default(cfg, 'mask_include_mode', 0);
            if ~mask_include_mode
                maskimg.data = maskimg.data<=0; 
            end

            maskval = StructBase.getfieldx(cfg, 'augument_intensity');
            if ~isempty(maskval)
                MaskAugment(obj, maskimg, maskval);
            end
        end

        function MaskAugment(obj, mask, maskval)
            if ~exist('maskval', 'var') ||isempty(maskval)
                maskval = 0; 
            end

            if isa(mask, 'VolHeaderImage')
                origimg = VolHeaderImage(obj);
                obj.ReformCS(mask);
                mask = mask.data>0; 
            else
                origimg = obj; 
            end

            if isnumeric(maskval)
                obj.data(mask)=maskval;
            elseif ischar(maskval)
                valtype   =extractBefore(maskval, '_');
                noiselevel=extractAfter(maskval, 'noise');
                if isempty(noiselevel)
                    noiselevel =0; 
                else
                    noiselevel=str2num(noiselevel);
                end
                
                switch(valtype)
                    case 'zscoremean'
                        [~, meanval, stdval]=obj.ZScoreNormalization(origimg.data(:));
                    case 'imagemean'
                        meanval= mean(double(origimg.data(:)));
                        stdval = std(double(origimg.data(:)));
                    case 'roimean'
                        meanval = mean(origimg.data(mask(:)));
                        stdval = std(origimg.data(mask(:)));
                end 

                MaskVal = meanval+stdval*noiselevel*randn(size(obj.data));
                obj.data(mask) = MaskVal(mask);
            end
        end
        
        % function MaskReplaceIntensity(obj, img2, mask)
        %     obj.data(mask) = img2.data(mask);
        % end

        %%crop image
        function box = getIntensityThresholdBox(obj, varargin)
%             Options = HierarchyCalculator.parseOptions(varargin);
%             threshold = StructBase.getfieldx_default(Options, 'threshold', 0);
            
            options = OptionsMap(varargin{:}); 
            threshold = options.getoptioni('threshold', 0); 

            ind = find(obj.data>threshold); 
            if isempty(ind)
                box =[];
                return
            end
            siz = size(obj.data);
            [y, x, z] = ind2sub(siz, ind); 
            y=y(:); x=x(:); z=z(:);
            
            box.xIndex = [min(x), max(x)]; 
            box.yIndex = [min(y), max(y)];  
            box.zIndex = [min(z), max(z)]; 
            
            margin    = options.getoptioni('margin');
            if ~isempty(margin)
                prefix = 'xyz'; 
                for k=1:3
                    X = prefix(k); 
                    m = round(margin.(X)/obj.([X '_pixdim']));
                    box.([X 'Index'])(1) = max(1, box.([X 'Index'])(1)-m);
                    box.([X 'Index'])(2) = min(obj.([X '_dim']), box.([X 'Index'])(2)+m);
                end
            end
            box.xCoor = obj.image2physics(box.xIndex, 'x'); 
            box.yCoor = obj.image2physics(box.yIndex, 'y'); 
            box.zCoor = obj.image2physics(box.zIndex, 'z'); 
        end
        
        function [box] = cropImage_IntensityThreshold(obj, varargin)
            box = getIntensityThresholdBox(obj, varargin{:}); 
            cropImage(obj, box);
        end
        
        function zCropImage(ct, zlen, cropmode)
            zdim = ct.z_dim;
            imagCoors = ones(3, zdim);
            imagCoors(3, :)=1:zdim; 
            dcmCoors = ct.Image2DicomCoor(imagCoors);
            zcoors = dcmCoors(3, :);
            switch lower(cropmode)
                case {'top', 'superior'}
                     maxz = max(zcoors);
                     I = maxz-zcoors<=zlen;
                     Z = find(I);
                case {'bottom', 'inferior'}
                     minz = min(zcoors);
                     I = zcoors-minz<=zlen;
                     Z = find(I);
                case {'center'}
                     medz = median(zcoors);
                     I = abs(zcoors-medz)<=zlen/2;
                     Z = find(I);
            end
            S = [1 1 Z(1)];
            Dim = [ct.x_dim ct.y_dim, abs(Z(end)-Z(1))+1];
            ct.CropBoundingBox(S, Dim);
        end


        function CropImage_superior(ct, zlen)
            zdim = ct.z_dim;
            imagCoors = ones(3, zdim);
            imagCoors(3, :)=1:zdim; 
            dcmCoors = ct.Image2DicomCoor(imagCoors);
            zcoors = dcmCoors(3, :);
            maxz = max(zcoors);
            I = maxz-zcoors<zlen;
            Z = find(I);
            S = [1 1 Z(1)];
            Dim = [ct.x_dim ct.y_dim, abs(Z(end)-Z(1))+1];
            ct.CropBoundingBox(S, Dim);
        end

        %S is the start voxel, in x, y, z order
        function CropBoundingBox(obj, S, Dim)
%             bb = round(bb);
%             S  = bb(1:3); 
%             Dim = bb(4:6);
            E  = S+Dim-1; 
            startpos = obj.Image2DicomCoor(S(:));
            %newdata  = obj.data(S(2):E(2), S(1):E(1), S(3):E(3));
            newdata = zeros(Dim([2 1 3]), "like", obj.data);
            S1(1) = max(S(1), 1); S1(2) = max(S(2), 1); S1(3) = max(S(3), 1);
            E1(1) = min(E(1), obj.x_dim); E1(2) = min(E(2), obj.y_dim); E1(3) = min(E(3), obj.z_dim);
            for n=1:3
                S0(n) = S1(n)-S(n)+1;
                E0(n) = Dim(n)+E1(n)-E(n);
            end
            newdata(S0(2):E0(2), S0(1):E0(1), S0(3):E0(3)) = obj.data(S1(2):E1(2), S1(1):E1(1), S1(3):E1(3));
            
            obj.setData(newdata);
            obj.x_start = startpos(1);
            obj.y_start = startpos(2);
            obj.z_start = startpos(3);
        end
        
        function cropImage(obj, box, varargin)
            dirs = 'xyz';
            for k=1:3
                dir = dirs(k); 
                dim = obj.([dir '_dim']);
                I    = StructBase.getfieldx_default(box, [dir 'Index'], [1, dim]);
                s(k) = max(1, I(1));
                e(k) = min(dim, I(end));
                ds(k) = s(k)-I(1); 
                de(k) = I(end)-e(k); 
                %obj.([dir '_start']) = obj.([dir '_start']) + (s(k)-1)*obj.([dir '_pixdim']);
            end
            Data        = obj.data(s(2):e(2), s(1):e(1), s(3):e(3)); 
            obj.setData(Data);            
            obj.ResetStartVoxelIndex([s(1),s(2), s(3)]);
            
            options = OptionsMap(varargin{:}); 
            padzero = options.getoptioni('padzero', 1); 
            if padzero
                box1 = struct('x', [ds(1) de(1)]*obj.x_pixdim, 'y', [ds(2) de(2)]*obj.y_pixdim, 'z', [ds(3) de(3)]*obj.z_pixdim); 
                obj.padImage(box1);
            end
            
            try
            obj.TransformMatrix=[];
            catch
            end
%             obj.ImagePositionPatient = [obj.x_start, obj.y_start, obj.z_start]'; 
        end
        
        
        function truncateImage(self, region, dir)
            dir = lower(dir);
            if isstruct(region)
                center = region.center;
                length = region.length;
                slices(1) = self.physics2image(center-length/2, dir);
                slices(2) = self.physics2image(center+length/2, dir);
                
                %make even slices
                numslices = slices(2)-slices(1)+1;
                if mod(numslices, 2)==1
                    slices(2)=slices(2)+1;
                end
            else
                slices = region;
            end
            
            slices(1) = max(1, slices(1)); 
            slices(2) = min(self.([dir '_dim']), slices(2));
            box = struct([dir 'Index'], [slices(1), slices(2)]); 
            self.cropImage(box);
        end
        
        
        function padImage(obj, box, mode)
            x = StructBase.getfieldx_default(box, 'x', [0, 0]); 
            y = StructBase.getfieldx_default(box, 'y', [0, 0]); 
            z = StructBase.getfieldx_default(box, 'z', [0, 0]);
            xIndex = StructBase.getfieldx_default(box, 'xIndex', round(x/obj.x_pixdim)); 
            yIndex = StructBase.getfieldx_default(box, 'yIndex', round(y/obj.y_pixdim));
            zIndex = StructBase.getfieldx_default(box, 'zIndex', round(z/obj.z_pixdim));
%             obj.x_start = obj.x_start -xIndex(1)*obj.x_pixdim;
%             obj.y_start = obj.y_start -yIndex(1)*obj.y_pixdim;
%             obj.z_start = obj.z_start -zIndex(1)*obj.z_pixdim;
            obj.ResetStartVoxelIndex([1-xIndex(1), 1-yIndex(1), 1-zIndex(1)]);
            siz = size(obj.data); N=numel(siz); siz((N+1):3)=1; 
           
            if exist('mode', 'var')
                data = obj.data;
                data = padarray(data, [yIndex(1), xIndex(1)], mode, 'pre'); 
                data = padarray(data, [yIndex(2), xIndex(2)], mode, 'post'); 
            else
                data = zeros(siz+[sum(yIndex),sum(xIndex),sum(zIndex)]); 
                data(yIndex(1)+(1:siz(1)),xIndex(1)+(1:siz(2)), zIndex(1)+(1:siz(3))) = obj.data; 
            end
            
            obj.setData(data); 
        end
        
        function mask=boxRegion(obj, box)
            dirs = 'xyz';
            for k=1:numel(dirs)
                dir=dirs(k); 
                coor = StructBase.getfieldx_default(box, dir, [0, 0]); 
                index =   round(obj.physics2image(coor, dir));
                I.(dir)=  index(1):index(2); 
            end
            mask = zeros(size(obj.data)); 
            mask(I.y, I.x, I.z)=1; 
        end
        
        function shiftROI(obj, mask, shift, varargin)
            options = OptionsMap(varargin{:});
            dirs = 'xyz';
            for k=1:numel(dirs)
                dir=dirs(k); 
                shiftIndex.(dir) = round(StructBase.getfieldx_default(shift, dir, 0)/obj.([dir '_pixdim']));
            end
            background = options.getOption('background', 0); 
            I   = find(mask); 
            roi = obj.data(I);
            obj.data(I) = background; 
            delta = shiftIndex.z*obj.x_dim*obj.y_dim + shiftIndex.x*obj.y_dim+shiftIndex.y; 
            obj.data(I+delta)=roi; 
        end
        
        function shiftBoxRegion(obj, box, shift, varargin)
            mask = obj.boxRegion(box); 
            obj.shiftROI(mask, shift, varargin{:}); 
        end
        
        
        function conv2D(obj, kernelData)
            if isa(kernelData, 'utils.FFTConvn')
%                  for k=1:obj.z_dim
%                      obj.data(:, :, k)     = kernelData.Calculate_real(obj.data(:, :, k));
%                  end
                tempdata = gpuArray(obj.data); 
                for k=1:obj.z_dim
                     tempdata(:, :, k)     = kernelData.Calculate_real(tempdata(:, :, k));
                end
                obj.data = gather(tempdata); 
            else
                for k=1:obj.z_dim
                     obj.data(:, :, k)     = convn(obj.data(:, :, k), kernelData, 'same');
    %                 obj.data(:, :, k)    = convnfft(obj.data(:, :, k), kernelData, 'same');
    %                   obj.data(:, :, k)    = ImageUtils.convnfft_faster(obj.data(:, :, k), kernelData);  
                end
            end
        end
        
        function dstData = qinterp3(obj, X, Y, Z, varargin)
            [Xi] = obj.physics2image(X, 'x'); 
            [Yi] = obj.physics2image(Y, 'y');
            [Zi] = obj.physics2image(Z, 'z');
            dstData = ba_interp3(obj.data,...
                                Xi, ...
                                Yi,...
                                Zi,...                                
                                'linear'); 
           options  = OptionsMap(varargin{:}); 
           extrapVal= options.getOption('ExtrapolationValue'); 
           if ~isempty(extrapVal)
               I = Xi<1 | Xi >obj.x_dim | Yi<1 | Yi >obj.y_dim | Zi<1 | Zi >obj.z_dim; 
               dstData(I) = extrapVal; 
           end
        end
        
        
        function permuteDim(obj, order)
            perfix='yxz'; 
            header = obj.header; 

            for k=1:3
                p = perfix(k);  p1 = perfix(order(k)); 
                obj.([p '_dim'])   = header.([p1 '_dim']);
                obj.([p '_pixdim']) = header.([p1 '_pixdim']);
                obj.([p '_start']) = header.([p1 '_start']);
            end
            
            if ~isempty(obj.data)
                obj.setData(permute(obj.data, order)); 
            end
        end
        
        function [c, index] = CenterOfMass(self, isdicom)
            if ~exist('isdicom', 'var') || isempty(isdicom)
                isdicom = 0; 
            end
            
            index = self.centerOfMassIndex(self.data); 
            if numel(index)==2
                index(3)=1;
            end
            if ~isempty(index)
                index([2 1]) = index([1 2]); 
                if ~isdicom
                    c     = self.image2physicsCoor(index);
                else
                    c     = self.Image2DicomCoor(index(:));
                    c     = c'; 
                end
            else
                c = [NaN NaN NaN]; 
            end
        end
        
        function immorph_2D(obj, operation, se, dir)
            if ~exist('se', 'var')
                se = [];
            end
            if ~exist('dir', 'var')
                dir = 'z';
            end
            numslices = obj.([dir '_dim']); 
            %sliceimg = getSliceImage(obj, 1, dir);
            for k=1:numslices
                sliceimg = getSliceImage(obj, k, dir);
                sliceimg.immorph(operation, se);
                dstdata(:, :, k) = sliceimg.data; 
            end
            switch dir
                case 'x'
                    dstdata = permute(dstdata, [1 3 2]); 
                case 'y'
                    dstdata = permute(dstdata, [3 2 1]); 
            end
            obj.setData(dstdata); 
        end
        
        function immorph(obj, operation, se, varargin)
            if ~exist('se', 'var')
                se = [];
            end
            if isnumeric(se) &&~isempty(se)
                if obj.z_dim>1
                    se = strel('sphere', se);
                else
                    se = strel('disk', se);
                end
            end
            switch lower(operation)
                case {'dilate'}
                        obj.data = imdilate(obj.data, se); 
                case {'erode'}
                        obj.data = imerode(obj.data, se);
                case {'open'}
                        obj.data = imopen(obj.data, se); 
                case {'close'}
                        obj.data = imclose(obj.data, se);
                case {'thin'}
                        obj.data = bwmorph(obj.data, 'thin', Inf);
                case {'fillhole', 'imfill'}
                     obj.data = imfill(obj.data, 'holes');
                case {'convexhull'}
                    if obj.z_dim>1
                    %s = regionprops3(obj.data,"BoundingBox","ConvexImage");
                    CC = struct('Connectivity', 26, 'ImageSize', size(obj.data), 'NumObjects', 1); 
                    CC.PixelIdxList{1} = find(obj.data>0); 
                    s = regionprops3(CC,"BoundingBox","ConvexImage");
                    if ~isempty(s)
                        bb = floor(s.BoundingBox(1, :));
                        I = bb(2)+[1:bb(5)]; J = bb(1)+[1:bb(4)]; K=bb(3)+[1:bb(6)];
                        obj.data(I,J ,K)=s.ConvexImage{1};
                    end
                    else
                        CC = struct('Connectivity', 8, 'ImageSize', size(obj.data), 'NumObjects', 1); 
                        CC.PixelIdxList{1} = find(obj.data>0); 
                        s = regionprops(CC,"BoundingBox","ConvexImage"); 
                        if ~isempty(s)
                            bb = floor(s.BoundingBox);
                            I = bb(2)+[1:bb(4)]; J = bb(1)+[1:bb(3)]; 
                            obj.data(I,J)=s.ConvexImage;
                        end
                    end
                case {'regionfill'}
                    options = OptionsMap(varargin{:});
                    mask = options.getOption('mask'); 
                    zdim = obj.z_dim; 
                    for k=1:zdim
                        maskdata = mask(:, :, k); 
                        if any(maskdata(:))
                            obj.data(:, :, k) = regionfill(obj.data(:, :, k), maskdata); 
                        end
                    end
             end
        end
      
        function imsmooth(obj, se)
            obj.data = imgaussfilt(obj.data, se);
        end
        
        function [gammaimag, gammapass] = gammaindex(obj, testimg, varargin)
            options = OptionsMap(varargin{:}); 
            prescription = options.getoptioni_numeric('NormValue', max(obj.data(:)));
            dd_cri  = options.getoptioni_numeric('dd_cri', 3);
            dta_cri = options.getoptioni_numeric('dta_cri', 3);
            issigned= options.getoptioni_numeric('issigned', 0);
            gammaimag= CalcGammaIndex(obj, testimg, prescription, dd_cri, dta_cri, issigned); 
            if nargout>=2
                dosethreshold=options.getoptioni_numeric('gammapass.dosethreshold', 10)/100; 
                VOI = obj.data>dosethreshold;
                gammaimag.data(~VOI)=0;
                gammadata = gammaimag.data(VOI); 
                gammapass = sum(abs(gammadata)<1) / numel(gammadata)*100; 
            end
        end
    end
    
    methods (Static)
        function res = DefaultInterpMethod()
            res = 'linear'; 
        end
        
        function flag=existImage(fname)
            flag = exist([fname '.header'], 'file') && (exist([fname '.img'], 'file') ||exist([fname '.sin'], 'file'));
        end
        
        function jawImage = constructImage1D(pixdim, halfWidth, penumbra)
            if isa(penumbra,'VolHeaderImage')
                dist = penumbra.xData;
            else
                dist = penumbra.dist; 
            end
            
            K = round(halfWidth/pixdim);
            zi = (-K:K)*pixdim;
            jawData =interp1(dist,penumbra.data,zi, 'linear', 'extrap'); 
            jawHeader.x_dim = length(jawData); 
            jawHeader.x_pixdim = pixdim; 
            jawHeader.x_start = zi(1); 
            jawImage = VolHeaderImage(jawHeader, jawData); 
        end
        
        function createMeanImage(src, dstFile)
            K = length(src);
            for k=1:K
                if ischar(src{k})
                    src{k} = VolHeaderImage(src{k}); 
                end
            end
            for k=2:K
                src{1}.data = src{1}.data+src{k}.data; 
            end
            src{1}.data=src{1}.data/K; 
            src{1}.writeFile(dstFile); 
        end
                
        
         function fname = getImageDataFile(hdrfname, dataFolder)
            [PATHSTR,NAME,EXT] = fileparts(hdrfname); 
            if ~isempty(EXT) && ~VolHeader.isMemberString(EXT, VolHeaderImage.imageExts) && ~VolHeader.isMemberString(EXT, VolHeaderImage.DefaultImageHeaderExts)
                    NAME = [NAME EXT]; 
            end
            if ~isempty(PATHSTR)
                PATHSTR = [PATHSTR filesep]; 
            end
            %fname = []; 
            fname = VolHeaderImage.getImageDataFileName(NAME, PATHSTR); 
            if ~isempty(fname) return; end
            header = VolHeader(hdrfname); 
            fname = VolHeaderImage.getImageDataFileName(header.uid, dataFolder); 
         end
        
         function fname = getImageDataFileName(name, dataFolder, imgExts)
             fname = []; 
             if nargin <2
                 dataFolder = []; 
             end
             fname0 = [dataFolder name]; 
             if nargin <3
                imgExts = VolHeaderImage.imageExts; 
             end
             for k=1:length(imgExts)
                    ext = imgExts{k}; 
                    if exist([fname0 ext], 'file')
                        fname = [fname0 ext];
                        return; 
                    end
             end
         end
         
         function lH = compareProfiles(imags, scaleFactor, varargin)
             if ~exist('scaleFactor', 'var')
                 scaleFactor=[]; 
             end
             if isempty(scaleFactor)
                 scaleFactor=ones(size(imags)); 
             end
             
             for k=1:length(imags)
                 if ischar(imags{k})
                     imags{k} = VolHeaderImage(imags{k}); 
                 end
                 if isa(imags{k}, 'VolHeaderImage') 
                     prof=imags{k}.getProfile(varargin{:});
                     dist{k} = prof.dist; 
                     data{k} = prof.data*scaleFactor(k); 
                 else
                     dist{k}=[]; data{k}=[]; 
                 end
                 legendStr{k} = num2str(k); 
             end
             lH = gplot(dist, data, [], legendStr); 
         end
         
         function lH= compareProfilesEx(imags0, imags1, varargin)
             colors={'r','g','b', 'm','k', 'y', 'c'};
             styles = {'-', '--'}; 
             N = numel(imags0);
             for k=1:N
                 imags{2*k-1}   = imags0{k}; 
                 imags{2*k}     = imags1{k}; 
             end
             lH = VolHeaderImage.compareProfiles(imags, [], varargin{:});
             for k=1:N
                 m=mod(k-1, numel(colors))+1; 
                 set(lH(2*k-1), 'LineStyle', styles{1}, 'Color', colors{m}); 
                 set(lH(2*k), 'LineStyle', styles{2}, 'Color', colors{m}); 
             end
             legend('ref', 'test'); 
         end
         
         
         function [profImage, prof] = constructSymmetryProfImage(srcImage, dstHeader)        
            %dstHeader= VolHeader(srcImage); 
            profImage = VolHeaderImage(dstHeader);
            
            if isa(srcImage, 'VolHeaderImage')
                srcImage.alignXYCenter;    
                [X, Y] = meshgrid( profImage.xData,  profImage.yData);
                R = sqrt(X.^2+Y.^2);
                r = 0:profImage.x_pixdim:max(R(:)); 
                prof = srcImage.getSymmetryDiagonalProfile(r, 1);
            else
                prof = srcImage; 
            end

            [X, Y] = meshgrid( profImage.xData,  profImage.yData); 
            R = sqrt(X.^2+Y.^2);
            profImage.data = interp1(prof.dist, prof.data, R, 'linear', 'extrap'); 
         end
        
         function [profImage] = constructXYProfImage(profx, profy, varargin)
             options = OptionsMap(varargin{:}); 
             header = options.getOption('templateImage', PortalImage.createTemplateHeader); 
              profImage = VolHeaderImage(header);
              profx = ProfileData(profx); profx.data = profx.data/profx.getValue(0); 
              profy = ProfileData(profy); profy.data = profy.data/profy.getValue(0);
              [X, Y] = meshgrid( profImage.xData,  profImage.yData);
%               if 0 
                  profImage.data = profx.getValue(X).*profy.getValue(Y);
%               else
%                   a  =  abs(X)./(abs(X)+abs(Y)); 
%                   profImage.data = a .* profx.getValue(X) +  (1-a).*profy.getValue(Y); 
%                   profImage.data(isnan(profImage.data))=1; 
%               end
         end
         
         function flag = toCalcImageFile(obj, fname, tag)
            if ~exist('tag', 'var')
                tag = 'ReCalculate';
            end
            flag  = ~VolHeaderImage.existImage(fname);
            recalc= StructBase.getfieldx(obj, tag);
            if ~isempty(recalc)
                flag = flag || recalc; 
            end
         end
        
         
         function [data, result]=flipVolumeDataOrientation(data, orientation)
            if ~exist('orientation', 'var')
                orientation = [];
            end
            if StructBase.getfieldx_default(orientation, 'x', 1)==-1
                data=flipdim(data, 2); 
                result.x=1; 
            end
            if StructBase.getfieldx_default(orientation, 'y', 1)==-1
                data=flipdim(data, 1); 
                result.y=1; 
            end
            if StructBase.getfieldx_default(orientation, 'z', 1)==-1
                data=flipdim(data, 3); 
                result.z=1; 
            end
         end
        
         
         function C = centerOfMassIndex(A)
            A = double(A);
            sz = size(A);
            nd = ndims(A);
            M = sum(A(:));
            C = zeros(1,nd);
            if M==0
                C = [];
            else
                for ii = 1:nd
                    shp = ones(1,nd);
                    shp(ii) = sz(ii);
                    rep = sz;
                    rep(ii) = 1;
                    ind = repmat(reshape(1:sz(ii),shp),rep);
                    C(ii) = sum(ind(:).*A(:))./M;
                end
            end
         end
        
         function [data, meanval, stdval] = ZScoreNormalization(data)
             data = double(data);
             mask    = data>mean(data(:));
             meanval = mean(data(mask(:))); 
             stdval  = std(data(mask(:)));
             data    = (data-meanval)./stdval; 
         end
         
         function [data, srcmedian] = ZScoreLinearMap(data, info)
             if ~exist('info', 'var')
                 info = [];
             end
             srcval = StructBase.getfieldx_default(info, 'SrcIntensity', 0);
             dstval = StructBase.getfieldx_default(info, 'DstIntensity', [0, 1000]);
             data   = double(data);
             mask   = data>mean(data(:));
             srcmedian = median(data(mask(:))); 
             k = (dstval(2)-dstval(1))/(srcmedian-srcval(1));
             data = k*(data-srcval(1)) + dstval(1); 
             % cliplow = StructBase.getfieldx_default(info, 'ClipLow', true);
             % if cliplow
             %    data(data<dstval(1))=dstval(1);
             % end
             thresholdlow = StructBase.getfieldx_default(info, 'ThresholdLow', dstval(1));
             if ~isempty(thresholdlow)
                data(data<thresholdlow)=dstval(1);
             end
         end

         function [data] = LinearMap(data, info)
             if ~exist('info', 'var')
                 info = [];
             end
             srcval = StructBase.getfieldx_default(info, 'SrcIntensity', []);
             dstval = StructBase.getfieldx_default(info, 'DstIntensity', []);
             data = double(data);
             k = (dstval(2)-dstval(1))/(srcval(2)-srcval(1));
             data = k*(data-srcval(1)) + dstval(1);
             cliplow = StructBase.getfieldx_default(info, 'ClipLow', true);
             if cliplow
                data(data<dstval(1))=dstval(1);
             end

             cliphigh = StructBase.getfieldx_default(info, 'ClipHigh', true);
             if cliphigh
                data(data>dstval(2))=dstval(2);
             end
         end

         
         function data = IntensityMap(data, info)
            if ischar(info)
                maptype = info;
            else
                maptype = StructBase.getfieldx_default(info, 'MapType', '');
            end
            switch lower(maptype)
                case 'linear'
                     Scale = StructBase.getfieldx_default(info, 'IntensityScale', 1);
                     Offset = StructBase.getfieldx_default(info, 'IntensityOffset', 0);   
                     data = double(data)*Scale+Offset;
                case 'linearmap'
                     [data] = VolHeaderImage.LinearMap(data, info);
                case 'zscorenorm'
                     [data] = VolHeaderImage.ZScoreNormalization(data, info);
                case 'zscorelinearmap'
                     [data] = VolHeaderImage.ZScoreLinearMap(data, info);
                case 'cleanbackground'
                     bgthreshold = StructBase.getfieldx_default(info, 'BackgroundThreshold', 50);
                     bgintensity = StructBase.getfieldx_default(info, 'BackgroundIntensity', 0);
                     mrmask = imfill(data>bgthreshold, 'holes');
                     data(~mrmask)=bgintensity;
                case 'cleanbg_2d'
                     bgthreshold = StructBase.getfieldx_default(info, 'BackgroundThreshold', 50);
                     bgintensity = StructBase.getfieldx_default(info, 'BackgroundIntensity', 0);
                     K = size(data, 3);
                     for k=1:K
                        mrmask(:, :, k) = imfill(data(:, :, k)>bgthreshold, 'holes');
                     end
                     data(~mrmask)=bgintensity;
            end
         end

         function img = SafeLoad(fname, pausetime)
            try 
                img = VolHeaderImage(fname);
            catch
                if ~exist('pausetime', 'var')
                    pausetime=10; 
                end
                pause(pausetime); 
                img = VolHeaderImage(fname);
            end
         end

         function [img1, img2] = zAlignCropImages(img1, img2, alignmode, margin)
            zlen1 = img1.z_dim*img1.z_pixdim; 
            zlen2 = img2.z_dim*img2.z_pixdim; 
            if abs(zlen1-zlen2)<margin
                return
            end
            if zlen1<zlen2
                img2= VolHeaderImage(img2);
                img = img2; 
                zlen=zlen1+margin; 
            else
                img1= VolHeaderImage(img1);
                img = img1; 
                zlen=zlen2+margin; 
            end
            img.zCropImage(zlen, alignmode);
         end

        
    end
end

