# MATLAB Medical Imaging Package Analysis

This is a comprehensive MATLAB package for **medical imaging and radiation therapy treatment planning**, specifically designed for handling various types of medical images and radiation therapy data.

## Core Architecture

The package follows an object-oriented design with a clear inheritance hierarchy:

```matlab
classdef VolHeader < HandleStructBase
    properties
       imageType
       data_type='float'
       x_dim=1; y_dim=1; z_dim=1
       x_pixdim=1; y_pixdim=1; z_pixdim=1
       x_start=0; y_start=0; z_start=0
       FrameOfReferenceUID
       ImageOrientationPatient
       LinearMeasureUnit = 'cm'
```

## Main Components

### 1. Base Classes
- **`VolHeader`**: Core class defining 3D volume geometry, coordinate systems, and DICOM metadata
- **`VolHeaderImage`**: Extends VolHeader to include actual image data and I/O operations
- **`HandleStructBase`**: Base class providing common functionality

### 2. Medical Image Types
- **`CTImage`**: CT scan handling with Hounsfield units and density conversion
- **`DVFImage`**: Deformation vector fields for image registration
- **`ROIMaskImage`**: Region of Interest masks for treatment planning
- **`OptMaskImage`**: Optimization masks for treatment planning
- **`PortalImage`**: Portal/verification images from linear accelerators
- **`IntensityMapImage`**: Intensity modulated radiation therapy (IMRT) fluence maps

### 3. Phantom and Geometry Classes
- **`PhantomImage`**: Virtual phantoms for testing and calibration
- **`CTPhantom`**: CT-specific phantom creation with material properties
- **`EllipsoidPhantom`**: Geometric phantom shapes
- **`SimpleGeometry`**: Basic geometric operations

### 4. Radiation Therapy Components
- **`RTBeamControlPoint`**: Radiation therapy beam parameters
- **`RTBeamDescription`**: Beam configuration and setup
- **`BEVCoordinate`**: Beam's eye view coordinate transformations

## Key Features

### Medical Imaging Capabilities
- **Multi-format Support**: DICOM, MHD/MHA, Pinnacle, NRRD formats
- **3D Volume Processing**: Complete 3D image manipulation and analysis
- **Coordinate System Management**: Handles patient, DICOM, and beam coordinate systems
- **Image Registration**: Deformation field support for image alignment

### Radiation Therapy Specific
- **Treatment Planning**: ROI definition, optimization masks, dose calculations
- **Beam Modeling**: Multi-leaf collimator (MLC) modeling, jaw positions
- **Portal Imaging**: Verification image processing
- **IMRT Support**: Intensity modulated radiation therapy planning

### Package Organization
- **`+ImageUtils/`**: Image processing utilities (convolution, interpolation, noise)
- **`+ROIMaskImage/`**: ROI manipulation functions (merging, conversion, statistics)
- **`+VolHeaderImage/`**: Volume loading/saving functions for various formats
- **`+VolHeader/`**: Header manipulation utilities

## Notable Functionality

### File I/O Support
```matlab
function [pixels, imageObj] = ximread(filename, normalize)
%XIMREAD Read a xim projection file.
%   This function uses the Image Acquisition Library (IAL)
```

### Material Properties
```matlab
function tt = MatTable()
    material = {'water', 'muscle', 'bone', 'lung', 'adipose', 'cork', 'air', 'halfwater'}'; 
    ctnum    = [1000 1024 2658 266 920 298 1 500]';
    density  = [1 1.024 1.5 .266 .920 .298 .001 .500]';
```

## Use Cases

This package is designed for:
1. **Medical Physics Research**: Radiation therapy treatment planning and analysis
2. **Image Processing**: 3D medical image manipulation and analysis
3. **Quality Assurance**: Phantom-based testing and verification
4. **Treatment Planning Systems**: Integration with clinical TPS software
5. **Research Applications**: Custom algorithm development for radiation therapy

## Dependencies
- External DLL: `VMS.IAL.Core.Image.dll` for specialized image formats
- MATLAB Image Processing Toolbox (implied)
- Custom utilities for DICOM and medical image format handling

## File Structure

### Main Classes
- `VolHeader.m` - Base volume header class
- `VolHeaderImage.m` - Volume with image data
- `CTImage.m` - CT image handling
- `ROIMaskImage.m` - ROI mask operations
- `PhantomImage.m` - Phantom creation
- `IntensityMapImage.m` - IMRT fluence maps
- `PortalImage.m` - Portal image processing
- `DVFImage.m` - Deformation vector fields

### Utility Packages
- `+ImageUtils/` - Image processing utilities
- `+ROIMaskImage/` - ROI manipulation functions
- `+VolHeaderImage/` - Volume I/O functions
- `+VolHeader/` - Header utilities
- `+imio/` - Image I/O functions

### Geometry and RT Classes
- `SimpleGeometry.m` - Basic geometric operations
- `RTBeamControlPoint.m` - Beam control parameters
- `RTBeamDescription.m` - Beam configuration
- `BEVCoordinate.m` - Beam's eye view coordinates

This is a sophisticated, production-level package for medical imaging and radiation therapy applications, with extensive functionality for handling the complex requirements of clinical medical physics workflows.
