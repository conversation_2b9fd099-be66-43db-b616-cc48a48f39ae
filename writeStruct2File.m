function  writeStruct2File(fname, obj, recursive )
%UNTITLED20 Summary of this function goes here
%   Detailed explanation goes here
if nargin <3
    recursive = 0;
end
%fid = fopen(fname, 'wt'); 
fid = fopen_force(fname, 'wt'); 
writeStruct(fid, obj, recursive); 
fclose(fid); 
end

function fid = fopen_force(fname, varargin)
    fid = fopen(fname, varargin{:});
    if fid<0
        folder = fileparts(fname); 
        if ~exist(folder, 'dir')
            mkdir(folder);
            fid = fopen(fname, varargin{:});
        end
    end
end

function    writeStruct( fid, obj, recursive)
%UNTITLED18 Summary of this function goes here
%   Detailed explanation goes here
    if nargin <3 
        recursive = 0;
    end
    names = fieldnames(obj);
    for k=1:length(names)
        name = names{k};
        %val = getfield(obj, name);
        val = obj.(name); 
        if isa(val, 'logical')
            val = double(val); 
        end
        if isSimpleField(val)
            writeSimpleField(fid, name, val); 
        elseif (isstruct(val)|| (iscell(val)&&isstruct(val{1}))) && recursive
            for n=1:length(val)
                
                if iscell(val)
                    val1 = val{n};
                else
                    val1 = val(n);
                end
                
                if isstruct(val1)
                    fprintf(fid, [name '={\n']); 
                    writeStruct(fid, val1, recursive);
                    fprintf(fid, '}\n');
                else
                    writeSimpleField(fid, name, val1)
                end
            end
        elseif iscell(val)&&ischar(val{1})
            for n=1:length(val)
                 writeSimpleField(fid, name, val{n})
            end
        else
            %disp(['unrecognized filed: ', name]); 
        end
    end
end

function flag = isSimpleField(val)
    flag = isempty(val)||ischar(val) || isnumeric(val) || islogical(val);
end
        
function writeSimpleField(fid, name, val)
    if isempty(val)
            fprintf(fid,  [name ' =\n']);
    elseif ischar(val)
        N = size(val, 1); 
        if N==1
            fprintf(fid, [name ' = %s\n'], val);
        else
            fprintf(fid, [name ' = \n']);
            fprintf(fid, '{\n');
            for n=1:N
                fprintf(fid, '%s\n', val(n, :)); 
            end
            fprintf(fid, '}\n');
        end
    elseif isnumeric(val)
        formatStr = getFormatStr(val); 
        if length(val)==1
            if round(val)==val
                fprintf(fid,  [name ' = ' formatStr '\n'], val);
            else
                fprintf(fid,  [name ' = ' formatStr '\n'], val);
            end 
        elseif isvector(val) && length(val)<10
            if all(round(val)==val)
                formatStr = '%d';
            else
                formatStr = '%e'; 
            end
            
            fprintf(fid,  [name ' = [' formatStr ], val(1));
            for k=2:length(val)
                fprintf(fid,  ['     ' formatStr ], val(k));
            end
            fprintf(fid, ']\n'); 
        else
            fprintf(fid, '%s = \n', name); 
            fprintf(fid, '{\n'); 
            S = size(val); 
            if all(val==floor(val))
                form = '%d,'; 
            else
                %form = '%12.8f,';
                form = '%e,';
            end
            formatStr = form; 
            for k = 2:S(2)
                formatStr = [formatStr ' ' form]; 
            end
            formatStr = [formatStr '\n']; 
            fprintf(fid, formatStr, val');  
            fprintf(fid, '};\n\n'); 
        end
   
    end
end

function formatStr = getFormatStr(val)
    if all(round(val)==val)
        formatStr = '%d';
    else
        formatStr = '%e'; 
    end
end
