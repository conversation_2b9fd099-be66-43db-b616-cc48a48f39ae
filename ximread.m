function [pixels, imageObj] = ximread(filename, normalize)
%XIMREAD Read a xim projection file.
%   [PIXELS, IMAGEOBJ] = XIMREAD(FILENAME) reads the projection from the
%   specified xim-file. The pixel values are returned in a two-dimensional
%   array, the type of the array is either uint8, uint16, or uint32 depending on
%   the file type. The image is returned also as an object (IMAGEOBJ). This
%   object contains all the meta-information contained in the xim-file as
%   properties. The object can also be modified and saved back to disk, using
%   the Save(filename) method.
%   If no FILENAME is specified (or if it is logical false), a File-Open-Dialog 
%   is opened and the user can select a projection.
%
%   [PIXELS, IMAGEOBJ] = XIMREAD(FILENAME, NORMALIZE) reads the projection from
%   the specified xim-file. If the NORMALIZE-flag is TRUE, the pixel values are
%   divided by the norm chamber value of the projection and returned as a
%   double-array.
%
%   This function uses the Image Acquisition Library (IAL), which is expected to
%   be in the directory for external libraries:
%   "Matlab installation directory"\extern\lib\win64\IAL_*******\VMS.IAL.Core.Image.dll
%   or in the relative path
%   ..\..\..\..\..\Image Acquisition Library (IAL)\builds\Core\*******\VMS.IAL.Core.Image.dll
%   (according to the file structure in the source control TFS)
%   or in the relative path
%   .\VMS.IAL.Core.Image.dll
%   (i.e. the same directory as this script)

  if nargin < 1, filename  = false; end;
  if nargin < 2, normalize = false; end;

  if ~filename
    [filename, pathname] = uigetfile( ...
      {'*.xim;*.hnd;*.hnc','Varian Projections (*.xim,*.hnd,*.hnc)';
       '*.xim', 'XIM projections (*.xim)'; ...
       '*.hnd', 'HND projections (*.hnd)'; ...
       '*.hnc', 'HNC projections (*.hnc)'; ...
       '*.*',   'All Files (*.*)'}, ...
      'Select a projection');
  
    if ~filename
      [pixels, imageObj] = abort();
      return;
    end
    
    filename = [pathname, filename];
  end
  
  % Load the IAL-dll
  ialPath = [matlabroot filesep 'extern' filesep 'lib' filesep 'win64' filesep 'IAL_*******' filesep 'VMS.IAL.Core.Image.dll'];
  if ~exist(ialPath, 'file')
    ialPath = [fileparts(fileparts(fileparts(fileparts(fileparts(fileparts(mfilename('fullpath'))))))) filesep ...
              'Image Acquisition Library (IAL)' filesep 'builds' filesep 'Core' filesep '*******' filesep 'VMS.IAL.Core.Image.dll'];
    if ~exist(ialPath, 'file')
      ialPath = [fileparts(mfilename('fullpath')) filesep 'VMS.IAL.Core.Image.dll'];
      if ~exist(ialPath, 'file')
        error('Could not find the Image Acquisition Library (IAL) VMS.IAL.Core.Image.dll');
      end
    end
  end
  NET.addAssembly(ialPath);

  % Create and load the projection
  imageObj = VMS.IAL.Core.Image.VImage();
  try
    imageObj.Load(filename);

    % Convert the pixels, given in a byte-array, to the appropriate number type
    switch imageObj.AllocatedBytesPerPixel
      case 1; pixels = uint8(imageObj.Data);
      case 2; pixels = typecast(uint8(imageObj.Data), 'uint16');
      case 4; pixels = typecast(uint8(imageObj.Data), 'uint32');
      otherwise; error('image format is not supported');
    end

    % reformat the one-dimensional pixel-array to a two-dimensional projection
    pixels = reshape(pixels, imageObj.Width, imageObj.Height)';

    % normalize the pixels the pixel values, if the corresponding input
    % parameter is passed in and is true
    if (nargin >= 2) && normalize
      if imageObj.KVBeam
        pixels = double(pixels) / double(imageObj.KVNormChamber);
      else
        error('Unable to normalize non-kV projections');
      end
    end
  catch exception
    delete(imageObj);
    rethrow(exception);
  end
  
  % Delete the image object, if it is not requested by the user
  if nargout < 2
    delete(imageObj);
  end
end


function [pixels, imageObj] = abort()
  pixels = false;
  imageObj = false;
end
